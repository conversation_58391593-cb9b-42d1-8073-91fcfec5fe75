"use client"

import { AnimatePresence, motion } from "framer-motion"
import { MessageSquare, X } from "lucide-react"
import { useEffect, useState } from "react"

interface SupportItem {
  id: string
  name: string
  icon: React.ReactNode
  href: string
  color: string
  hoverColor: string
}

// Custom Messenger Icon
const MessengerIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2C6.477 2 2 6.145 2 11.25c0 2.9 1.4 5.5 3.6 7.2V22l3.5-1.9c.9.2 1.9.4 2.9.4 5.523 0 10-4.145 10-9.25S17.523 2 12 2zm1.1 12.4l-2.5-2.7-4.9 2.7 5.4-5.7 2.6 2.7 4.8-2.7-5.4 5.7z" />
  </svg>
)

// Custom Zalo Icon
const ZaloIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm4.5 7.5l-3 3c-.3.3-.7.3-1 0l-1.5-1.5-3 3c-.3.3-.7.3-1 0s-.3-.7 0-1l3.5-3.5c.3-.3.7-.3 1 0l1.5 1.5 2.5-2.5c.3-.3.7-.3 1 0s.3.7 0 1z" />
  </svg>
)

const supportItems: SupportItem[] = [
  {
    id: "messenger",
    name: "Messenger",
    icon: <MessengerIcon />,
    href: "https://m.me/efruit.vn",
    color: "bg-blue-500",
    hoverColor: "hover:bg-blue-600",
  },
  {
    id: "zalo",
    name: "Zalo",
    icon: <ZaloIcon />,
    href: "https://zalo.me/1601463040477310118",
    color: "bg-blue-600",
    hoverColor: "hover:bg-blue-700",
  },
]

export default function FloatingSupport() {
  const [isVisible, setIsVisible] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.scrollY > 200) {
        setIsVisible(true)
      } else {
        setIsVisible(false)
      }
    }

    window.addEventListener("scroll", toggleVisibility)
    return () => window.removeEventListener("scroll", toggleVisibility)
  }, [])

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded)
  }

  const handleItemClick = (href: string) => {
    window.open(href, "_blank", "noopener,noreferrer")
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          className="fixed bottom-6 right-6 z-50 flex flex-col items-end space-y-3"
        >
          {/* Support Items */}
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.2 }}
                className="flex flex-col space-y-2"
              >
                {supportItems.map((item, index) => (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center space-x-2"
                  >
                    {/* Label */}
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="shadow-lg rounded-lg border bg-white px-3 py-2"
                    >
                      <span className="text-sm font-medium text-gray-700">
                        {item.name}
                      </span>
                    </motion.div>

                    {/* Icon Button */}
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handleItemClick(item.href)}
                      className={`shadow-lg flex h-12 w-12 items-center justify-center rounded-full text-white transition-all duration-200 ${item.color} ${item.hoverColor} `}
                    >
                      {item.icon}
                    </motion.button>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main Toggle Button */}
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            onClick={toggleExpanded}
            className={`shadow-lg flex h-14 w-14 items-center justify-center rounded-full transition-all duration-300 ${
              isExpanded
                ? "bg-red-500 hover:bg-red-600 rotate-180"
                : "bg-primary-main hover:bg-primary-main/80"
            } text-white`}
          >
            <motion.div
              animate={{ rotate: isExpanded ? 45 : 0 }}
              transition={{ duration: 0.2 }}
            >
              {isExpanded ? <X size={24} /> : <MessageSquare size={24} />}
            </motion.div>
          </motion.button>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
