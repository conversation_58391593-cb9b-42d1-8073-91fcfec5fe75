{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/ui/floating-support.tsx"], "sourcesContent": ["\"use client\"\n\nimport { AnimatePresence, motion } from \"framer-motion\"\nimport { MessageSquare, X } from \"lucide-react\"\nimport { useEffect, useState } from \"react\"\n\ninterface SupportItem {\n  id: string\n  name: string\n  icon: React.ReactNode\n  href: string\n  color: string\n  hoverColor: string\n}\n\n// Custom Messenger Icon\nconst MessengerIcon = () => (\n  <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n    <path d=\"M12 2C6.477 2 2 6.145 2 11.25c0 2.9 1.4 5.5 3.6 7.2V22l3.5-1.9c.9.2 1.9.4 2.9.4 5.523 0 10-4.145 10-9.25S17.523 2 12 2zm1.1 12.4l-2.5-2.7-4.9 2.7 5.4-5.7 2.6 2.7 4.8-2.7-5.4 5.7z\" />\n  </svg>\n)\n\n// Custom Zalo Icon\nconst ZaloIcon = () => (\n  <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n    <path d=\"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm4.5 7.5l-3 3c-.3.3-.7.3-1 0l-1.5-1.5-3 3c-.3.3-.7.3-1 0s-.3-.7 0-1l3.5-3.5c.3-.3.7-.3 1 0l1.5 1.5 2.5-2.5c.3-.3.7-.3 1 0s.3.7 0 1z\" />\n  </svg>\n)\n\nconst supportItems: SupportItem[] = [\n  {\n    id: \"messenger\",\n    name: \"Messenger\",\n    icon: <MessengerIcon />,\n    href: \"https://m.me/efruit.vn\",\n    color: \"bg-blue-500\",\n    hoverColor: \"hover:bg-blue-600\",\n  },\n  {\n    id: \"zalo\",\n    name: \"Zalo\",\n    icon: <ZaloIcon />,\n    href: \"https://zalo.me/1601463040477310118\",\n    color: \"bg-blue-600\",\n    hoverColor: \"hover:bg-blue-700\",\n  },\n]\n\nexport default function FloatingSupport() {\n  const [isVisible, setIsVisible] = useState(false)\n  const [isExpanded, setIsExpanded] = useState(false)\n\n  useEffect(() => {\n    const toggleVisibility = () => {\n      if (window.scrollY > 200) {\n        setIsVisible(true)\n      } else {\n        setIsVisible(false)\n      }\n    }\n\n    window.addEventListener(\"scroll\", toggleVisibility)\n    return () => window.removeEventListener(\"scroll\", toggleVisibility)\n  }, [])\n\n  const toggleExpanded = () => {\n    setIsExpanded(!isExpanded)\n  }\n\n  const handleItemClick = (href: string) => {\n    window.open(href, \"_blank\", \"noopener,noreferrer\")\n  }\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          exit={{ opacity: 0, scale: 0.8 }}\n          className=\"fixed bottom-6 right-6 z-50 flex flex-col items-end space-y-3\"\n        >\n          {/* Support Items */}\n          <AnimatePresence>\n            {isExpanded && (\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: 20 }}\n                transition={{ duration: 0.2 }}\n                className=\"flex flex-col space-y-2\"\n              >\n                {supportItems.map((item, index) => (\n                  <motion.div\n                    key={item.id}\n                    initial={{ opacity: 0, x: 20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    exit={{ opacity: 0, x: 20 }}\n                    transition={{ delay: index * 0.1 }}\n                    className=\"flex items-center space-x-2\"\n                  >\n                    {/* Label */}\n                    <motion.div\n                      initial={{ opacity: 0, scale: 0.8 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      className=\"shadow-lg rounded-lg border bg-white px-3 py-2\"\n                    >\n                      <span className=\"text-sm font-medium text-gray-700\">\n                        {item.name}\n                      </span>\n                    </motion.div>\n\n                    {/* Icon Button */}\n                    <motion.button\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.95 }}\n                      onClick={() => handleItemClick(item.href)}\n                      className={`shadow-lg flex h-12 w-12 items-center justify-center rounded-full text-white transition-all duration-200 ${item.color} ${item.hoverColor} `}\n                    >\n                      {item.icon}\n                    </motion.button>\n                  </motion.div>\n                ))}\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* Main Toggle Button */}\n          <motion.button\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.95 }}\n            onClick={toggleExpanded}\n            className={`shadow-lg flex h-14 w-14 items-center justify-center rounded-full transition-all duration-300 ${\n              isExpanded\n                ? \"bg-red-500 hover:bg-red-600 rotate-180\"\n                : \"bg-primary-main hover:bg-primary-main/80\"\n            } text-white`}\n          >\n            <motion.div\n              animate={{ rotate: isExpanded ? 45 : 0 }}\n              transition={{ duration: 0.2 }}\n            >\n              {isExpanded ? <X size={24} /> : <MessageSquare size={24} />}\n            </motion.div>\n          </motion.button>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  )\n}\n"], "names": [], "mappings": ";;;;AAIA;AAFA;AAAA;AACA;AAAA;AAHA;;;;;AAeA,wBAAwB;AACxB,MAAM,gBAAgB,kBACpB,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;kBACnD,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAIZ,mBAAmB;AACnB,MAAM,WAAW,kBACf,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;kBACnD,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAIZ,MAAM,eAA8B;IAClC;QACE,IAAI;QACJ,MAAM;QACN,oBAAM,8OAAC;;;;;QACP,MAAM;QACN,OAAO;QACP,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,oBAAM,8OAAC;;;;;QACP,MAAM;QACN,OAAO;QACP,YAAY;IACd;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI,OAAO,OAAO,GAAG,KAAK;gBACxB,aAAa;YACf,OAAO;gBACL,aAAa;YACf;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,cAAc,CAAC;IACjB;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO,IAAI,CAAC,MAAM,UAAU;IAC9B;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAClC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,MAAM;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAC/B,WAAU;;8BAGV,8OAAC,yLAAA,CAAA,kBAAe;8BACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC1B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAET,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC1B,YAAY;oCAAE,OAAO,QAAQ;gCAAI;gCACjC,WAAU;;kDAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,WAAU;kDAEV,cAAA,8OAAC;4CAAK,WAAU;sDACb,KAAK,IAAI;;;;;;;;;;;kDAKd,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,gBAAgB,KAAK,IAAI;wCACxC,WAAW,CAAC,yGAAyG,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,UAAU,CAAC,CAAC,CAAC;kDAEtJ,KAAK,IAAI;;;;;;;+BAzBP,KAAK,EAAE;;;;;;;;;;;;;;;8BAkCtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,YAAY;wBAAE,OAAO;oBAAI;oBACzB,UAAU;wBAAE,OAAO;oBAAK;oBACxB,SAAS;oBACT,WAAW,CAAC,8FAA8F,EACxG,aACI,2CACA,2CACL,WAAW,CAAC;8BAEb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,QAAQ,aAAa,KAAK;wBAAE;wBACvC,YAAY;4BAAE,UAAU;wBAAI;kCAE3B,2BAAa,8OAAC,4LAAA,CAAA,IAAC;4BAAC,MAAM;;;;;iDAAS,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnE"}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/ui/scroll-top.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\n\r\nexport default function ScrollToTopButton() {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const toggleVisibility = () => {\r\n      if (window.scrollY > 300) {\r\n        setIsVisible(true);\r\n      } else {\r\n        setIsVisible(false);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('scroll', toggleVisibility);\r\n    return () => window.removeEventListener('scroll', toggleVisibility);\r\n  }, []);\r\n\r\n  const scrollToTop = () => {\r\n    window.scrollTo({\r\n      top: 0,\r\n      behavior: 'smooth',\r\n    });\r\n  };\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {isVisible && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          exit={{ opacity: 0, y: 20 }}\r\n          className=\"fixed bottom-6 right-6 z-50\"\r\n        >\r\n          <button\r\n            onClick={scrollToTop}\r\n            className=\"rounded-full w-10 h-10 flex items-center justify-center shadow-lg hover:scale-110 transition-transform bg-primary-main text-sm hover:bg-primary-main/80 text-white font-bold\"\r\n          >\r\n            ↑\r\n          </button>\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI,OAAO,OAAO,GAAG,KAAK;gBACxB,aAAa;YACf,OAAO;gBACL,aAAa;YACf;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,MAAM;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC1B,WAAU;sBAEV,cAAA,8OAAC;gBACC,SAAS;gBACT,WAAU;0BACX;;;;;;;;;;;;;;;;AAOX"}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}