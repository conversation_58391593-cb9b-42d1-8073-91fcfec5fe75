{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/banners/components/hero-banner/index.tsx"], "sourcesContent": ["\"use client\"\nimport LocalizedClientLink from \"@modules/common/components/localized-client-link\"\nimport { Button } from \"components/ui/button\"\nimport Typography from \"components/ui/typography\"\nimport { motion } from \"framer-motion\"\nimport Image from \"next/image\"\nimport { useParams } from \"next/navigation\"\nimport { useTranslation } from \"react-i18next\"\nimport \"swiper/css\"\nimport \"swiper/css/navigation\"\nimport \"swiper/css/pagination\"\nimport {\n  Autoplay,\n  Keyboard,\n  Mousewheel,\n  Navigation,\n  Pagination,\n} from \"swiper/modules\"\nimport { Swiper, SwiperSlide } from \"swiper/react\"\nimport { T_Banner } from \"types/banner\"\n\nconst textVariants = {\n  hidden: { opacity: 0, y: 30 },\n  visible: { opacity: 1, y: 0 },\n}\n\nconst descriptionVariants = {\n  hidden: { opacity: 0, y: 20 },\n  visible: { opacity: 1, y: 0 },\n}\n\nconst buttonVariants = {\n  hidden: { opacity: 0, y: 10 },\n  visible: { opacity: 1, y: 0 },\n}\n\ntype HeroBannerProps = {\n  banners: T_Banner[]\n  className?: string\n  height?: string\n}\n\nexport default function HeroBanner({\n  banners,\n  className = \"\",\n  height = \"h-[90vh]\",\n}: HeroBannerProps) {\n  const { t } = useTranslation(\"templates\")\n  const params = useParams()\n  const lang = Array.isArray(params.localeLanguage)\n    ? params.localeLanguage[0] || \"vi\"\n    : params.localeLanguage || \"vi\"\n\n  function parseIfJsonString(value: any) {\n    if (typeof value === \"string\") {\n      try {\n        const parsed = JSON.parse(value)\n        if (typeof parsed === \"object\" && parsed !== null) return parsed\n      } catch {}\n    }\n    return value\n  }\n\n  const getBannerContent = (banner: T_Banner) => {\n    const rawTitle = parseIfJsonString(banner.title)\n    const rawDescription = parseIfJsonString(banner.description)\n    const rawButtonText = parseIfJsonString(banner.button_text)\n\n    const title =\n      typeof rawTitle === \"object\"\n        ? rawTitle?.[lang] || rawTitle?.vi || rawTitle?.en || \"\"\n        : rawTitle || \"\"\n    const description =\n      typeof rawDescription === \"object\"\n        ? rawDescription?.[lang] ||\n          rawDescription?.vi ||\n          rawDescription?.en ||\n          \"\"\n        : rawDescription || \"\"\n    const buttonText =\n      typeof rawButtonText === \"object\"\n        ? rawButtonText?.[lang] || rawButtonText?.vi || rawButtonText?.en || \"\"\n        : rawButtonText || \"\"\n\n    return { title, description, buttonText }\n  }\n\n  if (!banners || banners.length === 0) {\n    return null\n  }\n\n  return (\n    <div className={`h-full w-full ${className}`}>\n      <Swiper\n        cssMode={true}\n        spaceBetween={30}\n        className={`relative !${height}`}\n        mousewheel\n        speed={800}\n        pagination={{\n          clickable: true,\n          type: \"bullets\",\n          bulletClass: \"swiper-pagination-bullet\",\n          bulletActiveClass: \"swiper-pagination-bullet-active\",\n        }}\n        navigation={{\n          nextEl: \".swiper-button-next\",\n          prevEl: \".swiper-button-prev\",\n        }}\n        keyboard\n        loop={banners.length > 1}\n        autoplay={{\n          delay: 5000,\n          disableOnInteraction: false,\n        }}\n        observer\n        observeParents\n        parallax\n        modules={[Navigation, Pagination, Mousewheel, Keyboard, Autoplay]}\n      >\n        {banners.map((banner, index) => {\n          const { title, description, buttonText } = getBannerContent(banner)\n\n          return (\n            <SwiperSlide key={banner.id || index}>\n              <div className=\"relative h-full w-full\">\n                {banner.image && (\n                  <Image\n                    src={banner.image}\n                    alt={title || `Banner ${index + 1}`}\n                    className=\"h-full w-full object-cover object-center\"\n                    sizes=\"100vw\"\n                    priority={index === 0}\n                    width={0}\n                    height={0}\n                  />\n                )}\n              </div>\n              <div className=\"absolute left-0 top-0 flex h-full w-full items-center justify-center\">\n                <div className=\"grid px-10 sm:grid-cols-2 sm:px-20\">\n                  <div className=\"flex flex-col gap-y-8 text-start\">\n                    {title && (\n                      <motion.div\n                        initial=\"hidden\"\n                        animate=\"visible\"\n                        variants={textVariants}\n                        transition={{ duration: 0.5, ease: \"easeOut\" }}\n                      >\n                        <Typography className=\"text-black\" variant=\"h1\">\n                          <div dangerouslySetInnerHTML={{ __html: title }} />\n                        </Typography>\n                      </motion.div>\n                    )}\n\n                    {description && (\n                      <motion.div\n                        initial=\"hidden\"\n                        animate=\"visible\"\n                        variants={descriptionVariants}\n                        transition={{\n                          duration: 0.5,\n                          delay: 0.2,\n                          ease: \"easeOut\",\n                        }}\n                      >\n                        <Typography size=\"base\" className=\"!leading-7\">\n                          <div\n                            dangerouslySetInnerHTML={{ __html: description }}\n                          />\n                        </Typography>\n                      </motion.div>\n                    )}\n\n                    {(banner.link || buttonText) && (\n                      <motion.div\n                        initial=\"hidden\"\n                        animate=\"visible\"\n                        variants={buttonVariants}\n                        transition={{\n                          duration: 0.5,\n                          delay: 0.3,\n                          ease: \"easeOut\",\n                        }}\n                      >\n                        {banner.link ? (\n                          banner.link.startsWith(\"http\") ? (\n                            <a\n                              href={banner.link}\n                              target=\"_blank\"\n                              rel=\"noopener noreferrer\"\n                            >\n                              <Button className=\"flex w-fit items-center px-6 text-base font-semibold text-white\">\n                                {buttonText || t(\"view_details\")}\n                              </Button>\n                            </a>\n                          ) : (\n                            <LocalizedClientLink href={banner.link}>\n                              <Button className=\"flex w-fit items-center px-6 text-base font-semibold text-white\">\n                                {buttonText || t(\"view_details\")}\n                              </Button>\n                            </LocalizedClientLink>\n                          )\n                        ) : (\n                          <Button className=\"flex w-fit items-center px-6 text-base font-semibold text-white\">\n                            {buttonText || t(\"view_details\")}\n                          </Button>\n                        )}\n                      </motion.div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </SwiperSlide>\n          )\n        })}\n\n        <div className=\"swiper-button-prev absolute z-[1000] !flex items-center justify-center rounded-full bg-white p-2 text-sm text-primary-main\"></div>\n        <div className=\"swiper-button-next absolute z-[1000] !flex items-center justify-center rounded-full bg-white p-2 text-sm text-primary-main\"></div>\n      </Swiper>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAEA;AACA;AACA;AAIA;AAOA;AAPA;AAAA;AAAA;AAAA;AAAA;AAPA;AAGA;AAPA;;;;;;;;;;;;;;AAqBA,MAAM,eAAe;IACnB,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;AAC9B;AAEA,MAAM,sBAAsB;IAC1B,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;AAC9B;AAEA,MAAM,iBAAiB;IACrB,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;AAC9B;AAQe,SAAS,WAAW,EACjC,OAAO,EACP,YAAY,EAAE,EACd,SAAS,UAAU,EACH;IAChB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE;IAC7B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,OAAO,MAAM,OAAO,CAAC,OAAO,cAAc,IAC5C,OAAO,cAAc,CAAC,EAAE,IAAI,OAC5B,OAAO,cAAc,IAAI;IAE7B,SAAS,kBAAkB,KAAU;QACnC,IAAI,OAAO,UAAU,UAAU;YAC7B,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,IAAI,OAAO,WAAW,YAAY,WAAW,MAAM,OAAO;YAC5D,EAAE,OAAM,CAAC;QACX;QACA,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW,kBAAkB,OAAO,KAAK;QAC/C,MAAM,iBAAiB,kBAAkB,OAAO,WAAW;QAC3D,MAAM,gBAAgB,kBAAkB,OAAO,WAAW;QAE1D,MAAM,QACJ,OAAO,aAAa,WAChB,UAAU,CAAC,KAAK,IAAI,UAAU,MAAM,UAAU,MAAM,KACpD,YAAY;QAClB,MAAM,cACJ,OAAO,mBAAmB,WACtB,gBAAgB,CAAC,KAAK,IACtB,gBAAgB,MAChB,gBAAgB,MAChB,KACA,kBAAkB;QACxB,MAAM,aACJ,OAAO,kBAAkB,WACrB,eAAe,CAAC,KAAK,IAAI,eAAe,MAAM,eAAe,MAAM,KACnE,iBAAiB;QAEvB,OAAO;YAAE;YAAO;YAAa;QAAW;IAC1C;IAEA,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,cAAc,EAAE,WAAW;kBAC1C,cAAA,8OAAC,0IAAA,CAAA,SAAM;YACL,SAAS;YACT,cAAc;YACd,WAAW,CAAC,UAAU,EAAE,QAAQ;YAChC,UAAU;YACV,OAAO;YACP,YAAY;gBACV,WAAW;gBACX,MAAM;gBACN,aAAa;gBACb,mBAAmB;YACrB;YACA,YAAY;gBACV,QAAQ;gBACR,QAAQ;YACV;YACA,QAAQ;YACR,MAAM,QAAQ,MAAM,GAAG;YACvB,UAAU;gBACR,OAAO;gBACP,sBAAsB;YACxB;YACA,QAAQ;YACR,cAAc;YACd,QAAQ;YACR,SAAS;gBAAC,yLAAA,CAAA,aAAU;gBAAE,yLAAA,CAAA,aAAU;gBAAE,yLAAA,CAAA,aAAU;gBAAE,qLAAA,CAAA,WAAQ;gBAAE,qLAAA,CAAA,WAAQ;aAAC;;gBAEhE,QAAQ,GAAG,CAAC,CAAC,QAAQ;oBACpB,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,iBAAiB;oBAE5D,qBACE,8OAAC,0IAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAU;0CACZ,OAAO,KAAK,kBACX,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAK,OAAO,KAAK;oCACjB,KAAK,SAAS,CAAC,OAAO,EAAE,QAAQ,GAAG;oCACnC,WAAU;oCACV,OAAM;oCACN,UAAU,UAAU;oCACpB,OAAO;oCACP,QAAQ;;;;;;;;;;;0CAId,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;4CACZ,uBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAQ;gDACR,SAAQ;gDACR,UAAU;gDACV,YAAY;oDAAE,UAAU;oDAAK,MAAM;gDAAU;0DAE7C,cAAA,8OAAC,sIAAA,CAAA,UAAU;oDAAC,WAAU;oDAAa,SAAQ;8DACzC,cAAA,8OAAC;wDAAI,yBAAyB;4DAAE,QAAQ;wDAAM;;;;;;;;;;;;;;;;4CAKnD,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAQ;gDACR,SAAQ;gDACR,UAAU;gDACV,YAAY;oDACV,UAAU;oDACV,OAAO;oDACP,MAAM;gDACR;0DAEA,cAAA,8OAAC,sIAAA,CAAA,UAAU;oDAAC,MAAK;oDAAO,WAAU;8DAChC,cAAA,8OAAC;wDACC,yBAAyB;4DAAE,QAAQ;wDAAY;;;;;;;;;;;;;;;;4CAMtD,CAAC,OAAO,IAAI,IAAI,UAAU,mBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAQ;gDACR,SAAQ;gDACR,UAAU;gDACV,YAAY;oDACV,UAAU;oDACV,OAAO;oDACP,MAAM;gDACR;0DAEC,OAAO,IAAI,GACV,OAAO,IAAI,CAAC,UAAU,CAAC,wBACrB,8OAAC;oDACC,MAAM,OAAO,IAAI;oDACjB,QAAO;oDACP,KAAI;8DAEJ,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;kEACf,cAAc,EAAE;;;;;;;;;;yEAIrB,8OAAC,+KAAA,CAAA,UAAmB;oDAAC,MAAM,OAAO,IAAI;8DACpC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;kEACf,cAAc,EAAE;;;;;;;;;;yEAKvB,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;8DACf,cAAc,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAhFf,OAAO,EAAE,IAAI;;;;;gBA0FnC;8BAEA,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB"}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 333, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/blog/components/card-blog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport LocalizedClientLink from \"@modules/common/components/localized-client-link\"\r\nimport Typography from \"components/ui/typography\"\r\nimport moment from \"moment\"\r\nimport Image from \"next/image\"\r\nimport { useParams } from \"next/navigation\"\r\nimport { T_Post } from \"types/post\"\r\nimport { PAGE_PATH } from \"utils/path\"\r\n\r\ntype CardBlogProps = {\r\n  isGridType?: boolean\r\n  item: T_Post\r\n}\r\n\r\nexport function CardBlog({ isGridType, item }: CardBlogProps) {\r\n  const params = useParams()\r\n  const lang = Array.isArray(params.localeLanguage)\r\n    ? params.localeLanguage[0] || \"vi\"\r\n    : params.localeLanguage || \"vi\"\r\n\r\n  function parseIfJsonString(value: any) {\r\n    if (typeof value === \"string\") {\r\n      try {\r\n        const parsed = JSON.parse(value)\r\n        if (typeof parsed === \"object\" && parsed !== null) return parsed\r\n      } catch {}\r\n    }\r\n    return value\r\n  }\r\n\r\n  const rawTitle = parseIfJsonString(item.title)\r\n  const rawExcerpt = parseIfJsonString(item.excerpt)\r\n\r\n  const title =\r\n    typeof rawTitle === \"object\"\r\n      ? rawTitle?.[lang] || rawTitle?.vi || rawTitle?.en || \"\"\r\n      : rawTitle || \"\"\r\n  const excerpt =\r\n    typeof rawExcerpt === \"object\"\r\n      ? rawExcerpt?.[lang] || rawExcerpt?.vi || rawExcerpt?.en || \"\"\r\n      : rawExcerpt || \"\"\r\n\r\n  return (\r\n    <LocalizedClientLink\r\n      href={\r\n        item?.handle\r\n          ? `${PAGE_PATH.BLOGS.root}/${item?.handle}`\r\n          : `${PAGE_PATH.BLOGS.root}`\r\n      }\r\n    >\r\n      <div className=\"shadow-md group grid h-full overflow-hidden rounded-lg bg-white p-4 transition-transform hover:scale-[1.02] md:p-0\">\r\n        <Typography\r\n          variant=\"h5\"\r\n          size=\"lg\"\r\n          className=\"mb-2 line-clamp-2 text-start font-semibold text-gray-900 md:hidden\"\r\n        >\r\n          {title}\r\n        </Typography>\r\n        <div\r\n          className={`${isGridType ? \"grid grid-cols-[40%_1fr] items-start gap-4\" : \"flex flex-col\"}`}\r\n        >\r\n          <div className=\"relative aspect-[16/10] w-full overflow-hidden rounded-lg\">\r\n            <Image\r\n              src={item?.image ?? \"/images/no-image.svg\"}\r\n              alt={title || \"blog-image\"}\r\n              fill\r\n              className=\"h-full w-full object-cover\"\r\n            />\r\n          </div>\r\n          <div className={`md:p-4 ${isGridType ? \"flex flex-col\" : \"\"}`}>\r\n            <Typography\r\n              variant=\"p\"\r\n              size=\"sm\"\r\n              className=\"text-start font-semibold text-gray-700\"\r\n            >\r\n              {moment(item?.created_at).format(\"DD/MM/YYYY\")}\r\n            </Typography>\r\n            <div className=\"hidden md:block\">\r\n              <Typography\r\n                variant=\"h5\"\r\n                size=\"lg\"\r\n                className=\"mt-2 line-clamp-2 text-start font-semibold text-gray-900\"\r\n              >\r\n                {title}\r\n              </Typography>\r\n            </div>\r\n            <Typography\r\n              variant=\"p\"\r\n              size=\"sm\"\r\n              className=\"line-clamp-4 text-start text-gray-700 md:mt-2\"\r\n            >\r\n              {excerpt}\r\n            </Typography>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </LocalizedClientLink>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AARA;;;;;;;;AAeO,SAAS,SAAS,EAAE,UAAU,EAAE,IAAI,EAAiB;IAC1D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,OAAO,MAAM,OAAO,CAAC,OAAO,cAAc,IAC5C,OAAO,cAAc,CAAC,EAAE,IAAI,OAC5B,OAAO,cAAc,IAAI;IAE7B,SAAS,kBAAkB,KAAU;QACnC,IAAI,OAAO,UAAU,UAAU;YAC7B,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,IAAI,OAAO,WAAW,YAAY,WAAW,MAAM,OAAO;YAC5D,EAAE,OAAM,CAAC;QACX;QACA,OAAO;IACT;IAEA,MAAM,WAAW,kBAAkB,KAAK,KAAK;IAC7C,MAAM,aAAa,kBAAkB,KAAK,OAAO;IAEjD,MAAM,QACJ,OAAO,aAAa,WAChB,UAAU,CAAC,KAAK,IAAI,UAAU,MAAM,UAAU,MAAM,KACpD,YAAY;IAClB,MAAM,UACJ,OAAO,eAAe,WAClB,YAAY,CAAC,KAAK,IAAI,YAAY,MAAM,YAAY,MAAM,KAC1D,cAAc;IAEpB,qBACE,8OAAC,+KAAA,CAAA,UAAmB;QAClB,MACE,MAAM,SACF,GAAG,oHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,QAAQ,GACzC,GAAG,oHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,IAAI,EAAE;kBAG/B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,UAAU;oBACT,SAAQ;oBACR,MAAK;oBACL,WAAU;8BAET;;;;;;8BAEH,8OAAC;oBACC,WAAW,GAAG,aAAa,+CAA+C,iBAAiB;;sCAE3F,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,MAAM,SAAS;gCACpB,KAAK,SAAS;gCACd,IAAI;gCACJ,WAAU;;;;;;;;;;;sCAGd,8OAAC;4BAAI,WAAW,CAAC,OAAO,EAAE,aAAa,kBAAkB,IAAI;;8CAC3D,8OAAC,sIAAA,CAAA,UAAU;oCACT,SAAQ;oCACR,MAAK;oCACL,WAAU;8CAET,CAAA,GAAA,gIAAA,CAAA,UAAM,AAAD,EAAE,MAAM,YAAY,MAAM,CAAC;;;;;;8CAEnC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,sIAAA,CAAA,UAAU;wCACT,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAET;;;;;;;;;;;8CAGL,8OAAC,sIAAA,CAAA,UAAU;oCACT,SAAQ;oCACR,MAAK;oCACL,WAAU;8CAET;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf"}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 472, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/ui/pagination.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"utils\"\r\nimport { ChevronLeftIcon, ChevronRightIcon, DotsHorizontalIcon } from \"@radix-ui/react-icons\"\r\nimport { buttonVariants } from \"./button\"\r\nimport { ButtonProps } from \"./button\"\r\n\r\nconst Pagination = ({ className, ...props }: React.ComponentProps<\"nav\">) => (\r\n  <nav\r\n    role=\"navigation\"\r\n    aria-label=\"pagination\"\r\n    className={cn(\"mx-auto flex w-full justify-center\", className)}\r\n    {...props}\r\n  />\r\n)\r\nPagination.displayName = \"Pagination\"\r\n\r\nconst PaginationContent = React.forwardRef<\r\n  HTMLUListElement,\r\n  React.ComponentProps<\"ul\">\r\n>(({ className, ...props }, ref) => (\r\n  <ul\r\n    ref={ref}\r\n    className={cn(\"flex flex-row items-center gap-1\", className)}\r\n    {...props}\r\n  />\r\n))\r\nPaginationContent.displayName = \"PaginationContent\"\r\n\r\nconst PaginationItem = React.forwardRef<\r\n  HTMLLIElement,\r\n  React.ComponentProps<\"li\">\r\n>(({ className, ...props }, ref) => (\r\n  <li ref={ref} className={cn(\"\", className)} {...props} />\r\n))\r\nPaginationItem.displayName = \"PaginationItem\"\r\n\r\ntype PaginationLinkProps = {\r\n  isActive?: boolean\r\n} & Pick<ButtonProps, \"size\"> &\r\n  React.ComponentProps<\"a\">\r\n\r\nconst PaginationLink = ({\r\n  className,\r\n  isActive,\r\n  size = \"icon\",\r\n  ...props\r\n}: PaginationLinkProps) => (\r\n  <a\r\n    aria-current={isActive ? \"page\" : undefined}\r\n    className={cn(\r\n      buttonVariants({\r\n        variant: isActive ? \"paginationActive\" : \"pagination\",\r\n        size,\r\n      }),\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nPaginationLink.displayName = \"PaginationLink\"\r\n\r\nconst PaginationPrevious = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) => (\r\n  <PaginationLink\r\n    aria-label=\"Go to previous page\"\r\n    size=\"default\"\r\n    className={cn(\"gap-1 pl-2.5\", className)}\r\n    {...props}\r\n  >\r\n    <ChevronLeftIcon className=\"h-4 w-4\" />\r\n    {/* <span>Previous</span> */}\r\n  </PaginationLink>\r\n)\r\nPaginationPrevious.displayName = \"PaginationPrevious\"\r\n\r\nconst PaginationNext = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) => (\r\n  <PaginationLink\r\n    aria-label=\"Go to next page\"\r\n    size=\"default\"\r\n    className={cn(\"gap-1 pr-2.5\", className)}\r\n    {...props}\r\n  >\r\n    {/* <span>Next</span> */}\r\n    <ChevronRightIcon className=\"h-4 w-4\" />\r\n  </PaginationLink>\r\n)\r\nPaginationNext.displayName = \"PaginationNext\"\r\n\r\nconst PaginationEllipsis = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) => (\r\n  <span\r\n    aria-hidden\r\n    className={cn(\"flex h-9 w-9 items-center justify-center\", className)}\r\n    {...props}\r\n  >\r\n    <DotsHorizontalIcon className=\"h-4 w-4\" />\r\n    <span className=\"sr-only\">More pages</span>\r\n  </span>\r\n)\r\nPaginationEllipsis.displayName = \"PaginationEllipsis\"\r\n\r\nexport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationLink,\r\n  PaginationItem,\r\n  PaginationPrevious,\r\n  PaginationNext,\r\n  PaginationEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AAEA;AADA;;;;;;AAIA,MAAM,aAAa,CAAC,EAAE,SAAS,EAAE,GAAG,OAAoC,iBACtE,8OAAC;QACC,MAAK;QACL,cAAW;QACX,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,kCAAoB,sMAAM,UAAU,CAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,+BAAiB,sMAAM,UAAU,CAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAG,KAAK;QAAK,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,IAAI;QAAa,GAAG,KAAK;;;;;;AAEvD,eAAe,WAAW,GAAG;AAO7B,MAAM,iBAAiB,CAAC,EACtB,SAAS,EACT,QAAQ,EACR,OAAO,MAAM,EACb,GAAG,OACiB,iBACpB,8OAAC;QACC,gBAAc,WAAW,SAAS;QAClC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YACb,SAAS,WAAW,qBAAqB;YACzC;QACF,IACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,GAAG,OACyC,iBAC5C,8OAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;QAC7B,GAAG,KAAK;kBAET,cAAA,8OAAC,gLAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAI/B,mBAAmB,WAAW,GAAG;AAEjC,MAAM,iBAAiB,CAAC,EACtB,SAAS,EACT,GAAG,OACyC,iBAC5C,8OAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;QAC7B,GAAG,KAAK;kBAGT,cAAA,8OAAC,gLAAA,CAAA,mBAAgB;YAAC,WAAU;;;;;;;;;;;AAGhC,eAAe,WAAW,GAAG;AAE7B,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,GAAG,OAC0B,iBAC7B,8OAAC;QACC,aAAW;QACX,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;0BAET,8OAAC,gLAAA,CAAA,qBAAkB;gBAAC,WAAU;;;;;;0BAC9B,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAG9B,mBAAmB,WAAW,GAAG"}}, {"offset": {"line": 599, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/custom-pagination.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport {\r\n  <PERSON><PERSON><PERSON>,\r\n  PaginationContent,\r\n  PaginationEllipsis,\r\n  PaginationItem,\r\n  PaginationLink,\r\n  PaginationNext,\r\n  PaginationPrevious,\r\n} from \"./ui/pagination\"\r\n\r\ntype PaginationProps = {\r\n  currentPage: number\r\n  totalPage: number\r\n  onChange: (page: number) => void\r\n}\r\n\r\nexport default function CustomPagination({\r\n  currentPage,\r\n  totalPage,\r\n  onChange,\r\n}: PaginationProps) {\r\n  const handlePageChange = (page: number) => {\r\n    onChange(page)\r\n  }\r\n\r\n  const getPaginationItems = () => {\r\n    const paginationItems = []\r\n    const startPage = Math.max(1, currentPage - 2)\r\n    const endPage = Math.min(totalPage, currentPage + 2)\r\n\r\n    if (startPage > 1) {\r\n      paginationItems.push(\r\n        <PaginationItem key={1}>\r\n          <PaginationLink\r\n            className=\"cursor-pointer\"\r\n            onClick={() => handlePageChange(1)}\r\n          >\r\n            1\r\n          </PaginationLink>\r\n        </PaginationItem>\r\n      )\r\n      if (startPage > 2) {\r\n        paginationItems.push(\r\n          <PaginationItem key=\"ellipsis-start\" className=\"cursor-pointer\">\r\n            <PaginationEllipsis />\r\n          </PaginationItem>\r\n        )\r\n      }\r\n    }\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      paginationItems.push(\r\n        <PaginationItem key={i}>\r\n          <PaginationLink\r\n            isActive={i === currentPage}\r\n            onClick={() => handlePageChange(i)}\r\n            className=\"cursor-pointer\"\r\n          >\r\n            {i}\r\n          </PaginationLink>\r\n        </PaginationItem>\r\n      )\r\n    }\r\n\r\n    if (endPage < totalPage) {\r\n      if (endPage < totalPage - 1) {\r\n        paginationItems.push(\r\n          <PaginationItem key=\"ellipsis-end\" className=\"cursor-pointer\">\r\n            <PaginationEllipsis />\r\n          </PaginationItem>\r\n        )\r\n      }\r\n      paginationItems.push(\r\n        <PaginationItem key={totalPage}>\r\n          <PaginationLink\r\n            className=\"cursor-pointer\"\r\n            onClick={() => handlePageChange(totalPage)}\r\n          >\r\n            {totalPage}\r\n          </PaginationLink>\r\n        </PaginationItem>\r\n      )\r\n    }\r\n\r\n    return paginationItems\r\n  }\r\n\r\n  return (\r\n    <Pagination>\r\n      <PaginationContent>\r\n        <PaginationItem>\r\n          <PaginationPrevious\r\n            className={`cursor-pointer ${currentPage <= 1 ? \"cursor-not-allowed opacity-50\" : \"\"}`}\r\n            onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}\r\n          />\r\n        </PaginationItem>\r\n        {getPaginationItems()}\r\n        <PaginationItem>\r\n          <PaginationNext\r\n            className={`cursor-pointer ${currentPage >= totalPage ? \"cursor-not-allowed opacity-50\" : \"\"}`}\r\n            onClick={() =>\r\n              currentPage < totalPage && handlePageChange(currentPage + 1)\r\n            }\r\n          />\r\n        </PaginationItem>\r\n      </PaginationContent>\r\n    </Pagination>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAkBe,SAAS,iBAAiB,EACvC,WAAW,EACX,SAAS,EACT,QAAQ,EACQ;IAChB,MAAM,mBAAmB,CAAC;QACxB,SAAS;IACX;IAEA,MAAM,qBAAqB;QACzB,MAAM,kBAAkB,EAAE;QAC1B,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc;QAC5C,MAAM,UAAU,KAAK,GAAG,CAAC,WAAW,cAAc;QAElD,IAAI,YAAY,GAAG;YACjB,gBAAgB,IAAI,eAClB,8OAAC,sIAAA,CAAA,iBAAc;0BACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;oBACb,WAAU;oBACV,SAAS,IAAM,iBAAiB;8BACjC;;;;;;eAJkB;;;;;YASvB,IAAI,YAAY,GAAG;gBACjB,gBAAgB,IAAI,eAClB,8OAAC,sIAAA,CAAA,iBAAc;oBAAsB,WAAU;8BAC7C,cAAA,8OAAC,sIAAA,CAAA,qBAAkB;;;;;mBADD;;;;;YAIxB;QACF;QAEA,IAAK,IAAI,IAAI,WAAW,KAAK,SAAS,IAAK;YACzC,gBAAgB,IAAI,eAClB,8OAAC,sIAAA,CAAA,iBAAc;0BACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;oBACb,UAAU,MAAM;oBAChB,SAAS,IAAM,iBAAiB;oBAChC,WAAU;8BAET;;;;;;eANgB;;;;;QAUzB;QAEA,IAAI,UAAU,WAAW;YACvB,IAAI,UAAU,YAAY,GAAG;gBAC3B,gBAAgB,IAAI,eAClB,8OAAC,sIAAA,CAAA,iBAAc;oBAAoB,WAAU;8BAC3C,cAAA,8OAAC,sIAAA,CAAA,qBAAkB;;;;;mBADD;;;;;YAIxB;YACA,gBAAgB,IAAI,eAClB,8OAAC,sIAAA,CAAA,iBAAc;0BACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;oBACb,WAAU;oBACV,SAAS,IAAM,iBAAiB;8BAE/B;;;;;;eALgB;;;;;QASzB;QAEA,OAAO;IACT;IAEA,qBACE,8OAAC,sIAAA,CAAA,aAAU;kBACT,cAAA,8OAAC,sIAAA,CAAA,oBAAiB;;8BAChB,8OAAC,sIAAA,CAAA,iBAAc;8BACb,cAAA,8OAAC,sIAAA,CAAA,qBAAkB;wBACjB,WAAW,CAAC,eAAe,EAAE,eAAe,IAAI,kCAAkC,IAAI;wBACtF,SAAS,IAAM,cAAc,KAAK,iBAAiB,cAAc;;;;;;;;;;;gBAGpE;8BACD,8OAAC,sIAAA,CAAA,iBAAc;8BACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;wBACb,WAAW,CAAC,eAAe,EAAE,eAAe,YAAY,kCAAkC,IAAI;wBAC9F,SAAS,IACP,cAAc,aAAa,iBAAiB,cAAc;;;;;;;;;;;;;;;;;;;;;;AAOxE"}}, {"offset": {"line": 747, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 753, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/blog/components/blog-list/index.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { usePara<PERSON>, useRouter } from \"next/navigation\"\r\nimport { useTranslation } from \"react-i18next\"\r\n\r\nimport { CardBlog } from \"@modules/blog/components/card-blog\"\r\nimport { T_Post, T_PostListResp } from \"types/post\"\r\n\r\nimport LocalizedClientLink from \"@modules/common/components/localized-client-link\"\r\nimport CustomPagination from \"components/custom-pagination\"\r\nimport Typography from \"components/ui/typography\"\r\nimport useMediaQuery from \"hooks/use-media-query\"\r\nimport { PAGE_PATH } from \"utils/path\"\r\ntype IProps = {\r\n  postsList: T_PostListResp\r\n}\r\n\r\nexport default function BlogList({ postsList }: IProps) {\r\n  const { push } = useRouter()\r\n  const isMobile = useMediaQuery(\"(max-width: 768px)\")\r\n  const { posts, paging } = postsList\r\n  const { countryCode, localeLanguage } = useParams()\r\n  const { t } = useTranslation(\"templates\")\r\n\r\n  const handleChangePage = (page: number) => {\r\n    push(\r\n      `/${countryCode}/${localeLanguage}${PAGE_PATH.BLOGS.root}?page=${page}`\r\n    )\r\n  }\r\n\r\n  const renderPosts = () => (\r\n    <div className=\"mb-6 flex flex-col gap-y-6 bg-yellow-100 px-6 py-8 sm:px-10 sm:py-16\">\r\n      <div className=\"flex items-center justify-between\">\r\n        <Typography size=\"2xl\" className=\"font-bold text-primary-dark\">\r\n          Tin tức\r\n        </Typography>\r\n        <LocalizedClientLink href={PAGE_PATH.BLOGS.root}>\r\n          <Typography\r\n            size=\"base18\"\r\n            className=\"font-bold text-primary-main underline-offset-4 hover:underline\"\r\n          >\r\n            {t(\"view_more\")}\r\n          </Typography>\r\n        </LocalizedClientLink>\r\n      </div>\r\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4\">\r\n        {posts.map((item: T_Post, idx) => (\r\n          <CardBlog key={idx} item={item} isGridType={isMobile} />\r\n        ))}\r\n      </div>\r\n    </div>\r\n  )\r\n\r\n  const renderPagination = () =>\r\n    paging?.total_pages &&\r\n    paging?.total_pages > 1 && (\r\n      <CustomPagination\r\n        currentPage={paging.page || 1}\r\n        totalPage={paging?.total_pages || 1}\r\n        onChange={handleChangePage}\r\n      />\r\n    )\r\n\r\n  return (\r\n    <>\r\n      {renderPosts()}\r\n      {/* {renderPagination()} */}\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAGA;AACA;AACA;AACA;AACA;AATA;AAHA;;;;;;;;;;AAiBe,SAAS,SAAS,EAAE,SAAS,EAAU;IACpD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACzB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAa,AAAD,EAAE;IAC/B,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;IAC1B,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAChD,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,mBAAmB,CAAC;QACxB,KACE,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,iBAAiB,oHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM;IAE3E;IAEA,MAAM,cAAc,kBAClB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sIAAA,CAAA,UAAU;4BAAC,MAAK;4BAAM,WAAU;sCAA8B;;;;;;sCAG/D,8OAAC,+KAAA,CAAA,UAAmB;4BAAC,MAAM,oHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,IAAI;sCAC7C,cAAA,8OAAC,sIAAA,CAAA,UAAU;gCACT,MAAK;gCACL,WAAU;0CAET,EAAE;;;;;;;;;;;;;;;;;8BAIT,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAc,oBACxB,8OAAC,qJAAA,CAAA,WAAQ;4BAAW,MAAM;4BAAM,YAAY;2BAA7B;;;;;;;;;;;;;;;;IAMvB,MAAM,mBAAmB,IACvB,QAAQ,eACR,QAAQ,cAAc,mBACpB,8OAAC,0IAAA,CAAA,UAAgB;YACf,aAAa,OAAO,IAAI,IAAI;YAC5B,WAAW,QAAQ,eAAe;YAClC,UAAU;;;;;;IAIhB,qBACE;kBACG;;AAIP"}}, {"offset": {"line": 856, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 862, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/ui/marquee.tsx"], "sourcesContent": ["import { cn } from \"utils\";\r\n\r\ninterface MarqueeProps {\r\n  className?: string;\r\n  reverse?: boolean;\r\n  pauseOnHover?: boolean;\r\n  children?: React.ReactNode;\r\n  vertical?: boolean;\r\n  repeat?: number;\r\n  [key: string]: any;\r\n}\r\n\r\nexport default function Marquee({\r\n  className,\r\n  reverse,\r\n  pauseOnHover = false,\r\n  children,\r\n  vertical = false,\r\n  repeat = 4,\r\n  ...props\r\n}: MarqueeProps) {\r\n  return (\r\n    <div\r\n      {...props}\r\n      className={cn(\r\n        'group flex overflow-hidden p-2 [--duration:40s] [--gap:1rem] [gap:var(--gap)]',\r\n        {\r\n          'flex-row': !vertical,\r\n          'flex-col': vertical,\r\n        },\r\n        className\r\n      )}\r\n    >\r\n      {Array(repeat)\r\n        .fill(0)\r\n        .map((_, i) => (\r\n          <div\r\n            key={i}\r\n            className={cn('flex shrink-0 justify-around [gap:var(--gap)]', {\r\n              'animate-marquee flex-row': !vertical,\r\n              'animate-marquee-vertical flex-col': vertical,\r\n              'group-hover:[animation-play-state:paused]': pauseOnHover,\r\n              '[animation-direction:reverse]': reverse,\r\n            })}\r\n          >\r\n            {children}\r\n          </div>\r\n        ))}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;;;AAYe,SAAS,QAAQ,EAC9B,SAAS,EACT,OAAO,EACP,eAAe,KAAK,EACpB,QAAQ,EACR,WAAW,KAAK,EAChB,SAAS,CAAC,EACV,GAAG,OACU;IACb,qBACE,8OAAC;QACE,GAAG,KAAK;QACT,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,iFACA;YACE,YAAY,CAAC;YACb,YAAY;QACd,GACA;kBAGD,MAAM,QACJ,IAAI,CAAC,GACL,GAAG,CAAC,CAAC,GAAG,kBACP,8OAAC;gBAEC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;oBAC7D,4BAA4B,CAAC;oBAC7B,qCAAqC;oBACrC,6CAA6C;oBAC7C,iCAAiC;gBACnC;0BAEC;eARI;;;;;;;;;;AAajB"}}, {"offset": {"line": 895, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 901, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/home/<USER>/customer-trust/customer-trust.tsx"], "sourcesContent": ["\"use client\"\r\nimport Marquee from \"components/ui/marquee\"\r\nimport Typography from \"components/ui/typography\"\r\nimport { useState } from \"react\"\r\nimport { useTranslation } from \"react-i18next\"\r\n\r\nexport default function CustomerTrust() {\r\n  const { t } = useTranslation(\"templates\")\r\n\r\n  const allLogos = [...Array(28)].map((_, index) => (\r\n    <TrustedCustomerItem key={index} index={index} />\r\n  ))\r\n\r\n  const row1 = allLogos.slice(0, 10)\r\n  const row2 = allLogos.slice(10, 20)\r\n  const row3 = allLogos.slice(20, 28)\r\n\r\n  return (\r\n    <div className=\"mt-16 flex w-full flex-col gap-y-4 px-10\">\r\n      <Typography\r\n        size=\"3xl\"\r\n        className=\"text-center font-bold text-primary-dark\"\r\n      >\r\n        {t(\"trusted_customers\")}\r\n      </Typography>\r\n      <div className=\"md:shadow-xl relative flex h-full w-full flex-col items-center justify-center gap-2 overflow-hidden rounded-lg bg-background py-6 sm:gap-8 sm:py-10\">\r\n        <Marquee speed={10}>{row1}</Marquee>\r\n        <Marquee speed={12} reverse>\r\n          {row2}\r\n        </Marquee>\r\n        <Marquee speed={8}>{row3}</Marquee>\r\n        <div className=\"pointer-events-none absolute inset-y-0 left-0 w-1/4 bg-gradient-to-r from-white dark:from-background sm:w-1/3\"></div>\r\n        <div className=\"pointer-events-none absolute inset-y-0 right-0 w-1/4 bg-gradient-to-l from-white dark:from-background sm:w-1/3\"></div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nconst TrustedCustomerItem = ({ index }: { index: number }) => {\r\n  const [isHovering, setIsHovering] = useState(false)\r\n\r\n  return (\r\n    <div\r\n      className=\"flex cursor-pointer items-center justify-center\"\r\n      onMouseEnter={() => setIsHovering(true)}\r\n      onMouseLeave={() => setIsHovering(false)}\r\n    >\r\n      <img\r\n        className=\"customer-logo h-14 w-32 rounded-md border-gray-200 object-contain p-2 hover:border\"\r\n        src={`/images/home/<USER>/trusted${index + 1}${\r\n          isHovering ? \"_color\" : \"\"\r\n        }.png`}\r\n        alt={`Trusted Customer ${index + 1}`}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AAJA;;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,WAAW;WAAI,MAAM;KAAI,CAAC,GAAG,CAAC,CAAC,GAAG,sBACtC,8OAAC;YAAgC,OAAO;WAAd;;;;;IAG5B,MAAM,OAAO,SAAS,KAAK,CAAC,GAAG;IAC/B,MAAM,OAAO,SAAS,KAAK,CAAC,IAAI;IAChC,MAAM,OAAO,SAAS,KAAK,CAAC,IAAI;IAEhC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAU;gBACT,MAAK;gBACL,WAAU;0BAET,EAAE;;;;;;0BAEL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,mIAAA,CAAA,UAAO;wBAAC,OAAO;kCAAK;;;;;;kCACrB,8OAAC,mIAAA,CAAA,UAAO;wBAAC,OAAO;wBAAI,OAAO;kCACxB;;;;;;kCAEH,8OAAC,mIAAA,CAAA,UAAO;wBAAC,OAAO;kCAAI;;;;;;kCACpB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;AAEA,MAAM,sBAAsB,CAAC,EAAE,KAAK,EAAqB;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,8OAAC;QACC,WAAU;QACV,cAAc,IAAM,cAAc;QAClC,cAAc,IAAM,cAAc;kBAElC,cAAA,8OAAC;YACC,WAAU;YACV,KAAK,CAAC,iCAAiC,EAAE,QAAQ,IAC/C,aAAa,WAAW,GACzB,IAAI,CAAC;YACN,KAAK,CAAC,iBAAiB,EAAE,QAAQ,GAAG;;;;;;;;;;;AAI5C"}}, {"offset": {"line": 1018, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1024, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/svg/ic-ship.tsx"], "sourcesContent": ["export const ShipIcon = ({ size = 40, color = \"#fff\" }) => {\r\n  return (\r\n    <svg\r\n      width=\"100\"\r\n      height=\"54\"\r\n      viewBox=\"0 0 100 54\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M85.5066 53.6597C83.8374 53.6609 82.2053 53.1671 80.8168 52.2406C79.4283 51.3141 78.3458 49.9967 77.7062 48.4549C77.0666 46.9131 76.8986 45.2162 77.2235 43.5789C77.5484 41.9416 78.3516 40.4375 79.5314 39.2568C80.7113 38.076 82.2149 37.2717 83.8519 36.9456C85.489 36.6196 87.1859 36.7863 88.7282 37.4248C90.2705 38.0633 91.5887 39.1449 92.5162 40.5327C93.4437 41.9205 93.9388 43.5522 93.9388 45.2214C93.9377 47.458 93.0491 49.6027 91.4682 51.1848C89.8873 52.7669 87.7432 53.657 85.5066 53.6597ZM85.5066 39.8612C84.4462 39.86 83.4092 40.1734 82.527 40.7616C81.6447 41.3498 80.9567 42.1865 80.5501 43.1659C80.1434 44.1452 80.0364 45.2232 80.2425 46.2633C80.4487 47.3035 80.9587 48.2592 81.7081 49.0094C82.4574 49.7597 83.4125 50.2708 84.4524 50.4781C85.4924 50.6854 86.5704 50.5796 87.5502 50.1741C88.53 49.7686 89.3675 49.0816 89.9568 48.2C90.5461 47.3184 90.8606 46.2818 90.8606 45.2214C90.86 43.801 90.296 42.4389 89.2922 41.434C88.2884 40.429 86.9269 39.8634 85.5066 39.8612Z\"\r\n        fill=\"white\"\r\n      />\r\n      <path\r\n        d=\"M41.0391 53.6597C39.3698 53.6613 37.7376 53.1678 36.3488 52.2416C34.96 51.3154 33.8772 49.9981 33.2373 48.4563C32.5973 46.9145 32.4291 45.2176 32.7537 43.5802C33.0784 41.9428 33.8814 40.4384 35.0612 39.2575C36.241 38.0765 37.7446 37.2721 39.3817 36.9458C41.0188 36.6195 42.7158 36.7862 44.2582 37.4246C45.8006 38.063 47.119 39.1446 48.0466 40.5324C48.9741 41.9203 49.4692 43.5521 49.4692 45.2214C49.4676 47.4574 48.5792 49.6016 46.9988 51.1835C45.4185 52.7654 43.2752 53.6559 41.0391 53.6597ZM41.0391 39.8612C39.9786 39.8596 38.9415 40.1726 38.0589 40.7606C37.1764 41.3486 36.4881 42.1852 36.0811 43.1645C35.6742 44.1437 35.5669 45.2218 35.7728 46.2621C35.9787 47.3024 36.4885 48.2582 37.2378 49.0087C37.9871 49.7591 38.9422 50.2704 39.9822 50.4779C41.0222 50.6854 42.1003 50.5798 43.0803 50.1743C44.0602 49.7688 44.8978 49.0818 45.4871 48.2002C46.0765 47.3185 46.3911 46.2819 46.3911 45.2214C46.39 43.8015 45.826 42.44 44.8228 41.4353C43.8196 40.4305 42.459 39.8645 41.0391 39.8612Z\"\r\n        fill=\"white\"\r\n      />\r\n      <path\r\n        d=\"M96.6089 46.761H92.4C91.9918 46.761 91.6003 46.5989 91.3117 46.3102C91.0231 46.0216 90.8609 45.6301 90.8609 45.2219C90.8609 44.8137 91.0231 44.4222 91.3117 44.1336C91.6003 43.845 91.9918 43.6828 92.4 43.6828H96.1739V35.5215C96.1729 34.4181 95.8903 33.3332 95.353 32.3695L87.0214 17.432C86.9502 17.3046 86.8463 17.1984 86.7204 17.1244C86.5945 17.0505 86.4512 17.0114 86.3052 17.0114H74.9508V43.689H78.6159C79.0241 43.689 79.4156 43.8511 79.7042 44.1398C79.9928 44.4284 80.155 44.8199 80.155 45.2281C80.155 45.6363 79.9928 46.0277 79.7042 46.3164C79.4156 46.605 79.0241 46.7672 78.6159 46.7672H73.4117C73.0035 46.7672 72.612 46.605 72.3234 46.3164C72.0348 46.0277 71.8726 45.6363 71.8726 45.2281V15.4723C71.8726 15.0641 72.0348 14.6726 72.3234 14.384C72.612 14.0953 73.0035 13.9332 73.4117 13.9332H86.3052C86.9995 13.9328 87.6814 14.1179 88.2803 14.4693C88.8791 14.8207 89.3733 15.3256 89.7117 15.9319L98.0413 30.8714C98.8332 32.2934 99.2492 33.8939 99.25 35.5215V44.1199C99.2489 44.82 98.9703 45.4912 98.4752 45.9862C97.9802 46.4813 97.309 46.7599 96.6089 46.761Z\"\r\n        fill=\"white\"\r\n      />\r\n      <path\r\n        d=\"M34.1462 46.7606H19.3893C18.9811 46.7606 18.5897 46.5984 18.301 46.3098C18.0124 46.0212 17.8502 45.6297 17.8502 45.2215V33.3849C17.8502 32.9767 18.0124 32.5852 18.301 32.2966C18.5897 32.0079 18.9811 31.8458 19.3893 31.8458C19.7975 31.8458 20.189 32.0079 20.4776 32.2966C20.7663 32.5852 20.9284 32.9767 20.9284 33.3849V43.6824H34.1462C34.5544 43.6824 34.9458 43.8446 35.2345 44.1332C35.5231 44.4218 35.6853 44.8133 35.6853 45.2215C35.6853 45.6297 35.5231 46.0212 35.2345 46.3098C34.9458 46.5984 34.5544 46.7606 34.1462 46.7606Z\"\r\n        fill=\"white\"\r\n      />\r\n      <path\r\n        d=\"M19.3893 27.9262C18.9811 27.9262 18.5897 27.7641 18.301 27.4754C18.0124 27.1868 17.8502 26.7953 17.8502 26.3871V16.0526C17.8502 15.6445 18.0124 15.253 18.301 14.9643C18.5897 14.6757 18.9811 14.5135 19.3893 14.5135C19.7975 14.5135 20.189 14.6757 20.4776 14.9643C20.7663 15.253 20.9284 15.6445 20.9284 16.0526V26.3933C20.9268 26.8004 20.7639 27.1903 20.4755 27.4776C20.187 27.7649 19.7965 27.9262 19.3893 27.9262Z\"\r\n        fill=\"white\"\r\n      />\r\n      <path\r\n        d=\"M73.4115 46.7614H47.9303C47.5221 46.7614 47.1306 46.5992 46.842 46.3106C46.5533 46.0219 46.3912 45.6305 46.3912 45.2223C46.3912 44.8141 46.5533 44.4226 46.842 44.134C47.1306 43.8453 47.5221 43.6832 47.9303 43.6832H71.8724V3.41846H20.9284V9.75747C20.9284 10.1657 20.7663 10.5571 20.4776 10.8458C20.189 11.1344 19.7975 11.2966 19.3893 11.2966C18.9811 11.2966 18.5897 11.1344 18.301 10.8458C18.0124 10.5571 17.8502 10.1657 17.8502 9.75747V3.13526C17.8513 2.39396 18.1464 1.68337 18.6708 1.15938C19.1952 0.635394 19.906 0.340814 20.6473 0.340271H72.1556C72.8966 0.341357 73.6068 0.636176 74.1308 1.1601C74.6547 1.68403 74.9495 2.39432 74.9506 3.13526V45.2223C74.9506 45.6305 74.7884 46.0219 74.4998 46.3106C74.2112 46.5992 73.8197 46.7614 73.4115 46.7614Z\"\r\n        fill=\"white\"\r\n      />\r\n      <path\r\n        d=\"M25.4737 34.924H5.93951C5.53132 34.924 5.13985 34.7618 4.85121 34.4732C4.56258 34.1845 4.40042 33.7931 4.40042 33.3849C4.40042 32.9767 4.56258 32.5852 4.85121 32.2966C5.13985 32.0079 5.53132 31.8458 5.93951 31.8458H25.4737C25.8819 31.8458 26.2734 32.0079 26.562 32.2966C26.8506 32.5852 27.0128 32.9767 27.0128 33.3849C27.0128 33.7931 26.8506 34.1845 26.562 34.4732C26.2734 34.7618 25.8819 34.924 25.4737 34.924Z\"\r\n        fill=\"white\"\r\n      />\r\n      <path\r\n        d=\"M41.0395 27.9254H13.7811C13.3729 27.9254 12.9815 27.7633 12.6928 27.4747C12.4042 27.186 12.242 26.7945 12.242 26.3864C12.242 25.9782 12.4042 25.5867 12.6928 25.298C12.9815 25.0094 13.3729 24.8473 13.7811 24.8473H41.0395C41.4477 24.8473 41.8392 25.0094 42.1278 25.298C42.4164 25.5867 42.5786 25.9782 42.5786 26.3864C42.5786 26.7945 42.4164 27.186 42.1278 27.4747C41.8392 27.7633 41.4477 27.9254 41.0395 27.9254Z\"\r\n        fill=\"white\"\r\n      />\r\n      <path\r\n        d=\"M12.9335 19.7985H2.28909C1.8809 19.7985 1.48943 19.6363 1.20079 19.3477C0.912154 19.059 0.75 18.6676 0.75 18.2594C0.75 17.8512 0.912154 17.4597 1.20079 17.1711C1.48943 16.8824 1.8809 16.7203 2.28909 16.7203H12.9335C13.3417 16.7203 13.7331 16.8824 14.0218 17.1711C14.3104 17.4597 14.4726 17.8512 14.4726 18.2594C14.4726 18.6676 14.3104 19.059 14.0218 19.3477C13.7331 19.6363 13.3417 19.7985 12.9335 19.7985Z\"\r\n        fill=\"white\"\r\n      />\r\n      <path\r\n        d=\"M30.0015 11.2964H11.2123C10.8041 11.2964 10.4126 11.1343 10.124 10.8457C9.83532 10.557 9.67316 10.1655 9.67316 9.75736C9.67316 9.34916 9.83532 8.95769 10.124 8.66905C10.4126 8.38042 10.8041 8.21826 11.2123 8.21826H30.0015C30.4097 8.21826 30.8012 8.38042 31.0898 8.66905C31.3784 8.95769 31.5406 9.34916 31.5406 9.75736C31.5406 10.1655 31.3784 10.557 31.0898 10.8457C30.8012 11.1343 30.4097 11.2964 30.0015 11.2964Z\"\r\n        fill=\"white\"\r\n      />\r\n    </svg>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,WAAW,CAAC,EAAE,OAAO,EAAE,EAAE,QAAQ,MAAM,EAAE;IACpD,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;;0BAEN,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb"}}, {"offset": {"line": 1124, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1130, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/home/<USER>/fruit-gift/index.tsx"], "sourcesContent": ["import { getFirstVariant, getProductPrice } from \"@lib/util/product-helpers\"\r\nimport SaleButton from \"@modules/layout/components/main-nav-content/header/pre-order-button\"\r\nimport ProductItem from \"@modules/products/components/product-item\"\r\nimport { ShipIcon } from \"components/svg/ic-ship\"\r\nimport Typography from \"components/ui/typography\"\r\nimport Image from \"next/image\"\r\nimport { useTranslation } from \"react-i18next\"\r\nimport { Swiper, SwiperSlide } from \"swiper/react\"\r\nimport { TStoreProductWithCustomField } from \"types/product\"\r\nimport { PAGE_PATH } from \"utils/path\"\r\n\r\ninterface FruitGiftSectionProps {\r\n  category: {\r\n    name: string\r\n    products: TStoreProductWithCustomField[]\r\n  }\r\n  countryCode?: string\r\n}\r\n\r\nconst PreOrderOffer = () => {\r\n  const { t } = useTranslation(\"layout\")\r\n\r\n  return (\r\n    <div className=\"flex flex-col items-center gap-y-4 text-center text-white md:w-1/2\">\r\n      <ShipIcon />\r\n      <Typography size=\"lg\" className=\"mt-4 font-bold\">\r\n        {t(\"pre_order_promo\")}\r\n      </Typography>\r\n\r\n      <div className=\"flex flex-col items-center gap-2\">\r\n        <Typography size=\"sm\">\r\n          {t(\"one_day_discount\")} {t(\"discount_3\")}\r\n        </Typography>\r\n        <Typography size=\"sm\">\r\n          {t(\"two_days_discount\")} {t(\"discount_5\")}\r\n        </Typography>\r\n      </div>\r\n      <SaleButton\r\n        label={t(\"pre_order_button\")}\r\n        className=\"w-28 bg-gray-750 hover:bg-gray-800\"\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nconst FruitGiftSection = ({ category, countryCode }: FruitGiftSectionProps) => {\r\n  return (\r\n    <div className=\"flex flex-col gap-4 px-4 md:px-10\">\r\n      <Typography size=\"2xl\" className=\"font-bold text-primary-dark\">\r\n        {category.name}\r\n      </Typography>\r\n      <div className=\"flex flex-col gap-6 md:grid md:grid-cols-3 md:gap-10\">\r\n        {/* Banner Image */}\r\n        <div className=\"md:col-span-1\">\r\n          <Image\r\n            src=\"/images/home/<USER>\"\r\n            alt=\"fruit-gifts-banner\"\r\n            className=\"h-full w-full rounded-lg object-cover\"\r\n            width={0}\r\n            height={0}\r\n            sizes=\"100vw\"\r\n          />\r\n        </div>\r\n        {/* Product Slider & Offer */}\r\n        <div className=\"flex flex-col gap-6 md:col-span-2\">\r\n          <Swiper\r\n            slidesPerView={1.5}\r\n            spaceBetween={10}\r\n            breakpoints={{\r\n              640: { slidesPerView: 2 },\r\n              1024: { slidesPerView: 3 },\r\n            }}\r\n          >\r\n            {category.products.map((product) => {\r\n              const firstVariant = getFirstVariant(product)\r\n              const productPrice = getProductPrice(product)\r\n\r\n              return (\r\n                <SwiperSlide key={product.id}>\r\n                  <ProductItem\r\n                    product_id={product.id}\r\n                    countryCode={countryCode}\r\n                    href={PAGE_PATH.PRODUCT.detail(product.handle || \"\")}\r\n                    image={product.thumbnail || \"\"}\r\n                    title={product.title}\r\n                    description={product.description || \"\"}\r\n                    variant={firstVariant?.id || \"\"}\r\n                    price={productPrice.calculated_amount}\r\n                    fullProduct={product}\r\n                  />\r\n                </SwiperSlide>\r\n              )\r\n            })}\r\n          </Swiper>\r\n          <div className=\"flex flex-wrap items-center justify-center gap-6 rounded-lg bg-[#CBAB7C] p-6 sm:p-10 md:flex-nowrap\">\r\n            <PreOrderOffer />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default FruitGiftSection\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAHA;;;;;;;;;;;AAaA,MAAM,gBAAgB;IACpB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAA,CAAA,WAAQ;;;;;0BACT,8OAAC,sIAAA,CAAA,UAAU;gBAAC,MAAK;gBAAK,WAAU;0BAC7B,EAAE;;;;;;0BAGL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,UAAU;wBAAC,MAAK;;4BACd,EAAE;4BAAoB;4BAAE,EAAE;;;;;;;kCAE7B,8OAAC,sIAAA,CAAA,UAAU;wBAAC,MAAK;;4BACd,EAAE;4BAAqB;4BAAE,EAAE;;;;;;;;;;;;;0BAGhC,8OAAC,qMAAA,CAAA,UAAU;gBACT,OAAO,EAAE;gBACT,WAAU;;;;;;;;;;;;AAIlB;AAEA,MAAM,mBAAmB,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAyB;IACxE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAU;gBAAC,MAAK;gBAAM,WAAU;0BAC9B,SAAS,IAAI;;;;;;0BAEhB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,WAAU;4BACV,OAAO;4BACP,QAAQ;4BACR,OAAM;;;;;;;;;;;kCAIV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0IAAA,CAAA,SAAM;gCACL,eAAe;gCACf,cAAc;gCACd,aAAa;oCACX,KAAK;wCAAE,eAAe;oCAAE;oCACxB,MAAM;wCAAE,eAAe;oCAAE;gCAC3B;0CAEC,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC;oCACtB,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,kBAAe,AAAD,EAAE;oCACrC,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,kBAAe,AAAD,EAAE;oCAErC,qBACE,8OAAC,0IAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,qKAAA,CAAA,UAAW;4CACV,YAAY,QAAQ,EAAE;4CACtB,aAAa;4CACb,MAAM,oHAAA,CAAA,YAAS,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,MAAM,IAAI;4CACjD,OAAO,QAAQ,SAAS,IAAI;4CAC5B,OAAO,QAAQ,KAAK;4CACpB,aAAa,QAAQ,WAAW,IAAI;4CACpC,SAAS,cAAc,MAAM;4CAC7B,OAAO,aAAa,iBAAiB;4CACrC,aAAa;;;;;;uCAVC,QAAQ,EAAE;;;;;gCAchC;;;;;;0CAEF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMb;uCAEe"}}, {"offset": {"line": 1332, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1338, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/svg/ic-why-1.tsx"], "sourcesContent": ["export const IcWhy1 = ({ size = 48, color = \"#fff\", className = \"\" }) => {\r\n  return (\r\n    <svg\r\n      width={size}\r\n      height={(size * 46) / 48}\r\n      viewBox=\"0 0 48 46\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      className={className}\r\n    >\r\n      <path\r\n        d=\"M24.2875 37.0645C24.2875 36.9059 24.1585 36.777 24 36.777C23.8415 36.777 23.7125 36.9059 23.7125 37.0645C23.7125 37.223 23.8415 37.352 24 37.352C24.1585 37.352 24.2875 37.223 24.2875 37.0645Z\"\r\n        fill=\"#373F50\"\r\n        stroke={color}\r\n        strokeWidth=\"1.3\"\r\n      />\r\n      <path\r\n        d=\"M43.9687 28.627C43.9687 31.2154 41.8697 33.3145 39.2812 33.3145C36.6928 33.3145 34.5938 31.2154 34.5938 28.627C34.5938 26.0385 36.6928 23.9395 39.2812 23.9395C41.8697 23.9395 43.9687 26.0385 43.9687 28.627Z\"\r\n        stroke={color}\r\n        strokeWidth=\"1.3\"\r\n        strokeMiterlimit=\"10\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n      <path\r\n        d=\"M13.6855 44.5645H0.935547V42.6895C0.935547 38.5476 4.57492 35.1895 8.7168 35.1895C11.249 35.1895 13.769 36.4438 15.1265 38.3657\"\r\n        stroke={color}\r\n        strokeWidth=\"1.3\"\r\n        strokeMiterlimit=\"10\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n      <path\r\n        d=\"M32.8691 38.3657C34.2266 36.4438 36.7466 35.1895 39.2788 35.1895C43.4207 35.1895 47.0601 38.5476 47.0601 42.6895V44.5645H34.3101\"\r\n        stroke={color}\r\n        strokeWidth=\"1.3\"\r\n        strokeMiterlimit=\"10\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n      <path\r\n        d=\"M13.4043 28.627C13.4043 31.2154 11.3052 33.3145 8.7168 33.3145C6.12836 33.3145 4.0293 31.2154 4.0293 28.627C4.0293 26.0385 6.12836 23.9395 8.7168 23.9395C11.3052 23.9395 13.4043 26.0385 13.4043 28.627Z\"\r\n        stroke={color}\r\n        strokeWidth=\"1.3\"\r\n        strokeMiterlimit=\"10\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n      <path\r\n        d=\"M30.5605 24.875C30.5605 28.4994 27.6224 31.4375 23.998 31.4375C20.3737 31.4375 17.4355 28.4994 17.4355 24.875C17.4355 21.2506 20.3737 18.3125 23.998 18.3125C27.6224 18.3125 30.5605 21.2506 30.5605 24.875Z\"\r\n        stroke={color}\r\n        strokeWidth=\"1.3\"\r\n        strokeMiterlimit=\"10\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n      <path\r\n        d=\"M23.9639 1.4386L21.9361 5.58516L17.4023 6.24328L20.6836 9.44109L19.9092 13.957L23.9639 11.8252C25.4873 12.6258 26.5393 13.1789 28.0196 13.957L27.2442 9.44109L30.5255 6.24328L25.9917 5.58516L23.9639 1.4386Z\"\r\n        stroke={color}\r\n        strokeWidth=\"1.3\"\r\n        strokeMiterlimit=\"10\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n      <path\r\n        d=\"M39.8669 17.2448L42.7534 18.7617L42.2022 15.548L44.5374 13.2708L41.3097 12.802L39.8669 9.87703L38.4231 12.802L35.1953 13.2708L37.5306 15.548L36.9794 18.7617L39.8669 17.2448Z\"\r\n        stroke={color}\r\n        strokeWidth=\"1.3\"\r\n        strokeMiterlimit=\"10\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n      <path\r\n        d=\"M7.99188 17.2448L10.8784 18.7617L10.3272 15.548L12.6625 13.2708L9.43469 12.802L7.99188 9.87703L6.54813 12.802L3.32031 13.2708L5.65563 15.548L5.10438 18.7617L7.99188 17.2448Z\"\r\n        stroke={color}\r\n        strokeWidth=\"1.3\"\r\n        strokeMiterlimit=\"10\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n      <path\r\n        d=\"M34.3105 44.5645H13.6855V43.627C13.6855 41.7051 14.2115 39.906 15.1265 38.3657C16.9246 35.3413 20.2246 33.3145 23.998 33.3145C26.8462 33.3145 29.4243 34.4685 31.2899 36.3351C31.8965 36.9416 32.428 37.6232 32.8696 38.3657C33.7846 39.906 34.3105 41.7051 34.3105 43.627V44.5645Z\"\r\n        stroke={color}\r\n        strokeWidth=\"1.3\"\r\n        strokeMiterlimit=\"10\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n      <path\r\n        d=\"M27.5898 38.1379C28.593 38.797 29.4058 39.7232 29.9252 40.8145\"\r\n        stroke={color}\r\n        strokeWidth=\"1.3\"\r\n        strokeMiterlimit=\"10\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n    </svg>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,SAAS,CAAC,EAAE,OAAO,EAAE,EAAE,QAAQ,MAAM,EAAE,YAAY,EAAE,EAAE;IAClE,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ,AAAC,OAAO,KAAM;QACtB,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,8OAAC;gBACC,GAAE;gBACF,MAAK;gBACL,QAAQ;gBACR,aAAY;;;;;;0BAEd,8OAAC;gBACC,GAAE;gBACF,QAAQ;gBACR,aAAY;gBACZ,kBAAiB;gBACjB,eAAc;gBACd,gBAAe;;;;;;0BAEjB,8OAAC;gBACC,GAAE;gBACF,QAAQ;gBACR,aAAY;gBACZ,kBAAiB;gBACjB,eAAc;gBACd,gBAAe;;;;;;0BAEjB,8OAAC;gBACC,GAAE;gBACF,QAAQ;gBACR,aAAY;gBACZ,kBAAiB;gBACjB,eAAc;gBACd,gBAAe;;;;;;0BAEjB,8OAAC;gBACC,GAAE;gBACF,QAAQ;gBACR,aAAY;gBACZ,kBAAiB;gBACjB,eAAc;gBACd,gBAAe;;;;;;0BAEjB,8OAAC;gBACC,GAAE;gBACF,QAAQ;gBACR,aAAY;gBACZ,kBAAiB;gBACjB,eAAc;gBACd,gBAAe;;;;;;0BAEjB,8OAAC;gBACC,GAAE;gBACF,QAAQ;gBACR,aAAY;gBACZ,kBAAiB;gBACjB,eAAc;gBACd,gBAAe;;;;;;0BAEjB,8OAAC;gBACC,GAAE;gBACF,QAAQ;gBACR,aAAY;gBACZ,kBAAiB;gBACjB,eAAc;gBACd,gBAAe;;;;;;0BAEjB,8OAAC;gBACC,GAAE;gBACF,QAAQ;gBACR,aAAY;gBACZ,kBAAiB;gBACjB,eAAc;gBACd,gBAAe;;;;;;0BAEjB,8OAAC;gBACC,GAAE;gBACF,QAAQ;gBACR,aAAY;gBACZ,kBAAiB;gBACjB,eAAc;gBACd,gBAAe;;;;;;0BAEjB,8OAAC;gBACC,GAAE;gBACF,QAAQ;gBACR,aAAY;gBACZ,kBAAiB;gBACjB,eAAc;gBACd,gBAAe;;;;;;;;;;;;AAIvB"}}, {"offset": {"line": 1489, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1495, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/svg/ic-why-2.tsx"], "sourcesContent": ["export const IcWhy2 = ({ size = 48, color = \"#373F50\" }) => {\r\n  return (\r\n    <svg\r\n      width={size}\r\n      height={size}\r\n      viewBox=\"0 0 48 48\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M34.0156 8.63087L35.7496 9.30447C36.502 9.59669 37.1028 10.1952 37.3978 10.9464L38.089 12.7061C38.2016 12.9932 38.4786 13.1819 38.787 13.1819C39.0955 13.1819 39.3723 12.9931 39.4851 12.7061L40.1763 10.9464C40.4713 10.1952 41.0722 9.59678 41.8245 9.30447L43.5585 8.63087C43.847 8.51884 44.037 8.24124 44.037 7.93177C44.037 7.6223 43.8471 7.3447 43.5585 7.23267L41.8245 6.55907C41.0723 6.26685 40.4714 5.66835 40.1763 4.91703L39.4851 3.15743C39.3723 2.87036 39.0954 2.68164 38.787 2.68164C38.4787 2.68164 38.2017 2.87045 38.089 3.15743L37.3978 4.91703C37.1027 5.66835 36.5019 6.26676 35.7496 6.55907L34.0156 7.23267C33.7271 7.3447 33.5371 7.6223 33.5371 7.93177C33.5371 8.24124 33.727 8.51884 34.0156 8.63087ZM38.7871 5.483C39.229 6.59339 40.1115 7.48186 41.2173 7.93177C40.1115 8.38168 39.229 9.27006 38.7872 10.3804C38.3454 9.27015 37.4628 8.38168 36.357 7.93177C37.4628 7.48186 38.3454 6.59348 38.7871 5.483Z\"\r\n        fill={color}\r\n      />\r\n      <path\r\n        d=\"M2.3408 20.1396L3.78587 20.7008C4.38034 20.9318 4.85499 21.4047 5.08834 21.9984L5.66434 23.4647C5.77712 23.7518 6.05396 23.9405 6.3624 23.9405C6.67084 23.9405 6.94768 23.7517 7.06046 23.4647L7.63655 21.9984C7.86971 21.4047 8.34446 20.9318 8.93902 20.7008L10.384 20.1396C10.6725 20.0275 10.8625 19.7499 10.8625 19.4405C10.8625 19.131 10.6726 18.8534 10.384 18.7414L8.93902 18.1801C8.34446 17.9492 7.8698 17.4762 7.63655 16.8826L7.06046 15.4162C6.94768 15.1291 6.67074 14.9404 6.3624 14.9404C6.05405 14.9404 5.77712 15.1292 5.66434 15.4162L5.08834 16.8825C4.85509 17.4762 4.38034 17.9492 3.78587 18.1801L2.3408 18.7414C2.05234 18.8534 1.8623 19.131 1.8623 19.4405C1.8623 19.7499 2.05224 20.0275 2.3408 20.1396ZM6.3623 17.7082C6.72905 18.4581 7.33674 19.0693 8.08365 19.4405C7.33665 19.8117 6.72905 20.4228 6.3623 21.1727C5.99555 20.4228 5.38787 19.8116 4.64096 19.4405C5.38796 19.0693 5.99555 18.4582 6.3623 17.7082Z\"\r\n        fill={color}\r\n      />\r\n      <path\r\n        d=\"M24.0002 35.8645C30.6904 35.8645 36.1333 30.3895 36.1333 23.6596C36.1333 21.599 35.6114 19.5617 34.6234 17.7639C34.6146 17.7449 34.6049 17.7263 34.5943 17.7078L24.6508 0.376784C24.517 0.143814 24.2689 0 24.0002 0C23.7315 0 23.4834 0.14372 23.3497 0.376784L13.4062 17.7079C13.3958 17.7262 13.386 17.7448 13.3772 17.7638C12.3892 19.5615 11.8672 21.5991 11.8672 23.6598C11.8672 30.3895 17.3101 35.8646 24.0003 35.8646L24.0002 35.8645ZM14.7143 18.4453C14.7207 18.4336 14.727 18.4217 14.7328 18.4097L24.0002 2.25705L33.2676 18.4096C33.2734 18.4216 33.2797 18.4336 33.2862 18.4453C34.1675 20.0324 34.6333 21.8354 34.6333 23.6597C34.6333 29.5623 29.8633 34.3645 24.0002 34.3645C18.1371 34.3645 13.3671 29.5623 13.3671 23.6596C13.3671 21.8354 13.8329 20.0324 14.7143 18.4453Z\"\r\n        fill={color}\r\n      />\r\n      <path\r\n        d=\"M27.7675 31.9624C27.8819 31.9624 27.9982 31.936 28.1072 31.8805C31.2072 30.3014 33.133 27.1516 33.133 23.66C33.133 22.3407 32.844 21.0106 32.2974 19.8135C32.1253 19.4367 31.6804 19.2711 31.3035 19.4428C30.9267 19.6149 30.7608 20.0598 30.9328 20.4367C31.3909 21.4396 31.633 22.5543 31.633 23.6601C31.633 26.5845 30.0211 29.2222 27.4264 30.5439C27.0572 30.732 26.9105 31.1836 27.0985 31.5527C27.2309 31.8127 27.4943 31.9625 27.7675 31.9625V31.9624Z\"\r\n        fill={color}\r\n      />\r\n      <path\r\n        d=\"M29.5033 17.8776C29.6419 18.1192 29.8946 18.2544 30.1545 18.2544C30.2811 18.2544 30.4094 18.2224 30.5271 18.1549C30.8864 17.9487 31.0105 17.4904 30.8044 17.131L29.7005 15.2069C29.4944 14.8477 29.0362 14.7236 28.6767 14.9296C28.3174 15.1357 28.1933 15.5941 28.3994 15.9534L29.5033 17.8776Z\"\r\n        fill={color}\r\n      />\r\n      <path\r\n        d=\"M45.1194 31.3357C43.3853 29.834 40.8797 28.5196 37.8738 27.5345C37.4802 27.4059 37.0566 27.6202 36.9275 28.0137C36.7985 28.4072 37.0131 28.831 37.4066 28.96C43.1005 30.826 46.4999 33.7383 46.4999 36.7502C46.4999 39.2064 44.1787 41.6635 40.1315 43.4913C35.835 45.4316 30.106 46.5002 23.9999 46.5002C17.8938 46.5002 12.1648 45.4317 7.86834 43.4913C3.82116 41.6635 1.49991 39.2064 1.49991 36.7502C1.49991 33.7383 4.89928 30.8261 10.5932 28.96C10.9868 28.831 11.2013 28.4072 11.0723 28.0137C10.9433 27.6201 10.5197 27.4057 10.126 27.5345C7.12013 28.5196 4.61456 29.834 2.88038 31.3357C0.996094 32.9674 0 34.8397 0 36.7502C0 39.8671 2.57513 42.7467 7.25109 44.8583C11.7374 46.8843 17.6856 48.0003 24 48.0003C30.3144 48.0003 36.2627 46.8844 40.7489 44.8583C45.4249 42.7467 48 39.8671 48 36.7502C48 34.8397 47.0039 32.9674 45.1194 31.3357Z\"\r\n        fill={color}\r\n      />\r\n      <path\r\n        d=\"M8.56855 33.7503C8.56855 36.0497 10.2409 38.1703 13.2773 39.7215C16.1539 41.191 19.9617 42.0004 23.9995 42.0004C28.0373 42.0004 31.8452 41.191 34.7217 39.7215C37.7582 38.1703 39.4305 36.0497 39.4305 33.7503C39.4305 32.2695 38.7173 30.8272 37.3679 29.5794C37.0638 29.298 36.5892 29.3165 36.308 29.6207C36.0268 29.9247 36.0453 30.3993 36.3494 30.6806C37.3985 31.6509 37.9304 32.6836 37.9304 33.7503C37.9304 35.4576 36.5485 37.1039 34.0392 38.3857C31.37 39.7493 27.8045 40.5004 23.9994 40.5004C20.1944 40.5004 16.6289 39.7493 13.9596 38.3857C11.4503 37.1038 10.0685 35.4576 10.0685 33.7503C10.0685 32.6836 10.6004 31.6508 11.6495 30.6806C11.9536 30.3993 11.972 29.9247 11.6908 29.6207C11.4097 29.3167 10.9352 29.2982 10.631 29.5794C9.28152 30.8273 8.56836 32.2695 8.56836 33.7503H8.56855Z\"\r\n        fill={color}\r\n      />\r\n    </svg>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,SAAS,CAAC,EAAE,OAAO,EAAE,EAAE,QAAQ,SAAS,EAAE;IACrD,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;;0BAEN,8OAAC;gBACC,GAAE;gBACF,MAAM;;;;;;0BAER,8OAAC;gBACC,GAAE;gBACF,MAAM;;;;;;0BAER,8OAAC;gBACC,GAAE;gBACF,MAAM;;;;;;0BAER,8OAAC;gBACC,GAAE;gBACF,MAAM;;;;;;0BAER,8OAAC;gBACC,GAAE;gBACF,MAAM;;;;;;0BAER,8OAAC;gBACC,GAAE;gBACF,MAAM;;;;;;0BAER,8OAAC;gBACC,GAAE;gBACF,MAAM;;;;;;;;;;;;AAId"}}, {"offset": {"line": 1571, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1577, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/svg/ic-why-3.tsx"], "sourcesContent": ["export const IcWhy3 = ({ size = 48, color = \"#373F50\" }) => {\r\n  return (\r\n    <svg\r\n      width={size}\r\n      height={size - 6}\r\n      viewBox=\"0 0 48 42\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M37.2191 26.3357L43.3522 20.5358C47.6585 16.2295 47.6585 9.24761 43.3522 4.94129C39.0459 0.634982 32.064 0.634982 27.7577 4.94129L19.0721 13.3799C17.61 14.7947 17.5908 17.133 19.0295 18.5716V18.5717C20.4515 19.9937 22.7572 19.9937 24.1792 18.5717L25.734 17.0169C26.3321 16.4187 27.3021 16.4187 27.9002 17.0169L37.5532 26.6698C38.7221 27.8387 38.7221 29.7338 37.5532 30.9026C36.3844 32.0715 34.4893 32.0715 33.3205 30.9026\"\r\n        stroke={color}\r\n        strokeWidth=\"1.5\"\r\n        strokeMiterlimit=\"10\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n      <path\r\n        d=\"M25.999 32.6722L28.6189 35.2921C29.874 36.5472 31.9088 36.5472 33.164 35.2921C34.419 34.037 34.419 32.0021 33.164 30.747L30.5302 28.1132\"\r\n        stroke={color}\r\n        strokeWidth=\"1.5\"\r\n        strokeMiterlimit=\"10\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n      <path\r\n        d=\"M24.5833 7.98342L21.219 4.6192C16.6893 0.0894825 9.34507 0.0894825 4.81526 4.6192C0.285539 9.14901 0.285539 16.4932 4.81526 21.0229L6.0521 22.2598\"\r\n        stroke={color}\r\n        strokeWidth=\"1.5\"\r\n        strokeMiterlimit=\"10\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n      <path\r\n        d=\"M13.9904 37.6224L14.0319 37.6639C15.3094 38.9415 17.3808 38.9415 18.6584 37.6639L20.3324 35.9899C21.61 34.7123 21.61 32.6409 20.3324 31.3634L20.2909 31.3219C19.0133 30.0443 16.942 30.0443 15.6644 31.3219L13.9904 32.9959C12.7128 34.2735 12.7128 36.3449 13.9904 37.6224Z\"\r\n        stroke={color}\r\n        strokeWidth=\"1.5\"\r\n        strokeMiterlimit=\"10\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n      <path\r\n        d=\"M9.25112 33.0257L9.29255 33.0672C10.5702 34.3448 12.6415 34.3448 13.919 33.0672L18.14 28.8464C19.4176 27.5687 19.4176 25.4973 18.14 24.2198L18.0985 24.1784C16.821 22.9007 14.7496 22.9007 13.472 24.1784L9.25112 28.3992C7.97359 29.6768 7.97359 31.7482 9.25112 33.0257Z\"\r\n        stroke={color}\r\n        strokeWidth=\"1.5\"\r\n        strokeMiterlimit=\"10\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n      <path\r\n        d=\"M4.58315 28.3578L4.62459 28.3992C5.90221 29.6768 7.97362 29.6768 9.25115 28.3992L11.8334 25.817C13.111 24.5394 13.111 22.4681 11.8334 21.1905L11.792 21.149C10.5143 19.8714 8.44302 19.8714 7.1654 21.149L4.58315 23.7313C3.30562 25.0088 3.30562 27.0802 4.58315 28.3578Z\"\r\n        stroke={color}\r\n        strokeWidth=\"1.5\"\r\n        strokeMiterlimit=\"10\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n      <path\r\n        d=\"M20.2832 36.0493L24.0728 39.839C25.3279 41.094 27.3627 41.094 28.6179 39.839C29.873 38.5839 29.873 36.5489 28.6179 35.2938\"\r\n        stroke={color}\r\n        strokeWidth=\"1.5\"\r\n        strokeMiterlimit=\"10\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n    </svg>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,SAAS,CAAC,EAAE,OAAO,EAAE,EAAE,QAAQ,SAAS,EAAE;IACrD,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ,OAAO;QACf,SAAQ;QACR,MAAK;QACL,OAAM;;0BAEN,8OAAC;gBACC,GAAE;gBACF,QAAQ;gBACR,aAAY;gBACZ,kBAAiB;gBACjB,eAAc;gBACd,gBAAe;;;;;;0BAEjB,8OAAC;gBACC,GAAE;gBACF,QAAQ;gBACR,aAAY;gBACZ,kBAAiB;gBACjB,eAAc;gBACd,gBAAe;;;;;;0BAEjB,8OAAC;gBACC,GAAE;gBACF,QAAQ;gBACR,aAAY;gBACZ,kBAAiB;gBACjB,eAAc;gBACd,gBAAe;;;;;;0BAEjB,8OAAC;gBACC,GAAE;gBACF,QAAQ;gBACR,aAAY;gBACZ,kBAAiB;gBACjB,eAAc;gBACd,gBAAe;;;;;;0BAEjB,8OAAC;gBACC,GAAE;gBACF,QAAQ;gBACR,aAAY;gBACZ,kBAAiB;gBACjB,eAAc;gBACd,gBAAe;;;;;;0BAEjB,8OAAC;gBACC,GAAE;gBACF,QAAQ;gBACR,aAAY;gBACZ,kBAAiB;gBACjB,eAAc;gBACd,gBAAe;;;;;;0BAEjB,8OAAC;gBACC,GAAE;gBACF,QAAQ;gBACR,aAAY;gBACZ,kBAAiB;gBACjB,eAAc;gBACd,gBAAe;;;;;;;;;;;;AAIvB"}}, {"offset": {"line": 1681, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1687, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/home/<USER>/home-choose/home-choose.tsx"], "sourcesContent": ["\"use client\"\r\nimport { IcWhy1 } from \"components/svg/ic-why-1\"\r\nimport { IcWhy2 } from \"components/svg/ic-why-2\"\r\nimport { IcWhy3 } from \"components/svg/ic-why-3\"\r\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from \"components/ui/tabs\"\r\nimport Typography from \"components/ui/typography\"\r\nimport { motion } from \"framer-motion\"\r\nimport Image from \"next/image\"\r\nimport { useEffect, useState } from \"react\"\r\nimport { useTranslation } from \"react-i18next\"\r\nimport { cn } from \"utils\"\r\n\r\nexport default function HomeChoose() {\r\n  const { t } = useTranslation(\"templates\")\r\n\r\n  const REASONS = [\r\n    {\r\n      key: \"experience\",\r\n      title: t(\"why_choose_us.experience.title\"),\r\n      description: t(\"why_choose_us.experience.description\"),\r\n      icon: IcWhy1,\r\n    },\r\n    {\r\n      key: \"fresh_natural\",\r\n      title: t(\"why_choose_us.fresh_natural.title\"),\r\n      description: t(\"why_choose_us.fresh_natural.description\"),\r\n      icon: IcWhy2,\r\n    },\r\n    {\r\n      key: \"responsible\",\r\n      title: t(\"why_choose_us.responsible.title\"),\r\n      description: t(\"why_choose_us.responsible.description\"),\r\n      icon: IcWhy3,\r\n    },\r\n  ]\r\n\r\n  const [value, setValue] = useState(REASONS[0].title)\r\n  const [isHovered, setIsHovered] = useState<string | null>(null)\r\n\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      const currentIndex = REASONS.findIndex((reason) => reason.title === value)\r\n      const nextIndex = (currentIndex + 1) % REASONS.length\r\n      setValue(REASONS[nextIndex].title)\r\n    }, 5000)\r\n\r\n    return () => clearInterval(interval)\r\n  }, [value, REASONS])\r\n\r\n  return (\r\n    <motion.div\r\n      className=\"mx-auto w-full\"\r\n      initial={{ opacity: 0, y: 30 }}\r\n      whileInView={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.4, ease: \"easeOut\" }}\r\n      viewport={{ once: true, margin: \"-100px\" }}\r\n    >\r\n      <div className=\"relative flex flex-col\">\r\n        <motion.div\r\n          className=\"relative z-10 py-6 text-center\"\r\n          initial={{ opacity: 0, y: -20 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.3, ease: \"easeOut\" }}\r\n          viewport={{ once: true, margin: \"-50px\" }}\r\n        >\r\n          <Typography\r\n            size=\"3xl\"\r\n            className=\"text-center font-bold text-primary-dark\"\r\n          >\r\n            {t(\"why_choose_us.title\")}\r\n          </Typography>\r\n        </motion.div>\r\n\r\n        <Tabs\r\n          value={value}\r\n          onValueChange={setValue}\r\n          className=\"relative z-10 mt-8 flex flex-col gap-y-12 sm:mt-16\"\r\n        >\r\n          <TabsList className=\"mx-auto flex w-full max-w-[900px] grid-cols-3 items-center justify-between gap-4 px-4\">\r\n            {REASONS.map((reason, index) => (\r\n              <motion.div\r\n                key={reason.key}\r\n                initial={{ opacity: 0, scale: 0.95 }}\r\n                whileInView={{ opacity: 1, scale: 1 }}\r\n                transition={{\r\n                  duration: 0.25,\r\n                  delay: index * 0.05,\r\n                  ease: \"easeOut\",\r\n                }}\r\n                viewport={{ once: true, margin: \"-50px\" }}\r\n              >\r\n                <TabsTrigger\r\n                  onMouseEnter={() => setIsHovered(reason.title)}\r\n                  onMouseLeave={() => setIsHovered(null)}\r\n                  value={reason.title}\r\n                  className=\"group flex cursor-pointer flex-col gap-y-2 border-0 data-[state=active]:border-0 data-[state=active]:bg-transparent\"\r\n                >\r\n                  <div\r\n                    className={cn(\r\n                      \"mx-auto w-fit rounded-full border border-gray-200 bg-gray-100/50 p-2\",\r\n                      value === reason.title && \"border-gray-600\"\r\n                    )}\r\n                  >\r\n                    <div\r\n                      className={cn(\r\n                        \"flex h-16 w-16 items-center justify-center rounded-full transition-colors group-hover:bg-primary-main/60 md:h-20 md:w-20\",\r\n                        value === reason.title && \"bg-primary-main\"\r\n                      )}\r\n                    >\r\n                      <reason.icon\r\n                        color={\r\n                          value === reason.title\r\n                            ? \"white\"\r\n                            : isHovered === reason.title\r\n                              ? \"#fff\"\r\n                              : \"#373F50\"\r\n                        }\r\n                        className=\"h-8 w-8 md:h-10 md:w-10\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                  <Typography\r\n                    size=\"base18\"\r\n                    className={cn(\r\n                      \"mt-4 hidden text-center text-sm font-semibold transition-colors group-hover:text-primary-main sm:block md:text-base\",\r\n                      value === reason.title\r\n                        ? \"text-primary-dark\"\r\n                        : \"text-gray-600\"\r\n                    )}\r\n                  >\r\n                    {reason.title}\r\n                  </Typography>\r\n                </TabsTrigger>\r\n              </motion.div>\r\n            ))}\r\n          </TabsList>\r\n\r\n          <TabsContent value={value} className=\"mt-16 rounded-lg bg-yellow-100\">\r\n            {REASONS.map((reason) => (\r\n              <motion.div\r\n                key={reason.key}\r\n                initial={{ opacity: 0, y: 20 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.3, ease: \"easeOut\" }}\r\n                viewport={{ once: true, margin: \"-50px\" }}\r\n                className={value === reason.title ? \"block\" : \"hidden\"}\r\n              >\r\n                <div className=\"mx-auto grid w-full max-w-6xl items-center gap-6 rounded-lg px-4 py-8 sm:px-8 md:grid-cols-2 md:px-16\">\r\n                  <div className=\"flex flex-col gap-y-4 text-justify\">\r\n                    <Typography\r\n                      size=\"2xl\"\r\n                      className=\"text-center text-xl font-bold text-primary-dark sm:text-left md:text-2xl\"\r\n                    >\r\n                      {reason.title}\r\n                    </Typography>\r\n                    <Typography className=\"text-center text-sm !leading-normal text-gray-700 sm:text-justify md:text-base\">\r\n                      {reason.description}\r\n                    </Typography>\r\n                  </div>\r\n                  <div className=\"mt-6 flex justify-center md:mt-0 md:justify-end\">\r\n                    <Image\r\n                      src=\"/images/home/<USER>\"\r\n                      alt=\"Why eFruit\"\r\n                      width={380}\r\n                      height={380}\r\n                      className=\"h-auto w-full rounded-lg\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            ))}\r\n          </TabsContent>\r\n        </Tabs>\r\n      </div>\r\n    </motion.div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AADA;AAHA;AANA;;;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,UAAU;QACd;YACE,KAAK;YACL,OAAO,EAAE;YACT,aAAa,EAAE;YACf,MAAM,2IAAA,CAAA,SAAM;QACd;QACA;YACE,KAAK;YACL,OAAO,EAAE;YACT,aAAa,EAAE;YACf,MAAM,2IAAA,CAAA,SAAM;QACd;QACA;YACE,KAAK;YACL,OAAO,EAAE;YACT,aAAa,EAAE;YACf,MAAM,2IAAA,CAAA,SAAM;QACd;KACD;IAED,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,MAAM,eAAe,QAAQ,SAAS,CAAC,CAAC,SAAW,OAAO,KAAK,KAAK;YACpE,MAAM,YAAY,CAAC,eAAe,CAAC,IAAI,QAAQ,MAAM;YACrD,SAAS,OAAO,CAAC,UAAU,CAAC,KAAK;QACnC,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAO;KAAQ;IAEnB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAS;kBAEzC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;oBAC7C,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAQ;8BAExC,cAAA,8OAAC,sIAAA,CAAA,UAAU;wBACT,MAAK;wBACL,WAAU;kCAET,EAAE;;;;;;;;;;;8BAIP,8OAAC,gIAAA,CAAA,OAAI;oBACH,OAAO;oBACP,eAAe;oBACf,WAAU;;sCAEV,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;sCACjB,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAK;oCACnC,aAAa;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCACpC,YAAY;wCACV,UAAU;wCACV,OAAO,QAAQ;wCACf,MAAM;oCACR;oCACA,UAAU;wCAAE,MAAM;wCAAM,QAAQ;oCAAQ;8CAExC,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCACV,cAAc,IAAM,aAAa,OAAO,KAAK;wCAC7C,cAAc,IAAM,aAAa;wCACjC,OAAO,OAAO,KAAK;wCACnB,WAAU;;0DAEV,8OAAC;gDACC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,wEACA,UAAU,OAAO,KAAK,IAAI;0DAG5B,cAAA,8OAAC;oDACC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,4HACA,UAAU,OAAO,KAAK,IAAI;8DAG5B,cAAA,8OAAC,OAAO,IAAI;wDACV,OACE,UAAU,OAAO,KAAK,GAClB,UACA,cAAc,OAAO,KAAK,GACxB,SACA;wDAER,WAAU;;;;;;;;;;;;;;;;0DAIhB,8OAAC,sIAAA,CAAA,UAAU;gDACT,MAAK;gDACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,uHACA,UAAU,OAAO,KAAK,GAClB,sBACA;0DAGL,OAAO,KAAK;;;;;;;;;;;;mCAjDZ,OAAO,GAAG;;;;;;;;;;sCAwDrB,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAO;4BAAO,WAAU;sCAClC,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,MAAM;oCAAU;oCAC7C,UAAU;wCAAE,MAAM;wCAAM,QAAQ;oCAAQ;oCACxC,WAAW,UAAU,OAAO,KAAK,GAAG,UAAU;8CAE9C,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sIAAA,CAAA,UAAU;wDACT,MAAK;wDACL,WAAU;kEAET,OAAO,KAAK;;;;;;kEAEf,8OAAC,sIAAA,CAAA,UAAU;wDAAC,WAAU;kEACnB,OAAO,WAAW;;;;;;;;;;;;0DAGvB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;;;;;;;;;;;;mCAzBX,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoC/B"}}, {"offset": {"line": 1982, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1988, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/home/<USER>/product-category/index.tsx"], "sourcesContent": ["\"use client\"\r\nimport { getFirstVariant, getProductPrice } from \"@lib/util/product-helpers\"\r\nimport { translateText } from \"@lib/util/text-translator\"\r\nimport LocalizedClientLink from \"@modules/common/components/localized-client-link\"\r\nimport ProductItem from \"@modules/products/components/product-item\"\r\nimport ErrorBoundary from \"components/ui/error-boundary\"\r\nimport Typography from \"components/ui/typography\"\r\nimport { motion } from \"framer-motion\"\r\nimport { useTranslation } from \"react-i18next\"\r\nimport { TStoreProductWithCustomField } from \"types\"\r\nimport { PAGE_PATH } from \"utils/path\"\r\n\r\ninterface ProductCategoryProps {\r\n  category: {\r\n    name: string\r\n    handle: string\r\n    products: TStoreProductWithCustomField[]\r\n  }\r\n  countryCode?: string\r\n  index: number\r\n}\r\n\r\nconst ProductCategory = ({\r\n  category,\r\n  countryCode,\r\n  index,\r\n}: ProductCategoryProps) => {\r\n  const { t, i18n } = useTranslation(\"templates\")\r\n\r\n  return (\r\n    <motion.div\r\n      className=\"flex w-full flex-col gap-6\"\r\n      initial={{ opacity: 0, y: 30 }}\r\n      whileInView={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.3, delay: Math.min(index * 0.1, 0.3) }}\r\n      viewport={{ once: true, margin: \"-100px\" }}\r\n    >\r\n      <div className=\"flex w-full items-center justify-between\">\r\n        <Typography size=\"2xl\" className=\"font-bold text-primary-dark\">\r\n          {translateText(category.name || \"\", i18n.language).text_locale ||\r\n            category.name}\r\n        </Typography>\r\n        <LocalizedClientLink\r\n          href={PAGE_PATH.CATEGORIES.detail(category.handle)}\r\n          className=\"flex items-center justify-center\"\r\n        >\r\n          <Typography\r\n            size=\"base18\"\r\n            className=\"font-semibold text-primary-main underline-offset-4 hover:underline\"\r\n          >\r\n            {t(\"view_more\")}\r\n          </Typography>\r\n        </LocalizedClientLink>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:gap-8 xl:grid-cols-4\">\r\n        {category.products?.slice(0, 8).map((product, prodIndex) => {\r\n          const firstVariant = getFirstVariant(product)\r\n          const productPrice = getProductPrice(product)\r\n\r\n          // Translate title and description using translateText\r\n          const translatedTitle = product?.title\r\n            ? translateText(product.title, i18n.language).text_locale ||\r\n              product.title\r\n            : \"\"\r\n          const translatedDescription = product?.description\r\n            ? translateText(product.description, i18n.language).text_locale ||\r\n              product.description\r\n            : \"\"\r\n\r\n          return (\r\n            <motion.div\r\n              key={product.id}\r\n              initial={{ opacity: 0, y: 15 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{\r\n                duration: 0.25,\r\n                delay: Math.min(prodIndex * 0.05, 0.2),\r\n                ease: \"easeOut\",\r\n              }}\r\n              viewport={{ once: true, margin: \"-50px\" }}\r\n            >\r\n              <ErrorBoundary>\r\n                <ProductItem\r\n                  product_id={product?.id}\r\n                  countryCode={countryCode}\r\n                  href={PAGE_PATH.PRODUCT.detail(product.handle || \"\")}\r\n                  image={product?.thumbnail || \"\"}\r\n                  title={translatedTitle}\r\n                  description={translatedDescription}\r\n                  variant={firstVariant?.id || \"\"}\r\n                  price={productPrice.calculated_amount}\r\n                  fullProduct={product}\r\n                />\r\n              </ErrorBoundary>\r\n            </motion.div>\r\n          )\r\n        })}\r\n      </div>\r\n    </motion.div>\r\n  )\r\n}\r\n\r\nexport default ProductCategory\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AAFA;AADA;AAPA;;;;;;;;;;;AAsBA,MAAM,kBAAkB,CAAC,EACvB,QAAQ,EACR,WAAW,EACX,KAAK,EACgB;IACrB,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE;IAEnC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,OAAO,KAAK,GAAG,CAAC,QAAQ,KAAK;QAAK;QAC/D,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAS;;0BAEzC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,UAAU;wBAAC,MAAK;wBAAM,WAAU;kCAC9B,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,IAAI,IAAI,IAAI,KAAK,QAAQ,EAAE,WAAW,IAC5D,SAAS,IAAI;;;;;;kCAEjB,8OAAC,+KAAA,CAAA,UAAmB;wBAClB,MAAM,oHAAA,CAAA,YAAS,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,MAAM;wBACjD,WAAU;kCAEV,cAAA,8OAAC,sIAAA,CAAA,UAAU;4BACT,MAAK;4BACL,WAAU;sCAET,EAAE;;;;;;;;;;;;;;;;;0BAKT,8OAAC;gBAAI,WAAU;0BACZ,SAAS,QAAQ,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS;oBAC5C,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,kBAAe,AAAD,EAAE;oBACrC,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,kBAAe,AAAD,EAAE;oBAErC,sDAAsD;oBACtD,MAAM,kBAAkB,SAAS,QAC7B,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,KAAK,EAAE,KAAK,QAAQ,EAAE,WAAW,IACvD,QAAQ,KAAK,GACb;oBACJ,MAAM,wBAAwB,SAAS,cACnC,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,WAAW,EAAE,KAAK,QAAQ,EAAE,WAAW,IAC7D,QAAQ,WAAW,GACnB;oBAEJ,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BACV,UAAU;4BACV,OAAO,KAAK,GAAG,CAAC,YAAY,MAAM;4BAClC,MAAM;wBACR;wBACA,UAAU;4BAAE,MAAM;4BAAM,QAAQ;wBAAQ;kCAExC,cAAA,8OAAC,6IAAA,CAAA,UAAa;sCACZ,cAAA,8OAAC,qKAAA,CAAA,UAAW;gCACV,YAAY,SAAS;gCACrB,aAAa;gCACb,MAAM,oHAAA,CAAA,YAAS,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,MAAM,IAAI;gCACjD,OAAO,SAAS,aAAa;gCAC7B,OAAO;gCACP,aAAa;gCACb,SAAS,cAAc,MAAM;gCAC7B,OAAO,aAAa,iBAAiB;gCACrC,aAAa;;;;;;;;;;;uBApBZ,QAAQ,EAAE;;;;;gBAyBrB;;;;;;;;;;;;AAIR;uCAEe"}}, {"offset": {"line": 2135, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2141, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/home/<USER>/home-templates.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport HeroBanner from \"@modules/banners/components/hero-banner\"\r\nimport BlogList from \"@modules/blog/components/blog-list\"\r\nimport ErrorBoundary from \"components/ui/error-boundary\"\r\nimport { useMemo } from \"react\"\r\nimport { T_Banner } from \"types/banner\"\r\nimport { ICategoryWithProducts } from \"types/category\"\r\nimport { T_PostListResp } from \"types/post\"\r\nimport CustomerTrust from \"../components/customer-trust/customer-trust\"\r\nimport FruitGiftSection from \"../components/fruit-gift\"\r\nimport HomeChoose from \"../components/home-choose/home-choose\"\r\nimport ProductCategory from \"../components/product-category\"\r\n\r\ninterface HomeTemplateProps {\r\n  productListByCategory?: ICategoryWithProducts[]\r\n  postsList?: T_PostListResp\r\n  heroBanners?: T_Banner[]\r\n  countryCode?: string\r\n}\r\n\r\nexport const HomeTemplate = ({\r\n  productListByCategory,\r\n  postsList,\r\n  heroBanners,\r\n  countryCode,\r\n}: HomeTemplateProps) => {\r\n  const productListByCategoryNoFruitGifts = useMemo(() => {\r\n    return productListByCategory?.filter(\r\n      (item) => item.handle !== \"qua-tang-trai-cay\"\r\n    )\r\n  }, [productListByCategory])\r\n\r\n  const productListByCategoryFruitGifts = useMemo(() => {\r\n    return productListByCategory?.find(\r\n      (item) => item.handle === \"qua-tang-trai-cay\"\r\n    )\r\n  }, [productListByCategory])\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-y-16\">\r\n      {heroBanners && heroBanners.length > 0 && (\r\n        <HeroBanner\r\n          banners={heroBanners}\r\n          height=\"h-[90vh]\"\r\n          className=\"w-full\"\r\n        />\r\n      )}\r\n      <ErrorBoundary>\r\n        <div className=\"flex flex-col gap-y-16 px-4 sm:px-10\">\r\n          {productListByCategoryNoFruitGifts?.map((item, index) => (\r\n            <ProductCategory\r\n              key={item.id}\r\n              category={item}\r\n              countryCode={countryCode}\r\n              index={index}\r\n            />\r\n          ))}\r\n        </div>\r\n      </ErrorBoundary>\r\n      {productListByCategoryFruitGifts && (\r\n        <FruitGiftSection category={productListByCategoryFruitGifts} />\r\n      )}\r\n      {postsList && <BlogList postsList={postsList} />}\r\n      <HomeChoose />\r\n      <CustomerTrust />\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AAZA;;;;;;;;;;AAqBO,MAAM,eAAe,CAAC,EAC3B,qBAAqB,EACrB,SAAS,EACT,WAAW,EACX,WAAW,EACO;IAClB,MAAM,oCAAoC,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAChD,OAAO,uBAAuB,OAC5B,CAAC,OAAS,KAAK,MAAM,KAAK;IAE9B,GAAG;QAAC;KAAsB;IAE1B,MAAM,kCAAkC,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9C,OAAO,uBAAuB,KAC5B,CAAC,OAAS,KAAK,MAAM,KAAK;IAE9B,GAAG;QAAC;KAAsB;IAE1B,qBACE,8OAAC;QAAI,WAAU;;YACZ,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC,mKAAA,CAAA,UAAU;gBACT,SAAS;gBACT,QAAO;gBACP,WAAU;;;;;;0BAGd,8OAAC,6IAAA,CAAA,UAAa;0BACZ,cAAA,8OAAC;oBAAI,WAAU;8BACZ,mCAAmC,IAAI,CAAC,MAAM,sBAC7C,8OAAC,qKAAA,CAAA,UAAe;4BAEd,UAAU;4BACV,aAAa;4BACb,OAAO;2BAHF,KAAK,EAAE;;;;;;;;;;;;;;;YAQnB,iDACC,8OAAC,+JAAA,CAAA,UAAgB;gBAAC,UAAU;;;;;;YAE7B,2BAAa,8OAAC,8JAAA,CAAA,UAAQ;gBAAC,WAAW;;;;;;0BACnC,8OAAC,yKAAA,CAAA,UAAU;;;;;0BACX,8OAAC,+KAAA,CAAA,UAAa;;;;;;;;;;;AAGpB"}}, {"offset": {"line": 2239, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}