module.exports = {

"[project]/src/i18n/locales/en/layout.json (json)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
__turbopack_export_value__(JSON.parse("{\"loading\":\"Loading...\",\"cart\":{\"title\":\"Shopping Cart\",\"empty\":\"Your shopping cart is empty.\",\"go_to_cart\":\"Go to cart\",\"explore_products\":\"Explore products\",\"remove\":\"Remove\",\"quantity\":\"Quantity\",\"sub_total\":\"Subtotal\",\"total\":\"Total\"},\"footer\":{\"address\":\"Address\",\"phone\":\"Customer Service Hotline\",\"customer_service\":\"Customer Service\",\"buy_online\":\"Buy Online\",\"email\":\"Email\",\"contact\":\"CONTACT\",\"store\":\"Store\",\"store_location\":\"305/9 Nguyen Trong Tuyen Street, Ward 10, Phu Nhuan District, Ho Chi Minh City\",\"company_location\":\"388 Huynh Van Banh Street, Phu Nhuan Ward, Ho Chi Minh City, Vietnam\",\"company_name\":\"EFRUIT TRADING SERVICE COMPANY LIMITED\",\"business_license\":\"Tax Identification Number\",\"business_license_value\":\"**********\",\"email_address_value\":\"<EMAIL>\",\"phone_sales_key\":\"Phone\",\"phone_sales_value\":\"0906 70 70 15\",\"phone_hotline_key\":\"Customer Service Hotline\",\"phone_hotline_value\":\"0906 707015 - 0938 707015\",\"food_safety_standards\":\"Certification: The establishment meets the HACCP standards in accordance with CODEX 2022 (CXC 1-1969, Rev. 2022)\",\"food_safety_certificate_number\":\"Certificate Number\",\"food_safety_certificate_value\":\"ISOQ.************\",\"subscribe_newsletter\":{\"title\":\"SUBSCRIBE FOR NEWS\",\"description\":\"Sign up now and be the first to know about new products, promotions, upcoming events at the store, and many other useful information.\",\"placeholder\":\"Enter email\",\"button\":\"Subscribe\",\"success\":\"Subscription successful!\",\"error\":\"Failed to subscribe.\"},\"copy_right\":\"© Copyright e-Fruit 2013-2025. All rights reserved.\",\"form_contact_us\":{\"title\":\"Contact Us\",\"description\":\"We're here to help you with any questions or concerns you may have. Please fill out the form below and we'll get back to you as soon as possible.\",\"name\":\"Name\",\"name_error\":\"Enter your name.\",\"phone\":\"Phone\",\"phone_error\":\"Enter your phone number.\",\"email\":\"Email\",\"email_error\":\"Enter a valid email.\",\"country\":\"Country\",\"country_error\":\"Select your country.\",\"submit\":\"Submit\",\"submit_success\":\"Request sent successfully!\",\"submit_error\":\"Failed to send request.\"}},\"search\":{\"title\":\"Search\",\"collection\":\"COLLECTION\",\"promotion\":\"PROMOTION\",\"news\":\"NEWS\",\"category\":\"CATEGORY\",\"placeholder\":\"Search by keyword\",\"view_more\":\"View More\"},\"review\":{\"title\":\"Review\",\"review_product\":\"Review Product\",\"button_submit\":\"Submit\",\"review\":\"Review\",\"product_name\":\"Product Name\",\"product_variant\":\"Variant\",\"product_price\":\"Price\",\"review_success\":\"Review successful\",\"review_error\":\"Review failed\"},\"pre_order_promo\":\"Pre-Order Promotion\",\"select_date_alert\":\"Please select a date before confirming!\",\"select_date_time\":\"Select datetime\",\"cart_error\":\"Unable to retrieve or create a cart!\",\"selected_date\":\"Selected date:\",\"saved_pick_time\":\"Saved pick_time in cart.metadata:\",\"update_cart_error\":\"Error updating cart:\",\"general_error\":\"An error occurred. Please try again!\",\"confirmation_success\":\"CONFIRMED SUCCESSFULLY!\",\"pre_order_success_message\":\"You have received the pre-order promotion. \\nChoose your products now to complete the order!\",\"order_now\":\"Order Now\",\"pre_order_online\":\"Preorder online\",\"receive_discount\":\"To receive a discount\",\"one_day_discount\":\"1 day pre-order:\",\"discount_3\":\"3% discount\",\"two_days_discount\":\"2 days pre-order:\",\"discount_5\":\"5% discount\",\"confirm\":\"Confirm\",\"confirmed\":\"✓ Selected\",\"online_only_note\":\"*Discount is only applied when booking online via our website Please specify the delivery time\",\"time_caption\":\"Hours\",\"pre_order_button\":\"Pre-order\"}"));}}),
"[project]/src/i18n/locales/en/templates.json (json)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
__turbopack_export_value__(JSON.parse("{\"loading\":\"Loading...\",\"out_of_stock\":\"Out of stock\",\"choose_a_product\":\"Choose a product\",\"add_to_cart\":\"Add to Cart\",\"add_to_wishlist\":\"Add to wishlist\",\"no_products_found\":\"No products found\",\"browser_does_not_support_video\":\"Sorry, your browser does not support embedded videos.\",\"search_results_for\":\"Search results for\",\"search_products\":\"Search products\",\"no_results\":\"No results found\",\"view_details\":\"View Details\",\"trusted_customers\":\"Trusted Customers\",\"price_range\":\"Price Range\",\"price_from\":\"Price From\",\"view_more\":\"View More\",\"reset\":\"Reset\",\"apply\":\"Apply\",\"page_not_found\":{\"title\":\"Page not found\",\"description\":\"The page you are looking for does not exist.\",\"button\":\"Go to HomePage\"},\"cart_not_found\":{\"title\":\"Page not found\",\"description\":\"The cart you tried to access does not exist. Clear your cookies and try again.\",\"button\":\"Go to frontpage\"},\"modal_search\":{\"title\":\"Search product\",\"search_placeholder\":\"Enter search keyword...\",\"search_button\":\"Search\",\"results\":\"Results\",\"no_results\":\"No products found\",\"enter_search_keyword\":\"Enter search keyword\",\"clear\":\"Clear\",\"searching\":\"Searching...\",\"view_all_results\":\"View all results for \\\"{{query}}\\\"\",\"try_different_keywords\":\"Try searching with different keywords\",\"min_characters\":\"Enter at least 2 characters to search\"},\"cart_sheet\":{\"your_cart\":\"Your Cart\",\"empty_cart\":\"Your cart is empty\",\"sub_total\":\"Subtotal\",\"checkout\":\"Checkout\",\"you_may_also_like\":\"You may also like\",\"remove\":\"Remove\"},\"page_product\":{\"in_stock\":\"In stock\",\"out_of_stock\":\"Out of stock\",\"sold_out\":\"Sold out\",\"add_to_cart\":\"Add to Cart\",\"choose_a_product\":\"Choose a product\"},\"why_choose_us\":{\"title\":\"Why choose us?\",\"experience\":{\"title\":\"Trustworthy with extensive experience\",\"description\":\"eFruit is proud of being a leading brand providing fresh fruits, smoothies and juices for many companies and offices in Ho Chi Minh city.\"},\"fresh_natural\":{\"title\":\"Fresh, clean and natural\",\"description\":\"Using high quality, safety, sustainable and non-toxic chemicals Vietnamese agricultural products is the operation policy of eFruit.\"},\"responsible\":{\"title\":\"Responsible and effective\",\"description\":\"Confident, creative, sustainable operations, always listening and being ready to bring the best services to our customers.\"}},\"all_products\":{\"title\":\"All products\",\"filter_products\":\"Filter products\",\"reset_filter\":\"Reset\",\"price\":\"Price\",\"category\":\"Category\",\"apply\":\"Apply\",\"show\":\"Show\",\"products\":\"Products\",\"sort_by\":\"Sort by\",\"featured\":\"Featured\",\"new_arrival\":\"New arrival\",\"low_to_high\":\"Low to high\",\"high_to_low\":\"High to low\",\"top_rated\":\"Top rated\",\"add_to_wishlist\":\"Add to wishlist\",\"no_product_found\":\"No product found\"},\"home_slider\":{\"title_part1\":\"Healthy\",\"title_highlight1\":\"Food\",\"title_part2\":\"Happy\",\"title_highlight2\":\"Life\",\"description\":\"Fresh quality fruits from Vietnamese specialties to international fruits from countries with advanced agriculture.\"}}"));}}),
"[project]/src/i18n/locales/en/product_detail.json (json)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
__turbopack_export_value__(JSON.parse("{\"product_action\":{\"variant_code\":\"Variant Code\",\"promotion_code\":\"Promotion Code\",\"promotion_expired\":\"Expired\",\"promotion_condition\":\"Condition\",\"subtotal\":\"Subtotal\",\"note\":\"Note\",\"promotion_content\":{\"percentage_title\":\"Discount by percentage\",\"percentage_description\":\"Product apply percentage discount code\",\"fixed_title\":\"Discount by fixed amount\",\"fixed_description\":\"Product apply fixed discount code\"},\"flash_sale\":\"Flash Sale\",\"end_in\":\"End in\",\"low_in_stock\":\"Low in stock\",\"in_stock\":\"In stock\",\"out_of_stock\":\"Out of stock\",\"no_stock_location\":\"No stock location\",\"no_variants_available\":\"No variants available\",\"no_variants_error\":\"This product has no variants available\",\"no_variant_selected\":\"Please select a variant\",\"favorite_product\":\"Favorite Product\",\"guide_size\":\"Guide Size\",\"color\":\"Color\",\"size\":\"Size\",\"quantity\":\"Quantity\",\"buy_x_get_y\":\"Buy 1 Get 1\",\"add_to_cart\":\"Add to Cart\",\"add_to_cart_success\":\"Add to Cart Success\",\"add_to_cart_success_description\":\"has been added to cart\",\"add_to_cart_failed\":\"Add to Cart Failed\",\"buy_now\":\"Buy Now\",\"add_to_wishlist\":\"Add to Wishlist\",\"added_to_wishlist_success\":\"Added to Wishlist Success\",\"added_to_wishlist_failed\":\"Added to Wishlist Failed\",\"remove_from_wishlist\":\"Remove from Wishlist\",\"removed_from_wishlist_success\":\"Removed from Wishlist Success\",\"removed_from_wishlist_failed\":\"Removed from Wishlist Failed\",\"nationwide_delivery\":\"Nationwide delivery\",\"fast_return\":\"Fast Return\",\"attentive_care\":\"Attentive Care\",\"description\":\"Description\",\"default_description\":\"Product description\",\"delivery_policy\":\"Delivery Policy\",\"return_policy\":\"Return Policy\",\"no_content\":\"No content\",\"no_image_available\":\"No image available\",\"mix_and_match\":{\"title\":\"MIX AND MATCH\",\"description\":\"Mix and match with other products to get a discount\",\"size\":\"Size\"},\"product_interest\":\"PRODUCT INTEREST\",\"commonly_purchased_with\":\"COMMONLY PURCHASED WITH\"},\"popup_stores\":{\"exist_in_store\":\"Stores has this product\",\"title\":\"Store has this product\",\"city\":\"City\",\"district\":\"District\",\"address\":\"Address\",\"phone\":\"Phone\",\"time_open\":\"Time Open\",\"view_on_map\":\"View on Map\"},\"popup_guide_side\":{\"guide_size\":\"Guide Size\",\"product_size\":\"Product Size\",\"height\":\"Height\",\"weight\":\"Weight\",\"chest\":\"Chest\",\"waist\":\"Waist\",\"hip\":\"Hip\",\"shoulder\":\"Shoulder\",\"sleeve\":\"Sleeve\",\"form_type\":\"Form Type\",\"size_normal\":\"Normal\",\"size_slim\":\"Slim\",\"size_large\":\"Large\",\"find_size\":\"Find Size\",\"recommend_size\":\"Recommend Size\"},\"review\":{\"title\":\"Reviews\",\"write_review\":\"Write a Review\",\"description_tip\":\"How would you rate the quality of this product?\",\"write_post_review\":\"Write a Post Review\",\"no_review\":\"No review yet\",\"star\":\"Star\",\"variant\":\"Variant\",\"review_title\":\"Review Title\",\"review_content\":\"Review Content\",\"have_comment\":\"Have Comment\",\"have_image\":\"Have Image\",\"all_reviews\":\"All\"}}"));}}),
"[project]/src/i18n/locales/en/cart.json (json)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
__turbopack_export_value__(JSON.parse("{\"home\":\"Home\",\"cart\":\"Cart\",\"cart_count\":\"({count} items)\",\"items\":\"Items\",\"note\":\"Note\",\"order_note\":\"Order Note\",\"product_note\":\"Product Note\",\"no\":\"No\",\"quantity\":\"Quantity\",\"remove\":\"Remove\",\"your_order\":\"Your order\",\"checkout\":\"Checkout\",\"subtotal\":\"Subtotal\",\"discount\":\"Discount\",\"shipping\":\"Shipping fee\",\"shipping_processing\":\"Processing\",\"taxes\":\"VAT\",\"gift_card\":\"Gift card\",\"total\":\"Total\",\"excl_shipping_taxes\":\"(excl. shipping and taxes)\",\"product_interest\":\"PRODUCTS OFTEN BOUGHT WITH\",\"continue_shopping\":\"Continue shopping\",\"empty_cart_message\":\"Your cart is empty\",\"cart_loading\":\"Loading cart information...\",\"sign_in_prompt\":{\"title\":\"Already have an account?\",\"description\":\"Sign in for a better experience\",\"sign_in\":\"Sign in\"},\"proceed_to_checkout\":\"Place order\",\"brewing_note\":\"Note\",\"table_headers\":{\"name\":\"Name\",\"options\":\"Options\",\"quantity\":\"Quantity\",\"price\":\"Price\",\"total_amount\":\"Total amount\"},\"order_button\":\"Order\",\"product_action\":{\"add_to_cart_success\":\"Added to cart successfully\",\"add_to_cart_error\":\"Failed to add to cart\",\"no_variants_error\":\"This product has no variants available\",\"no_variant_selected\":\"Please select a variant\"}}"));}}),
"[project]/src/i18n/locales/en/checkout.json (json)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
__turbopack_export_value__(JSON.parse("{\"cart\":\"Cart\",\"edit\":\"Edit\",\"save\":\"Save\",\"quantity\":\"Quantity\",\"subtotal\":\"Subtotal\",\"proceed_to_checkout\":\"Place order\",\"brewing_note\":\"Note\",\"item(s)\":\"item(s)\",\"steps\":{\"delivery_address\":{\"title\":\"Shipping Information\",\"shipping_address\":\"Shipping Address\",\"billing_address\":\"Billing Address\",\"continue_to_delivery\":\"Continue to Delivery\",\"contact\":\"Contact\",\"greeting\":\"Hi {{name}}, do you want to use one of your saved addresses?\",\"choose_address\":\"Choose an address\",\"fullname\":\"Full Name\",\"form\":{\"first_name\":\"First Name\",\"last_name\":\"Last Name\",\"full_name\":\"Full Name\",\"phone\":\"Phone Number\",\"country_code\":\"Country Code\",\"country\":\"Country\",\"city\":\"City\",\"province\":\"Province/City\",\"district\":\"District\",\"ward\":\"Ward/Commune\",\"address\":\"Specific Address (house number, street name...)\",\"generate_invoice\":\"Invoice Information\",\"company\":\"Company\",\"company_name\":\"Company Name\",\"company_tax_code\":\"Tax Code\",\"company_address\":\"Company Address\",\"postal_code\":\"Postal Code\",\"same_as_billing\":\"Billing address same as shipping address\",\"email\":\"Email\",\"save_address\":\"Remember\",\"note\":\"Delivery Notes\",\"validation\":{\"full_name_required\":\"Please enter full name\",\"phone_required\":\"Please enter phone number\",\"phone_invalid\":\"Invalid phone number\",\"email_required\":\"Please enter email\",\"email_invalid\":\"Invalid email\",\"address_required\":\"Please enter address\",\"province_required\":\"Please select province/city\",\"district_required\":\"Please select district\",\"ward_required\":\"Please select ward\"}}},\"shipping_method\":{\"title\":\"Shipping Method\",\"delivery\":\"Delivery\",\"pickup\":\"Pickup\",\"continue_to_payment\":\"Continue to Payment\",\"method\":\"Method\",\"no_shipping_methods\":\"No shipping methods available\",\"error\":\"Error setting shipping method\"},\"payment\":{\"title\":\"Payment Method\",\"method\":\"Method\",\"details\":\"Details\",\"payment_method\":\"Payment Method\",\"payment_details\":\"Payment Details\",\"another_step_will_appear\":\"Another step will appear\",\"gift_card\":\"Gift Card\",\"enter_card_details\":\"Enter card details\",\"continue_to_review\":\"Continue to review\",\"bank_transfer\":\"Bank transfer\",\"account_name\":\"Account Holder:\",\"bank_name\":\"Bank Name:\",\"account_number\":\"Account Number:\",\"transfer_description\":\"Description:\",\"bank_details\":{\"account_name\":\"CT TNHH TMDV EFRUIT\",\"bank_name\":\"TECHCOMBANK Bank\",\"account_number\":\"250487\",\"transfer_description\":\"eFruit - Your name/company\"}},\"review\":{\"title\":\"Review\",\"complete_order\":\"Complete Order\",\"memberPointPolicy\":\"Member Point Policy\",\"deliveryPolicy\":\"Delivery Policy\",\"privacyPolicy\":\"Privacy Policy\",\"returnPolicy\":\"Return Policy\",\"paymentOnline\":\"Payment Online\",\"content\":\"By clicking the Complete Order button, you confirm that you have read, understand and accept our <deliveryPolicy>Delivery Policy</deliveryPolicy>, <privacyPolicy>Privacy Policy</privacyPolicy> and <returnPolicy>Return Policy</returnPolicy> and acknowledge that you have read <paymentOnline>Payment Online</paymentOnline>.\"}},\"summary\":{\"title\":\"Your Order\",\"back_to_cart\":\"Back to Cart\",\"discount\":\"Discount\",\"discount_code\":\"Discount Code\",\"discount_code_placeholder\":\"Enter your discount code\",\"discount_code_button\":\"Apply\",\"discount_code_error\":\"Invalid discount code\",\"discount_code_success\":\"Discount code applied\",\"discount_code_view\":\"View your discount codes\",\"discount_code_remove_success\":\"Discount code removed\",\"discount_code_remove_error\":\"Failed to remove discount code\",\"subtotal\":\"Subtotal\",\"shipping\":\"Shipping fee\",\"tax\":\"VAT\",\"total\":\"Total\"},\"note\":\"Note\",\"order_note\":\"Order Note\",\"product_note\":\"Product Note\",\"delivery_time\":\"Delivery time\",\"cart_dialog_description\":\"This is your cart dialog. You can review your items, proceed to checkout, or continue shopping.\"}"));}}),
"[project]/src/i18n/locales/en/account.json (json)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
__turbopack_export_value__(JSON.parse("{\"error\":{\"title\":\"Error\",\"invalid_email_or_password\":\"Invalid email or password\",\"email_already_exists\":\"Identity with email already exists\",\"something_went_wrong\":\"Something went wrong\",\"register_failed\":\"Registration failed\"},\"register\":{\"title\":\"Register\",\"haveAccount\":\"Already have an account?\",\"loginNow\":\"Login now\"},\"logout\":{\"title\":\"Logout\"},\"required\":{\"title\":\"This field is required\"},\"signIn\":{\"title\":\"Log in\",\"success\":\"Login successful\",\"noAccount\":\"Don't have an account?\",\"registerNow\":\"Register now\"},\"form_register\":{\"success\":\"Registration successful!\",\"first_name\":\"First Name\",\"last_name\":\"Last Name\",\"email\":\"Email\",\"phone\":\"Phone Number\",\"password\":\"Password\",\"confirmPassword\":\"Confirm Password\",\"agreeTerms\":\"I agree to  <privacyPolicy> the Policy and Terms </privacyPolicy>\",\"validation\":{\"first_name\":\"Invalid first name\",\"last_name\":\"Invalid last name\",\"email\":\"Invalid email\",\"email_invalid\":\"Invalid email\",\"pwd_length\":\"Password must be at least 8 characters\",\"pwd_char\":\"Password must include a character\",\"pwd_letter\":\"Password must include a numeric digit\",\"pwd_special\":\"Password must include a special character\",\"pwd_compare\":\"Passwords do not match\",\"confirmPassword\":\"Passwords do not match\",\"phone\":\"Invalid phone number\",\"phone_invalid\":\"Invalid phone number\"}},\"saveInfo\":{\"title\":\"Save\",\"success\":\"Information updated successfully\",\"error\":\"Update failed\"},\"cancel\":{\"title\":\"Cancel\"},\"profile\":{\"personalInfo\":\"Personal Information\",\"orders\":\"Order History\",\"changePwd\":\"Change Password\"},\"address\":{\"notFound\":\"Address not found\",\"title\":\"Address Book\",\"addBtn\":\"Add New Address\",\"add_success\":\"Address added successfully\",\"add_error\":\"Address addition failed\",\"editBtn\":\"Edit\",\"deleteBtn\":\"Delete\",\"setDefaultBtn\":\"Set as Default\",\"delete_success\":\"Address deleted successfully\",\"delete_error\":\"Address deletion failed\",\"update_success\":\"Address updated successfully\",\"update_error\":\"Address update failed\",\"please_select\":\"Please select...\",\"info\":{\"addAddress\":\"Add New Address\",\"editAddress\":\"Edit Address\",\"first_name\":\"First Name\",\"last_name\":\"Last Name\",\"company\":\"Company\",\"email\":\"Email\",\"address\":\"Address\",\"residence\":\"Apartment, residence, etc.\",\"postalCode\":\"Postal Code\",\"city\":\"City - District - County\",\"province\":\"Province\",\"ward\":\"Ward - Commune\",\"country\":\"Country\",\"phone\":\"Phone Number\",\"defaultShipping\":\"Default\",\"setAsDefault\":\"Set as Default\"}},\"resetPwd\":{\"sendBtn\":\"Send email\",\"sendEmailSuccess\":\"Password reset email sent successfully\",\"sendEmailError\":\"Password reset email failed to send\"},\"greeting\":\"Greetings\",\"order\":{\"review\":\"Review\",\"productReview\":\"Product Review\",\"reviewSuccess\":\"Review successful\",\"reviewError\":\"Review failed\",\"all\":\"All\",\"processing\":\"Processing\",\"completed\":\"Completed\",\"canceled\":\"Cancelled\",\"delivering\":\"Delivering\",\"search\":\"Search by product name or order id\",\"searchBtn\":\"Search\",\"notFound\":\"Order not found\",\"quantity\":\"Quantity\",\"total\":\"Total\",\"giveReview\":\"Give a review\",\"seeDetails\":\"See details\",\"payment_status\":{\"not_paid\":\"Not Paid\",\"awaiting\":\"Awaiting\",\"authorized\":\"Authorized\",\"partially_authorized\":\"Partially Authorized\",\"captured\":\"Captured\",\"partially_captured\":\"Partially Captured\",\"partially_refunded\":\"Partially Refunded\",\"refunded\":\"Refunded\",\"canceled\":\"Canceled\",\"requires_action\":\"Requires Action\"},\"status\":{\"not_fulfilled\":\"Not Fulfilled\",\"partially_fulfilled\":\"Partially Fulfilled\",\"fulfilled\":\"Fulfilled\",\"partially_shipped\":\"Partially Shipped\",\"shipped\":\"Shipped\",\"partially_delivered\":\"Partially Delivered\",\"delivered\":\"Delivered\",\"canceled\":\"Canceled\"},\"detail\":{\"orderId\":\"Order ID\",\"orderDate\":\"Order Date\",\"orderStatus\":\"Order Status\",\"customerDetails\":\"Customer Details\",\"purchasedProducts\":\"Purchased Products\",\"variant\":\"Variant\",\"noVariant\":\"No variant\",\"paymentMethod\":\"Payment Method\",\"status\":\"Status\",\"productPaied\":\"Product Paied\",\"review\":\"Review\",\"reviewSuccess\":\"Review successful\",\"reviewError\":\"Review failed\",\"summary\":{\"subTotal\":\"Subtotal\",\"shipping\":\"Shipping\",\"taxes\":\"Taxes\",\"total\":\"Total\",\"discount\":\"Discount\"}},\"more\":\"See more\",\"noOrders\":\"Nothing to see here\",\"noOrdersMessage\":\"You don't have any orders yet, let us change that :(\",\"continueShopping\":\"Continue shopping\"}}"));}}),
"[project]/src/i18n/locales/en/order_confirm.json (json)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
__turbopack_export_value__(JSON.parse("{\"title\":\"Your Purchase Is Successful\",\"order_confirmed\":\"ORDER CONFIRMED\",\"order_number\":\"Order ID\",\"thanks_message\":\"Thank you for shopping at eFruit.vn\",\"content\":\"Once the store confirms your order, the product will be delivered to you at the scheduled time. eFruit is honored to serve you!\",\"continue_shopping\":\"Continue Shopping\",\"order_load_error\":\"Unable to load order information\",\"order_processing\":\"Please wait a moment, we are processing...\",\"order_loading\":\"Loading order information\",\"order_not_found\":\"Order not found\",\"error_occurred\":\"An error occurred, please try again later.\",\"shipping_details\":\"1. Shipping Details\",\"delivery_method\":\"2. Delivery Method\",\"payment_method\":{\"title\":\"3. Payment Method\",\"credit_card\":\"Credit Card\",\"paypal\":\"PayPal\",\"ideal\":\"iDeal\",\"bancontact\":\"Bancontact\",\"manual_payment\":\"Manual Payment\"},\"summary\":{\"title\":\"Your Order\",\"quantity\":\"Quantity\",\"variant\":\"Variant\",\"product\":\"Product\",\"price\":\"Subtotal\",\"discount\":\"Discount\",\"shipping\":\"Shipping\",\"taxes\":\"Taxes\",\"total\":\"Total\",\"reviewed\":\"Reviewed\"}}"));}}),
"[project]/src/i18n/locales/en/wishlist.json (json)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
__turbopack_export_value__(JSON.parse("{\"title\":\"Favorite Products\",\"noItems\":\"No products in wishlist\",\"goToStore\":\"Go to store\",\"addToWishlist\":\"Add to wishlist\",\"addToWishlistSuccess\":\"Added to wishlist\",\"addToWishlistError\":\"Error adding to wishlist\",\"removeFromWishlist\":\"Remove from wishlist\",\"removeFromWishlistSuccess\":\"Removed from wishlist\",\"removeFromWishlistError\":\"Error removing from wishlist\",\"add\":\"Add\",\"remove\":\"Remove\"}"));}}),
"[project]/src/i18n/locales/en/blog.json (json)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
__turbopack_export_value__(JSON.parse("{\"news\":\"NEWS\",\"category\":\"Category\",\"no_posts_found\":\"No posts found for this category\",\"featured\":{\"title\":\"Featured articles\",\"load_error\":\"Error loading featured articles\"},\"all_blogs\":{\"title\":\"News & Promotions\",\"load_error\":\"Error loading all blogs\"},\"menu_category\":{\"all\":\"All\",\"search_placeholder\":\"Search by title\",\"category_placeholder\":\"Select category\"}}"));}}),
"[project]/src/i18n/locales/en/collections.json (json)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
__turbopack_export_value__(JSON.parse("{\"title\":\"All products\",\"filter\":\"Filter\",\"filter_products\":\"Filter products\",\"reset_filter\":\"Reset\",\"price\":\"Price\",\"category\":\"Category\",\"apply\":\"Apply\",\"remove\":\"Remove\",\"show\":\"Show\",\"products\":\"Products\",\"sort_menu\":{\"title\":\"Sort by\",\"newest\":\"Newest\",\"featured\":\"Featured\",\"price_asc\":\"Price ascending\",\"price_desc\":\"Price descending\"},\"add_to_wishlist\":\"Add to wishlist\",\"no_product_found\":\"No product found\",\"filter_menu\":{\"all\":\"All\",\"remove\":\"Remove\",\"remove_all\":\"Remove all\",\"sort_by\":\"Sort by\",\"category\":\"Category\",\"program\":\"Program\",\"size\":\"Size\",\"color\":\"Color\",\"price\":\"Price\",\"newest\":\"Newest\",\"featured\":\"Featured\",\"price_asc\":\"Price ascending\",\"price_desc\":\"Price descending\",\"latest_arrivals\":\"Latest Arrivals\",\"price_low_to_high\":\"Price: Low → High\",\"price_high_to_low\":\"Price: High → Low\"}}"));}}),
"[project]/src/i18n/locales/vi/layout.json (json)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
__turbopack_export_value__(JSON.parse("{\"loading\":\"Đang tải...\",\"cart\":{\"title\":\"Giỏ hàng\",\"empty\":\"Giỏ hàng của bạn đang trống.\",\"go_to_cart\":\"Xem chi tiết giỏ hàng\",\"explore_products\":\"Khám phá sản phẩm\",\"remove\":\"Xóa\",\"quantity\":\"Số lượng\",\"sub_total\":\"Tạm tính\",\"total\":\"Tổng cộng\"},\"footer\":{\"address\":\"Địa chỉ\",\"phone\":\"Sđt mua hàng\",\"customer_service\":\"Dịch vụ khách hàng\",\"buy_online\":\"Mua online\",\"email\":\"Email\",\"contact\":\"LIÊN HỆ\",\"store\":\"Cửa hàng\",\"store_location\":\"305/9 Nguyễn Trọng Tuyển, Phường 10, Quận Phú Nhuận, TP. HCM\",\"company_location\":\"388 Huỳnh Văn Bánh, Phường Phú Nhuận, TP Hồ Chí Minh, Việt Nam\",\"company_name\":\"CÔNG TY TNHH THƯƠNG MẠI DỊCH VỤ EFRUIT\",\"business_license\":\"MST\",\"business_license_value\":\"**********\",\"email_address_value\":\"<EMAIL>\",\"phone_sales_key\":\"Điện thoại\",\"phone_sales_value\":\"0906 70 70 15\",\"phone_hotline_key\":\"Sđt hotline\",\"phone_hotline_value\":\"0906 707015 & 0938 707015\",\"food_safety_standards\":\"Cơ sở đủ điều kiện tiêu chuẩn HACCP CODEX 2022 (CXC 1-1969, Rev, 2022)\",\"food_safety_certificate_number\":\"Giấy chứng nhận số\",\"food_safety_certificate_value\":\"ISOQ.************\",\"subscribe_newsletter\":{\"title\":\"ĐĂNG KÝ NHẬN TIN\",\"description\":\"Đăng ký ngay và là người đầu tiên nắm được thông tin khi có mặt hàng mới, khuyến mãi, các sự kiện sắp diễn ra tại cửa hàng và nhiều thông tin hữu ích khác.\",\"placeholder\":\"Nhập địa chỉ email\",\"button\":\"Đăng ký\",\"success\":\"Đăng ký thành công!\",\"error\":\"Đăng ký thất bại.\"},\"copy_right\":\"© Copyright e-Fruit 2013-2025. All rights reserved.\",\"form_contact_us\":{\"title\":\"Liên hệ\",\"description\":\"Chúng tôi sẵn sàng giúp bạn với bất kỳ câu hỏi hoặc lo ngại nào. Vui lòng điền vào biểu mẫu bên dưới và chúng tôi sẽ trả lời bạn sớm nhất có thể.\",\"name\":\"Tên\",\"name_error\":\"Nhập tên của bạn.\",\"phone\":\"Điện thoại\",\"phone_error\":\"Nhập số điện thoại của bạn.\",\"email\":\"Email\",\"email_error\":\"Nhập địa chỉ email hợp lệ.\",\"country\":\"Quốc gia\",\"country_error\":\"Chọn quốc gia của bạn.\",\"submit\":\"Gửi\",\"submit_success\":\"Yêu cầu đã được gửi thành công!\",\"submit_error\":\"Yêu cầu thất bại.\"}},\"search\":{\"title\":\"Tìm kiếm\",\"collection\":\"BỘ SƯU TẬP\",\"promotion\":\"KHUYẾN MÃI\",\"news\":\"TIN TỨC\",\"category\":\"LOẠI SẢN PHẨM\",\"placeholder\":\"Tìm kiếm theo từ khóa\",\"view_more\":\"Xem thêm\"},\"review\":{\"title\":\"Đánh giá sản phẩm\",\"review\":\"Đánh giá\",\"button_submit\":\"Đánh giá\",\"review_product\":\"Đánh giá sản phẩm\",\"product_name\":\"Tên sản phẩm\",\"product_variant\":\"Phân loại\",\"product_price\":\"Giá\",\"review_success\":\"Đánh giá thành công\",\"review_error\":\"Đánh giá thất bại\"},\"pre_order_promo\":\"Ưu đãi đặt trước\",\"select_date_alert\":\"Vui lòng chọn ngày trước khi xác nhận!\",\"select_date_time\":\"Chọn ngày đặt\",\"cart_error\":\"Không thể lấy hoặc tạo giỏ hàng!\",\"selected_date\":\"Ngày đã chọn:\",\"saved_pick_time\":\"Đã lưu pick_time vào cart.metadata:\",\"update_cart_error\":\"Lỗi khi cập nhật giỏ hàng:\",\"general_error\":\"Có lỗi xảy ra. Vui lòng thử lại!\",\"confirmation_success\":\"XÁC NHẬN THÀNH CÔNG!\",\"pre_order_success_message\":\"Bạn đã nhận được ưu đãi đặt trước. \\nHãy chọn sản phẩm ngay để hoàn tất đơn hàng!\",\"order_now\":\"Đi đặt hàng ngay\",\"pre_order_online\":\"ĐẶT HÀNG ONLINE TRƯỚC\",\"receive_discount\":\"ĐỂ NHẬN KHUYẾN MÃI\",\"one_day_discount\":\"Đặt trước 1 ngày:\",\"discount_3\":\"giảm 3% tổng hóa đơn\",\"two_days_discount\":\"Đặt trước 2 ngày trở lên:\",\"discount_5\":\"giảm 5% tổng hóa đơn\",\"confirm\":\"Xác nhận\",\"confirmed\":\"✓ Đã chọn\",\"online_only_note\":\"* Chương trình chỉ áp dụng khi \\nđặt Online tại website\",\"time_caption\":\"Giờ\",\"pre_order_button\":\"Đặt trước\"}"));}}),
"[project]/src/i18n/locales/vi/templates.json (json)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
__turbopack_export_value__(JSON.parse("{\"loading\":\"Đang tải...\",\"out_of_stock\":\"Hết hàng\",\"choose_a_product\":\"Chọn sản phẩm\",\"add_to_cart\":\"Mua ngay\",\"add_to_wishlist\":\"Thêm vào danh sách yêu thích\",\"no_products_found\":\"Không tìm thấy sản phẩm\",\"browser_does_not_support_video\":\"Sorry, uw browser ondersteunt geen ingesloten video's.\",\"search_results_for\":\"Kết quả tìm kiếm\",\"search_products\":\"Tìm kiếm sản phẩm\",\"no_results\":\"Không tìm thấy kết quả\",\"view_details\":\"Xem chi tiết\",\"trusted_customers\":\"Khách hàng tin tưởng\",\"price_range\":\"Mức giá\",\"price_from\":\"Giá từ\",\"view_more\":\"Xem thêm\",\"reset\":\"Đặt lại\",\"apply\":\"Áp dụng\",\"page_not_found\":{\"title\":\"Trang không tồn tại\",\"description\":\"Trang bạn tìm kiếm không tồn tại.\",\"button\":\"Trở về trang chủ\"},\"cart_not_found\":{\"title\":\"Trang không tồn tại\",\"description\":\"Giỏ hàng bạn cố gắng truy cập không tồn tại. Xóa cookies và thử lại.\",\"button\":\"Về trang chủ\"},\"modal_search\":{\"title\":\"Tìm kiếm sản phẩm\",\"search_placeholder\":\"Nhập từ khóa tìm kiếm...\",\"search_button\":\"Tìm kiếm\",\"results\":\"Kết quả\",\"no_results\":\"Không tìm thấy sản phẩm nào\",\"enter_search_keyword\":\"Nhập từ khóa để tìm kiếm\",\"clear\":\"Xoá\",\"searching\":\"Đang tìm kiếm...\",\"view_all_results\":\"Xem tất cả kết quả cho \\\"{{query}}\\\"\",\"try_different_keywords\":\"Thử tìm kiếm với từ khóa khác\",\"min_characters\":\"Nhập ít nhất 2 ký tự để tìm kiếm\"},\"cart_sheet\":{\"your_cart\":\"Giỏ hàng\",\"empty_cart\":\"Giỏ hàng của bạn đang trống\",\"sub_total\":\"Giá tạm tính\",\"checkout\":\"Thanh toán\",\"you_may_also_like\":\"Sản phẩm khác\",\"add_to_cart\":\"Mua ngay\",\"remove\":\"Xóa\"},\"page_product\":{\"in_stock\":\"Còn hàng\",\"out_of_stock\":\"Hết hàng\",\"sold_out\":\"Hết hàng\",\"add_to_cart\":\"Mua ngay\",\"choose_a_product\":\"Chọn sản phẩm\"},\"why_choose_us\":{\"title\":\"Tại sao chọn eFruit?\",\"experience\":{\"title\":\"Uy tín nhiều kinh nghiệm\",\"description\":\"e-Fruit tự hào là thương hiệu tiên phong trong dịch vụ cung cấp sản phẩm, giải pháp về trái cây tươi an toàn đến nhiều công ty, văn phòng tại Hồ Chí Minh\"},\"fresh_natural\":{\"title\":\"Tươi sạch tự nhiên\",\"description\":\"Ủng hộ nông sản Việt chất lượng, an toàn, canh tác bền vững, không hóa chất độc hại bảo vệ môi sinh là tiêu chí của eFruit.\"},\"responsible\":{\"title\":\"Trách nhiệm hiệu quả\",\"description\":\"Tự tin, sáng tạo, hoạt động bền vững, luôn lắng nghe và sẵn sàng đem đến những trải nghiệm tốt nhất cho khách hàng.\"}},\"all_products\":{\"title\":\"Tất cả sản phẩm\",\"filter_products\":\"Lọc sản phẩm\",\"reset_filter\":\"Đặt lại\",\"price\":\"Giá\",\"category\":\"Danh mục sản phẩm\",\"apply\":\"Áp dụng\",\"show\":\"Hiển thị\",\"products\":\"Sản phẩm\",\"sort_by\":\"Sắp xếp\",\"featured\":\"Nổi bật\",\"new_arrival\":\"Mới về\",\"low_to_high\":\"Thấp đến cao\",\"high_to_low\":\"Cao đến thấp\",\"top_rated\":\"Top đánh giá\",\"add_to_wishlist\":\"Thêm vào danh sách yêu thích\",\"no_product_found\":\"Không tìm thấy sản phẩm\"},\"home_slider\":{\"title_part1\":\"Thực phẩm\",\"title_highlight1\":\"Lành\",\"title_part2\":\"Vui sống\",\"title_highlight2\":\"Khỏe\",\"description\":\"Trái cây tươi chất lượng từ các loại đặc sản Việt đến ngoại tại các quốc gia có nền nông nghiệp tiên tiến.\"}}"));}}),
"[project]/src/i18n/locales/vi/product_detail.json (json)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
__turbopack_export_value__(JSON.parse("{\"product_action\":{\"variant_code\":\"Mã sản phẩm\",\"promotion_code\":\"Mã giảm giá\",\"promotion_expired\":\"HSD\",\"promotion_condition\":\"Điều kiện\",\"subtotal\":\"Giá tạm tính\",\"note\":\"Ghi chú\",\"promotion_content\":{\"percentage_title\":\"Giảm giá theo phần trăm\",\"percentage_description\":\"Sản phẩm áp dụng mã giảm giá theo phần trăm\",\"fixed_title\":\"Giảm giá theo số tiền cố định\",\"fixed_description\":\"Sản phẩm áp dụng mã giảm giá theo số tiền cố định\"},\"flash_sale\":\"Flash Sale\",\"end_in\":\"Kết thúc trong\",\"low_in_stock\":\"Còn ít hàng\",\"in_stock\":\"Còn hàng\",\"out_of_stock\":\"Hết hàng\",\"no_stock_location\":\"Không có kho hàng\",\"no_variants_available\":\"Không có phiên bản sản phẩm\",\"no_variants_error\":\"Sản phẩm này không có phiên bản nào\",\"no_variant_selected\":\"Vui lòng chọn phiên bản sản phẩm\",\"favorite_product\":\"Sản phẩm yêu thích\",\"guide_size\":\"Hướng dẫn size\",\"color\":\"Màu sắc\",\"size\":\"Size\",\"quantity\":\"Số lượng\",\"buy_x_get_y\":\"Mua 1 tặng 1\",\"add_to_cart\":\"Thêm vào giỏ hàng\",\"add_to_cart_success\":\"Thêm vào giỏ hàng thành công\",\"add_to_cart_success_description\":\"đã được thêm vào giỏ hàng\",\"add_to_cart_failed\":\"Thêm vào giỏ hàng thất bại\",\"buy_now\":\"Mua ngay\",\"add_to_wishlist\":\"Thêm vào danh sách yêu thích\",\"added_to_wishlist_success\":\"Thêm vào danh sách yêu thích thành công\",\"added_to_wishlist_failed\":\"Thêm vào danh sách yêu thích thất bại\",\"remove_from_wishlist\":\"Xóa khỏi danh sách yêu thích\",\"removed_from_wishlist_success\":\"Xóa khỏi danh sách yêu thích thành công\",\"removed_from_wishlist_failed\":\"Xóa khỏi danh sách yêu thích thất bại\",\"nationwide_delivery\":\"Giao hàng toàn quốc\",\"fast_return\":\"Đổi trả nhanh chóng\",\"attentive_care\":\"Chăm sóc tận tình\",\"description\":\"Mô tả sản phẩm\",\"default_description\":\"Mô tả sản phẩm\",\"delivery_policy\":\"Chính sách giao hàng\",\"return_policy\":\"Chính sách đổi hàng\",\"no_content\":\"Không có nội dung\",\"no_image_available\":\"Không có hình ảnh\",\"mix_and_match\":{\"title\":\"MIX AND MATCH\",\"description\":\"Kết hợp với sản phẩm khác để được giảm giá\",\"size\":\"Kích cỡ\"},\"product_interest\":\"SẢN PHẨM LIÊN QUAN\",\"commonly_purchased_with\":\"SẢN PHẨM THƯỜNG ĐƯỢC MUA KÈM\"},\"popup_stores\":{\"exist_in_store\":\"cửa hàng có sản phẩm này\",\"title\":\"Cửa hàng có sản phẩm này\",\"city\":\"Tỉnh/Thành phố\",\"district\":\"Quận/Huyện\",\"address\":\"Địa chỉ\",\"phone\":\"Số điện thoại\",\"time_open\":\"Giờ hoạt động\",\"view_on_map\":\"Xem bản đồ\"},\"popup_guide_side\":{\"guide_size\":\"Hướng dẫn chọn size\",\"product_size\":\"Số đo sản phẩm\",\"height\":\"Chiều cao\",\"weight\":\"Cân nặng\",\"chest\":\"Vòng ngực\",\"waist\":\"Vòng eo\",\"hip\":\"Vòng mông\",\"shoulder\":\"Vòng vai\",\"sleeve\":\"Dài tay\",\"form_type\":\"Nhu cầu mặc\",\"size_normal\":\"Size bình thường\",\"size_slim\":\"Size slim\",\"size_large\":\"Size lớn\",\"find_size\":\"Tìm size\",\"recommend_size\":\"Size gợi ý cho bạn\"},\"review\":{\"title\":\"Đánh giá\",\"write_review\":\"Viết đánh giá\",\"description_tip\":\"Bạn đánh giá chất lượng sản phẩm này như thế nào?\",\"write_post_review\":\"Viết bài đánh giá\",\"star\":\"Sao\",\"variant\":\"Phân loại: \",\"review_title\":\"Tiêu đề đánh giá\",\"review_content\":\"Nội dung đánh giá\",\"have_comment\":\"Có bình luận\",\"have_image\":\"Có ảnh\",\"all_reviews\":\"Tất cả\",\"no_review\":\"Chưa có đánh giá\"}}"));}}),
"[project]/src/i18n/locales/vi/cart.json (json)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
__turbopack_export_value__(JSON.parse("{\"home\":\"Trang chủ\",\"cart\":\"Giỏ hàng\",\"cart_count\":\"({count} sản phẩm)\",\"items\":\"Sản phẩm\",\"note\":\"Ghi chú\",\"product_note\":\"Ghi chú sản phẩm\",\"order_note\":\"Ghi chú đơn hàng\",\"no\":\"Không\",\"quantity\":\"Số lượng\",\"remove\":\"Xóa sản phẩm\",\"your_order\":\"Đơn hàng\",\"checkout\":\"Thanh toán\",\"subtotal\":\"Giá tạm tính\",\"discount\":\"Giảm giá\",\"shipping\":\"Phí giao hàng\",\"shipping_processing\":\"Đang xử lý\",\"taxes\":\"Thuế VAT\",\"gift_card\":\"Thẻ quà tặng\",\"total\":\"Tổng cộng\",\"excl_shipping_taxes\":\"(không bao gồm phí vận chuyển và thuế)\",\"product_interest\":\"SẢN PHẨM THƯỜNG ĐƯỢC MUA KÈM\",\"continue_shopping\":\"Tiếp tục mua sắm\",\"empty_cart_message\":\"Giỏ hàng của bạn đang trống\",\"cart_loading\":\"Đang tải thông tin giỏ hàng...\",\"sign_in_prompt\":{\"title\":\"Đã có tài khoản?\",\"description\":\"Đăng nhập để có trải nghiệm tốt hơn\",\"sign_in\":\"Đăng nhập\"},\"proceed_to_checkout\":\"Tiến hành đặt hàng\",\"brewing_note\":\"Ghi chú\",\"table_headers\":{\"name\":\"Tên món\",\"options\":\"Tùy chọn\",\"quantity\":\"Số lượng\",\"price\":\"Giá\",\"total_amount\":\"Thành tiền\"},\"order_button\":\"Đặt hàng\",\"product_action\":{\"add_to_cart_success\":\"Thêm vào giỏ hàng thành công\",\"add_to_cart_error\":\"Thêm vào giỏ hàng thất bại\",\"no_variants_error\":\"Sản phẩm này không có phiên bản nào\",\"no_variant_selected\":\"Vui lòng chọn phiên bản sản phẩm\"}}"));}}),
"[project]/src/i18n/locales/vi/checkout.json (json)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
__turbopack_export_value__(JSON.parse("{\"cart\":\"Giỏ hàng\",\"edit\":\"Chỉnh sửa\",\"save\":\"Lưu\",\"proceed_to_checkout\":\"Tiến hành đặt hàng\",\"subtotal\":\"Giá tạm tính\",\"brewing_note\":\"Ghi chú\",\"empty_cart_message\":\"trống\",\"item(s)\":\"sản phẩm\",\"steps\":{\"delivery_address\":{\"title\":\"Thông tin giao hàng\",\"billing_address\":\"Địa chỉ gửi hoá đơn\",\"shipping_address\":\"Địa chỉ giao hàng\",\"continue_to_delivery\":\"Tiếp tục giao hàng\",\"contact\":\"Liên hệ\",\"greeting\":\"Chào {{name}}, bạn có muốn sử dụng một trong các địa chỉ đã lưu không?\",\"choose_address\":\"Chọn địa chỉ\",\"fullname\":\"Họ và tên\",\"form\":{\"first_name\":\"Họ\",\"last_name\":\"Tên\",\"full_name\":\"Họ và tên\",\"phone\":\"Số điện thoại\",\"country_code\":\"Mã quốc gia\",\"country\":\"Quốc gia\",\"city\":\"Thành phố\",\"province\":\"Tỉnh/Thành phố\",\"district\":\"Quận/Huyện\",\"ward\":\"Phường/Xã\",\"address\":\"Địa chỉ cụ thể (số nhà, tên đường...)\",\"generate_invoice\":\"Thông tin xuất hóa đơn\",\"company\":\"Công ty\",\"company_name\":\"Tên công ty\",\"company_tax_code\":\"Mã số thuế\",\"company_address\":\"Địa chỉ công ty\",\"postal_code\":\"Mã bưu điện\",\"same_as_billing\":\"Sử dụng địa chỉ thanh toán\",\"email\":\"Email\",\"save_address\":\"Ghi nhớ\",\"note\":\"Ghi chú khi giao hàng\",\"validation\":{\"full_name_required\":\"Vui lòng nhập đầy đủ họ và tên\",\"phone_required\":\"Vui lòng nhập số điện thoại\",\"phone_invalid\":\"Số điện thoại không hợp lệ\",\"email_required\":\"Vui lòng nhập email\",\"email_invalid\":\"Email không hợp lệ\",\"address_required\":\"Vui lòng nhập địa chỉ cụ thể\",\"province_required\":\"Vui lòng chọn tỉnh/thành phố\",\"district_required\":\"Vui lòng chọn quận/huyện\",\"ward_required\":\"Vui lòng chọn phường/xã\"}}},\"shipping_method\":{\"title\":\"Phương thức vận chuyển\",\"delivery\":\"Giao hàng\",\"pickup\":\"Mang đi\",\"continue_to_payment\":\"Tiếp tục thanh toán\",\"method\":\"Phương thức\",\"no_shipping_methods\":\"Không có phương thức vận chuyển\",\"error\":\"Lỗi khi thiết lập phương thức vận chuyển\"},\"payment\":{\"title\":\"Thanh toán\",\"method\":\"Phương thức\",\"details\":\"Chi tiết\",\"payment_method\":\"Phương thức thanh toán\",\"payment_details\":\"Chi tiết thanh toán\",\"another_step_will_appear\":\"Một bước sẽ xuất hiện\",\"gift_card\":\"Thẻ quà tặng\",\"enter_card_details\":\"Nhập thông tin thẻ\",\"continue_to_review\":\"Tiếp tục xem lại\",\"bank_transfer\":\"Chuyển khoản\",\"account_name\":\"Tên tài khoản\",\"bank_name\":\"Tên ngân hàng\",\"account_number\":\"Số tài khoản\",\"transfer_description\":\"Nội dung chuyển khoản\",\"bank_details\":{\"account_name\":\"CT TNHH TMDV EFRUIT\",\"bank_name\":\"Ngân hàng TECHCOMBANK\",\"account_number\":\"250487\",\"transfer_description\":\"eF - Tên anh/chị (Cty)\"}},\"review\":{\"title\":\"Xem lại\",\"complete_order\":\"Đặt hàng\",\"memberPointPolicy\":\"Chính sách tích điểm thành viên\",\"deliveryPolicy\":\"Chính sách giao hàng\",\"privacyPolicy\":\"Chính sách bảo mật\",\"returnPolicy\":\"Chính sách bảo hành, đổi / trả sản phẩm\",\"paymentOnline\":\"Chính sách thanh toán khi mua hàng online\",\"content\":\"Bằng cách nhấp vào nút Hoàn tất đơn hàng, bạn xác nhận rằng bạn đã đọc, hiểu và chấp nhận <deliveryPolicy>Chính sách giao hàng</deliveryPolicy>, <privacyPolicy>Chính sách bảo mật</privacyPolicy> và <returnPolicy>Chính sách bảo hành đổi trả sản phẩm</returnPolicy> và xác nhận rằng bạn đã đọc <paymentOnline>Chính sách thanh toán khi mua hàng online</paymentOnline>.\"}},\"summary\":{\"title\":\"Đơn hàng\",\"back_to_cart\":\"Quay lại giỏ hàng\",\"discount\":\"Giảm giá\",\"discount_code\":\"Mã giảm giá\",\"discount_code_placeholder\":\"Mã giảm giá\",\"discount_code_button\":\"Áp dụng\",\"discount_code_error\":\"Mã giảm giá không hợp lệ\",\"discount_code_success\":\"Mã giảm giá đã được áp dụng\",\"discount_code_view\":\"Xem mã giảm giá của bạn\",\"discount_code_remove_success\":\"Mã giảm giá đã được xóa\",\"discount_code_remove_error\":\"Không thể xóa mã giảm giá\",\"subtotal\":\"Giá tạm tính\",\"shipping\":\"Phí giao hàng\",\"tax\":\"Thuế\",\"total\":\"Tổng cộng\"},\"note\":\"Ghi chú\",\"product_note\":\"Ghi chú sản phẩm\",\"order_note\":\"Ghi chú đơn hàng\",\"delivery_time\":\"Thời gian giao\",\"cart_dialog_description\":\"Đây là hộp thoại giỏ hàng của bạn. Bạn có thể xem lại các mặt hàng, tiến hành thanh toán hoặc tiếp tục mua sắm.\"}"));}}),
"[project]/src/i18n/locales/vi/account.json (json)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
__turbopack_export_value__(JSON.parse("{\"error\":{\"title\":\"Lỗi\",\"invalid_email_or_password\":\"Email hoặc mật khẩu không hợp lệ\",\"email_already_exists\":\"Email đã tồn tại\",\"something_went_wrong\":\"Đã có lỗi xảy ra\",\"register_failed\":\"Đăng ký thất bại\"},\"register\":{\"title\":\"Đăng ký\",\"haveAccount\":\"Bạn đã có tài khoản?\",\"loginNow\":\"Đăng nhập ngay\"},\"logout\":{\"title\":\"Đăng xuất\"},\"required\":{\"title\":\"Đây là trường bắt buộc\"},\"signIn\":{\"title\":\"Đăng nhập\",\"success\":\"Đăng nhập thành công\",\"error\":\"Đăng nhập thất bại\",\"noAccount\":\"Bạn chưa có tài khoản?\",\"registerNow\":\"Đăng ký ngay\"},\"form_register\":{\"success\":\"Đăng ký thành công!\",\"first_name\":\"Tên\",\"last_name\":\"Họ\",\"email\":\"Email\",\"phone\":\"Số điện thoại\",\"password\":\"Mật khẩu\",\"confirmPassword\":\"Nhập lại mật khẩu\",\"agreeTerms\":\"Tôi đồng ý với <privacyPolicy> Điều khoản và Chính sách </privacyPolicy>\",\"validation\":{\"first_name\":\"Tên không hợp lệ\",\"last_name\":\"Họ không hợp lệ\",\"email\":\"Email không hợp lệ\",\"email_invalid\":\"Email không hợp lệ\",\"pwd_length\":\"Mật khẩu phải có hơn 8 ký tự\",\"confirmPassword\":\"Mật khẩu không khớp\",\"pwd_char\":\"Mật khẩu phải có ký tự\",\"pwd_letter\":\"Mật khẩu phải có số thứ tự\",\"pwd_special\":\"Mật khẩu phải có ký tự đặc biệt\",\"pwd_compare\":\"Mật khẩu không khớp\",\"phone\":\"Số điện thoại không hợp lệ\",\"phone_invalid\":\"Số điện thoại không hợp lệ\"}},\"saveInfo\":{\"title\":\"Lưu\",\"success\":\"Thông tin cập nhật thành công\",\"error\":\"Cập nhật thất bại\"},\"cancel\":{\"title\":\"Hủy\"},\"profile\":{\"personalInfo\":\"Thông tin cá nhân\",\"orders\":\"Lịch sử mua hàng\",\"changePwd\":\"Đổi mật khẩu\"},\"address\":{\"notFound\":\"Không tìm thấy địa chỉ\",\"title\":\"Sổ địa chỉ\",\"addBtn\":\"Thêm địa chỉ mới\",\"editBtn\":\"Chỉnh xửa\",\"deleteBtn\":\"Xóa\",\"setDefaultBtn\":\"Đặt làm mặc định\",\"add_success\":\"Địa chỉ đã được thêm thành công\",\"add_error\":\"Thêm địa chỉ thất bại\",\"delete_success\":\"Địa chỉ đã được xóa thành công\",\"delete_error\":\"Xóa địa chỉ thất bại\",\"update_success\":\"Địa chỉ đã được cập nhật thành công\",\"update_error\":\"Cập nhật địa chỉ thất bại\",\"please_select\":\"Vui lòng chọn...\",\"info\":{\"addAddress\":\"Thêm địa chỉ mới\",\"editAddress\":\"Chỉnh sửa địa chỉ\",\"first_name\":\"Tên\",\"last_name\":\"Họ\",\"company\":\"Công ty\",\"email\":\"Email\",\"address\":\"Địa chỉ\",\"residence\":\"Căn hộ, nhà ở, etc...\",\"postalCode\":\"Mã bưu chính\",\"city\":\"Thành phố - Quận - Huyện\",\"province\":\"Tỉnh\",\"ward\":\"Phường - Xã\",\"country\":\"Quốc gia\",\"phone\":\"Số điện thoại\",\"defaultShipping\":\"Mặc định\",\"setAsDefault\":\"Đặt làm mặc định\"}},\"resetPwd\":{\"sendBtn\":\"Gửi email\",\"sendEmailSuccess\":\"Email đổi mật khẩu gửi thành công\",\"sendEmailError\":\"Email đổi mật khẩu gửi thất bại\"},\"greeting\":\"Xin chào\",\"order\":{\"review\":\"Đánh giá\",\"productReview\":\"Đánh giá sản phẩm\",\"reviewSuccess\":\"Đánh giá thành công\",\"reviewError\":\"Đánh giá thất bại\",\"all\":\"Tất cả\",\"processing\":\"Đang xử lý\",\"completed\":\"Hoàn thành\",\"canceled\":\"Đã hủy\",\"delivering\":\"Đang giao hàng\",\"search\":\"Tìm kiếm theo tên sản phẩm hoặc mã đơn hàng\",\"searchBtn\":\"Tìm kiếm\",\"notFound\":\"Không tìm thấy đơn hàng\",\"quantity\":\"Số lượng\",\"total\":\"Tổng cộng\",\"giveReview\":\"Đánh giá\",\"seeDetails\":\"Xem chi tiết\",\"status\":{\"not_fulfilled\":\"Chưa xác nhận\",\"partially_fulfilled\":\"Đã xác nhận 1 phần\",\"fulfilled\":\"Đã xác nhận đơn hàng\",\"partially_shipped\":\"Đã vận chuyển 1 phần\",\"shipped\":\"Đã vận chuyển\",\"partially_delivered\":\"Đã giao hàng 1 phần\",\"delivered\":\"Đã giao hàng\",\"canceled\":\"Đã hủy\"},\"payment_status\":{\"not_paid\":\"Chưa thanh toán\",\"awaiting\":\"Chờ thanh toán\",\"authorized\":\"Đã xác nhận\",\"partially_authorized\":\"Đã xác nhận 1 phần\",\"captured\":\"Đã thanh toán\",\"partially_captured\":\"Đã thanh toán 1 phần\",\"partially_refunded\":\"Đã hoàn trả 1 phần\",\"refunded\":\"Đã hoàn trả\",\"canceled\":\"Đã hủy\",\"requires_action\":\"Chờ xử lý\"},\"detail\":{\"orderId\":\"Mã đơn hàng\",\"orderDate\":\"Ngày đặt hàng\",\"orderStatus\":\"Trạng thái đơn hàng\",\"customerDetails\":\"Chi tiết khách hàng\",\"purchasedProducts\":\"Sản phẩm đã mua\",\"variant\":\"Phân loại\",\"noVariant\":\"Không có phân loại\",\"paymentMethod\":\"Phương thức thanh toán\",\"status\":\"Trạng thái\",\"productPaied\":\"Sản phẩm đã mua\",\"review\":\"Đánh giá\",\"reviewSuccess\":\"Đánh giá thành công\",\"reviewError\":\"Đánh giá thất bại\",\"summary\":{\"subTotal\":\"Tạm tính\",\"shipping\":\"Phí giao hàng\",\"taxes\":\"Thuế\",\"total\":\"Tổng cộng\",\"discount\":\"Giảm giá\"}},\"more\":\"Xem thêm\",\"noOrders\":\"Không có đơn hàng nào\",\"noOrdersMessage\":\"Bạn chưa có đơn hàng nào, hãy đặt hàng ngay\",\"continueShopping\":\"Tiếp tục mua hàng\"}}"));}}),
"[project]/src/i18n/locales/vi/order_confirm.json (json)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
__turbopack_export_value__(JSON.parse("{\"title\":\"Đặt hàng thành công\",\"order_confirmed\":\"ĐƠN HÀNG ĐÃ ĐƯỢC XÁC NHẬN\",\"order_number\":\"Mã đơn hàng\",\"thanks_message\":\"Cảm ơn bạn đã mua sắm tại website eFruit.vn\",\"content\":\"Sau khi Shop xác nhận đơn hàng, sản phẩm sẽ được giao đến bạn theo thời gian hẹn trước. eFruit rất hân hạnh được phục vụ bạn!\",\"continue_shopping\":\"Tiếp tục mua sắm\",\"order_load_error\":\"Không thể tải thông tin đơn hàng\",\"order_processing\":\"Vui lòng chờ một chút, chúng tôi đang xử lý...\",\"order_loading\":\"Đang tải thông tin đơn hàng\",\"order_not_found\":\"Không tìm thấy đơn hàng\",\"error_occurred\":\"Đã xảy ra lỗi, vui lòng thử lại sau.\",\"shipping_details\":\"1. Thông tin giao hàng\",\"delivery_method\":\"2. Phương thức vận chuyển\",\"payment_method\":{\"title\":\"3. Phương thức thanh toán\",\"credit_card\":\"Thẻ tín dụng\",\"paypal\":\"PayPal\",\"ideal\":\"iDeal\",\"bancontact\":\"Bancontact\",\"manual_payment\":\"Thanh toán sau\"},\"summary\":{\"title\":\"Đơn hàng\",\"quantity\":\"Số lượng\",\"variant\":\"Phân loại\",\"product\":\"Sản phẩm\",\"price\":\"Tạm tính\",\"discount\":\"Giảm giá\",\"shipping\":\"Phí giao hàng\",\"taxes\":\"Thuế\",\"total\":\"Tổng tiền\",\"reviewed\":\"Đã đánh giá\"}}"));}}),
"[project]/src/i18n/locales/vi/wishlist.json (json)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
__turbopack_export_value__(JSON.parse("{\"title\":\"Sản phẩm yêu thích\",\"noItems\":\"Không có sản phẩm trong danh sách yêu thích\",\"goToStore\":\"Đi đến cửa hàng\",\"addToWishlist\":\"Thêm vào danh sách yêu thích\",\"addToWishlistSuccess\":\"Đã thêm vào danh sách yêu thích\",\"addToWishlistError\":\"Lỗi khi thêm vào danh sách yêu thích\",\"removeFromWishlist\":\"Xóa khỏi danh sách yêu thích\",\"removeFromWishlistSuccess\":\"Đã xóa khỏi danh sách yêu thích\",\"removeFromWishlistError\":\"Lỗi khi xóa khỏi danh sách yêu thích\",\"add\":\"Thêm\",\"remove\":\"Xóa\"}"));}}),
"[project]/src/i18n/locales/vi/blog.json (json)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
__turbopack_export_value__(JSON.parse("{\"news\":\"TIN TỨC\",\"category\":\"Danh mục\",\"no_posts_found\":\"Không tìm thấy bài viết cho danh mục này\",\"featured\":{\"title\":\"Bài viết nổi bật\",\"load_error\":\"Lỗi khi tải bài viết nổi bật\"},\"all_blogs\":{\"title\":\"Tin tức & Khuyến mãi\",\"load_error\":\"Lỗi khi tải bài viết\"},\"menu_category\":{\"all\":\"Tất cả\",\"search_placeholder\":\"Tìm kiếm theo tiêu đề\",\"category_placeholder\":\"Chọn danh mục\"}}"));}}),
"[project]/src/i18n/locales/vi/collections.json (json)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
__turbopack_export_value__(JSON.parse("{\"title\":\"Tất cả sản phẩm\",\"filter\":\"Bộ lọc\",\"filter_products\":\"Lọc sản phẩm\",\"reset_filter\":\"Reset\",\"price\":\"Giá\",\"category\":\"Danh mục\",\"apply\":\"Áp dụng\",\"remove\":\"Xóa\",\"show\":\"Hiển thị\",\"products\":\"Sản phẩm\",\"sort_menu\":{\"title\":\"Sắp xếp theo\",\"newest\":\"Mới nhất\",\"featured\":\"Nổi bật\",\"price_asc\":\"Giá tăng dần\",\"price_desc\":\"Giá giảm dần\"},\"add_to_wishlist\":\"Thêm vào danh sách yêu thích\",\"no_product_found\":\"Không tìm thấy sản phẩm\",\"filter_menu\":{\"all\":\"Tất cả\",\"remove\":\"Xóa\",\"remove_all\":\"Xóa tất cả\",\"sort_by\":\"Sắp xếp theo\",\"category\":\"Danh mục\",\"program\":\"Chương trình khuyến mãi\",\"size\":\"Kích cỡ\",\"color\":\"Màu sắc\",\"price\":\"Giá\",\"newest\":\"Mới nhất\",\"featured\":\"Nổi bật\",\"price_asc\":\"Giá tăng dần\",\"price_desc\":\"Giá giảm dần\",\"latest_arrivals\":\"Mới nhất\",\"price_low_to_high\":\"Giá: Thấp → Cao\",\"price_high_to_low\":\"Giá: Cao → Thấp\"}}"));}}),
"[project]/src/i18n/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$i18next$2f$dist$2f$esm$2f$i18next$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/i18next/dist/esm/i18next.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$i18next$2d$browser$2d$languagedetector$2f$dist$2f$esm$2f$i18nextBrowserLanguageDetector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/node_modules/react-i18next/dist/es/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$initReactI18next$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-i18next/dist/es/initReactI18next.js [app-ssr] (ecmascript)");
;
;
;
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$i18next$2f$dist$2f$esm$2f$i18next$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].use(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$i18next$2d$browser$2d$languagedetector$2f$dist$2f$esm$2f$i18nextBrowserLanguageDetector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]).use(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$initReactI18next$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["initReactI18next"]).init({
    debug: false,
    lng: "vi",
    fallbackLng: "vi",
    interpolation: {
        escapeValue: false
    },
    defaultNS: "layout",
    ns: [
        "layout",
        "templates",
        "product_detail",
        "cart",
        "checkout",
        "account",
        "order_confirm",
        "wishlist",
        "blog",
        "collections"
    ],
    resources: {
        en: {
            layout: __turbopack_require__("[project]/src/i18n/locales/en/layout.json (json)"),
            templates: __turbopack_require__("[project]/src/i18n/locales/en/templates.json (json)"),
            product_detail: __turbopack_require__("[project]/src/i18n/locales/en/product_detail.json (json)"),
            cart: __turbopack_require__("[project]/src/i18n/locales/en/cart.json (json)"),
            checkout: __turbopack_require__("[project]/src/i18n/locales/en/checkout.json (json)"),
            account: __turbopack_require__("[project]/src/i18n/locales/en/account.json (json)"),
            order_confirm: __turbopack_require__("[project]/src/i18n/locales/en/order_confirm.json (json)"),
            wishlist: __turbopack_require__("[project]/src/i18n/locales/en/wishlist.json (json)"),
            blog: __turbopack_require__("[project]/src/i18n/locales/en/blog.json (json)"),
            collections: __turbopack_require__("[project]/src/i18n/locales/en/collections.json (json)")
        },
        vi: {
            layout: __turbopack_require__("[project]/src/i18n/locales/vi/layout.json (json)"),
            templates: __turbopack_require__("[project]/src/i18n/locales/vi/templates.json (json)"),
            product_detail: __turbopack_require__("[project]/src/i18n/locales/vi/product_detail.json (json)"),
            cart: __turbopack_require__("[project]/src/i18n/locales/vi/cart.json (json)"),
            checkout: __turbopack_require__("[project]/src/i18n/locales/vi/checkout.json (json)"),
            account: __turbopack_require__("[project]/src/i18n/locales/vi/account.json (json)"),
            order_confirm: __turbopack_require__("[project]/src/i18n/locales/vi/order_confirm.json (json)"),
            wishlist: __turbopack_require__("[project]/src/i18n/locales/vi/wishlist.json (json)"),
            blog: __turbopack_require__("[project]/src/i18n/locales/vi/blog.json (json)"),
            collections: __turbopack_require__("[project]/src/i18n/locales/vi/collections.json (json)")
        }
    }
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$i18next$2f$dist$2f$esm$2f$i18next$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
}}),
"[project]/src/lib/context/provider-i18n.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/i18n/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/node_modules/react-i18next/dist/es/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$I18nextProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-i18next/dist/es/I18nextProvider.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
const I18nProvider = ({ children, defaultLanguage })=>{
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].changeLanguage(defaultLanguage);
    }, [
        defaultLanguage
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$I18nextProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["I18nextProvider"], {
        i18n: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
        defaultNS: "layout",
        children: children
    }, void 0, false, {
        fileName: "[project]/src/lib/context/provider-i18n.tsx",
        lineNumber: 18,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = I18nProvider;
}}),
"[project]/src/contexts/cart-bubble-context.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
__turbopack_esm__({
    "CartBubbleProvider": (()=>CartBubbleProvider),
    "useCartBubble": (()=>useCartBubble)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
"use client";
;
;
const CartBubbleContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useCartBubble = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(CartBubbleContext);
    if (!context) {
        throw new Error("useCartBubble must be used within a CartBubbleProvider");
    }
    return context;
};
const CartBubbleProvider = ({ children })=>{
    const [animations, setAnimations] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const triggerBubble = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((params)=>{
        const id = `bubble-${Date.now()}-${Math.random()}`;
        const newAnimation = {
            id,
            ...params
        };
        setAnimations((prev)=>[
                ...prev,
                newAnimation
            ]);
        setTimeout(()=>{
            setAnimations((prev)=>prev.filter((anim)=>anim.id !== id));
        }, 2000);
    }, []);
    const removeAnimation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((id)=>{
        setAnimations((prev)=>prev.filter((anim)=>anim.id !== id));
    }, []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(CartBubbleContext.Provider, {
        value: {
            animations,
            triggerBubble,
            removeAnimation
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/cart-bubble-context.tsx",
        lineNumber: 74,
        columnNumber: 5
    }, this);
};
}}),
"[project]/src/components/cart-bubble/index.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
__turbopack_esm__({
    "CartBubble": (()=>CartBubble),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$cart$2d$bubble$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/cart-bubble-context.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
"use client";
;
;
;
const CartBubble = ()=>{
    const { animations } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$cart$2d$bubble$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCartBubble"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "pointer-events-none fixed inset-0 z-[9999]",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimatePresence"], {
            children: animations.map((animation)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(BubbleItem, {
                    animation: animation
                }, animation.id, false, {
                    fileName: "[project]/src/components/cart-bubble/index.tsx",
                    lineNumber: 14,
                    columnNumber: 11
                }, this))
        }, void 0, false, {
            fileName: "[project]/src/components/cart-bubble/index.tsx",
            lineNumber: 12,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/cart-bubble/index.tsx",
        lineNumber: 11,
        columnNumber: 5
    }, this);
};
const BubbleItem = ({ animation })=>{
    const { startX, startY, endX, endY, productImage } = animation;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
        className: "absolute flex items-center justify-center",
        initial: {
            x: startX - 20,
            y: startY - 20,
            scale: 1,
            opacity: 1
        },
        animate: {
            x: endX - 20,
            y: endY - 20,
            scale: 0.5,
            opacity: 0
        },
        exit: {
            opacity: 0,
            scale: 0
        },
        transition: {
            duration: 1.2,
            ease: "easeOut"
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "shadow-lg flex h-10 w-10 items-center justify-center rounded-full bg-blue-500 font-bold text-white",
            children: productImage ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                src: productImage,
                alt: "Product",
                className: "h-8 w-8 rounded-full object-cover"
            }, void 0, false, {
                fileName: "[project]/src/components/cart-bubble/index.tsx",
                lineNumber: 63,
                columnNumber: 11
            }, this) : "+1"
        }, void 0, false, {
            fileName: "[project]/src/components/cart-bubble/index.tsx",
            lineNumber: 61,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/cart-bubble/index.tsx",
        lineNumber: 37,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = CartBubble;
}}),
"[project]/src/utils/index.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
__turbopack_esm__({
    "cn": (()=>cn),
    "delay": (()=>delay)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/clsx/dist/clsx.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-ssr] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
const delay = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));
}}),
"[project]/src/components/ui/tooltip.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
__turbopack_esm__({
    "Tooltip": (()=>Tooltip),
    "TooltipContent": (()=>TooltipContent),
    "TooltipProvider": (()=>TooltipProvider),
    "TooltipTrigger": (()=>TooltipTrigger)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/utils/index.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tooltip$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@radix-ui/react-tooltip/dist/index.mjs [app-ssr] (ecmascript)");
"use client";
;
;
;
;
const TooltipProvider = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tooltip$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__.Provider;
const Tooltip = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tooltip$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__.Root;
const TooltipTrigger = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tooltip$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__.Trigger;
const TooltipContent = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tooltip$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__.Portal, {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tooltip$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__.Content, {
            ref: ref,
            sideOffset: sideOffset,
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("z-50 overflow-hidden rounded-md bg-primary-main text-white px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2", className),
            ...props
        }, void 0, false, {
            fileName: "[project]/src/components/ui/tooltip.tsx",
            lineNumber: 19,
            columnNumber: 5
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/tooltip.tsx",
        lineNumber: 18,
        columnNumber: 3
    }, this));
TooltipContent.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tooltip$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__.Content.displayName;
;
}}),
"[project]/src/app/[countryCode]/[localeLanguage]/layout.tsx [app-rsc] (ecmascript, Next.js server component, client modules ssr)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
}}),

};

//# sourceMappingURL=src_313ae9._.js.map