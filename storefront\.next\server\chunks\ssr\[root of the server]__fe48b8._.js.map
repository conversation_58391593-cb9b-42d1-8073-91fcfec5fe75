{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/lib/data/customer.ts"], "sourcesContent": ["\"use server\"\r\nimport { revalidateTag } from \"next/cache\"\r\nimport { redirect } from \"next/navigation\"\r\n\r\nimport { HttpTypes } from \"@medusajs/types\"\r\n\r\nimport { sdk } from \"@lib/config\"\r\nimport medusaError from \"@lib/util/medusa-error\"\r\nimport {\r\n  getAuthHeaders,\r\n  getCacheOptions,\r\n  getCacheTag,\r\n  getCartId,\r\n  removeAuthToken,\r\n  setAuthToken,\r\n} from \"./cookies\"\r\n\r\nexport const retrieveCustomer =\r\n  async (): Promise<HttpTypes.StoreCustomer | null> => {\r\n    const headers = {\r\n      ...(await getAuthHeaders()),\r\n    }\r\n\r\n    const next = {\r\n      ...(await getCacheOptions(\"customers\")),\r\n    }\r\n\r\n    return await sdk.client\r\n      .fetch<{ customer: HttpTypes.StoreCustomer }>(`/store/customers/me`, {\r\n        method: \"GET\",\r\n        query: {\r\n          fields: \"*orders\",\r\n        },\r\n        headers,\r\n        next,\r\n      })\r\n      .then(({ customer }) => customer)\r\n      .catch(() => null)\r\n  }\r\n\r\nexport const updateCustomer = async (body: HttpTypes.StoreUpdateCustomer) => {\r\n  const headers = {\r\n    ...(await getAuthHeaders()),\r\n  }\r\n\r\n  const updateRes = await sdk.store.customer\r\n    .update(body, {}, headers)\r\n    .then(({ customer }) => customer)\r\n    .catch(medusaError)\r\n\r\n  const cacheTag = await getCacheTag(\"customers\")\r\n  revalidateTag(cacheTag)\r\n\r\n  return updateRes\r\n}\r\n\r\nexport async function signup(_currentState: unknown, formData: FormData) {\r\n  const password = formData.get(\"password\") as string\r\n  const customerForm = {\r\n    email: formData.get(\"email\") as string,\r\n    first_name: formData.get(\"first_name\") as string,\r\n    last_name: formData.get(\"last_name\") as string,\r\n    phone: formData.get(\"phone\") as string,\r\n  }\r\n\r\n  try {\r\n    const token = await sdk.auth.register(\"customer\", \"emailpass\", {\r\n      email: customerForm.email,\r\n      password: password,\r\n    })\r\n\r\n    const headers = {\r\n      ...(await getAuthHeaders()),\r\n      Authorization: `Bearer ${token}`,\r\n    }\r\n\r\n    const { customer: createdCustomer } = await sdk.store.customer.create(\r\n      customerForm,\r\n      {},\r\n      headers\r\n    )\r\n\r\n    const loginToken = await sdk.auth.login(\"customer\", \"emailpass\", {\r\n      email: customerForm.email,\r\n      password,\r\n    })\r\n\r\n    await setAuthToken(loginToken as string)\r\n\r\n    const customerCacheTag = await getCacheTag(\"customers\")\r\n    revalidateTag(customerCacheTag)\r\n\r\n    await transferCart()\r\n\r\n    return { isSuccess: true, data: createdCustomer }\r\n  } catch (error: any) {\r\n    if (error?.status === 401) {\r\n      return {\r\n        isSuccess: false,\r\n        error: {\r\n          message: \"Error: Identity with email already exists\",\r\n          errorStatus: error?.status,\r\n        },\r\n      }\r\n    } else {\r\n      return {\r\n        isSuccess: false,\r\n        error: {\r\n          message: \"Error: Something went wrong\",\r\n          errorStatus: error?.status,\r\n        },\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nexport async function login(_currentState: unknown, formData: FormData) {\r\n  const email = formData.get(\"email\") as string\r\n  const password = formData.get(\"password\") as string\r\n\r\n  try {\r\n    const token = await sdk.auth.login(\"customer\", \"emailpass\", {\r\n      email,\r\n      password,\r\n    })\r\n    await setAuthToken(token as string)\r\n    const customerCacheTag = await getCacheTag(\"customers\")\r\n    revalidateTag(customerCacheTag)\r\n\r\n    await transferCart()\r\n    return { isSuccess: true, error: null, data: { token: token } }\r\n  } catch (error: any) {\r\n    if (error?.status === 401) {\r\n      return {\r\n        isSuccess: false,\r\n        error: {\r\n          message: \"Error: Invalid email or password\",\r\n          errorStatus: error?.status,\r\n        },\r\n      }\r\n    } else {\r\n      return {\r\n        isSuccess: false,\r\n        error: {\r\n          message: \"Error: Something went wrong\",\r\n          errorStatus: error?.status,\r\n        },\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nexport async function signout(countryCode: string, localeLanguage: string) {\r\n  await sdk.auth.logout()\r\n  removeAuthToken()\r\n  revalidateTag(\"auth\")\r\n  revalidateTag(\"customer\")\r\n  redirect(`/${countryCode}/${localeLanguage}/account`)\r\n}\r\n\r\nexport async function transferCart() {\r\n  const cartId = await getCartId()\r\n\r\n  if (!cartId) {\r\n    return\r\n  }\r\n\r\n  const headers = await getAuthHeaders()\r\n\r\n  await sdk.store.cart.transferCart(cartId, {}, headers)\r\n\r\n  revalidateTag(\"cart\")\r\n}\r\n\r\nexport const addCustomerAddress = async (\r\n  _currentState: unknown,\r\n  formData: {\r\n    first_name: string\r\n    last_name: string\r\n    company: string\r\n    address_1: string\r\n    address_2: string\r\n    city: string\r\n    postal_code: string\r\n    province: string\r\n    country_code: string\r\n    phone: string\r\n  }\r\n): Promise<any> => {\r\n  const address = {\r\n    first_name: formData.first_name,\r\n    last_name: formData.last_name,\r\n    company: formData.company,\r\n    address_1: formData.address_1,\r\n    address_2: formData.address_2,\r\n    city: formData.city,\r\n    postal_code: formData.postal_code,\r\n    province: formData.province,\r\n    country_code: formData.country_code,\r\n    phone: formData.phone,\r\n  }\r\n\r\n  const headers = {\r\n    ...(await getAuthHeaders()),\r\n  }\r\n\r\n  return sdk.store.customer\r\n    .createAddress(address, {}, headers)\r\n    .then(async ({ customer }) => {\r\n      const customerCacheTag = await getCacheTag(\"customers\")\r\n      revalidateTag(customerCacheTag)\r\n      return { success: true, error: null, data: customer }\r\n    })\r\n    .catch((err) => {\r\n      return { success: false, error: err.toString() }\r\n    })\r\n}\r\n\r\nexport const requestPasswordReset = async (email: string) => {\r\n  if (!email) {\r\n    return\r\n  }\r\n\r\n  // return await sdk.client\r\n  //   .fetch(`/auth/customer/emailpass/reset-password`, {\r\n  //     method: \"POST\",\r\n  //     body: JSON.stringify({ identifier: email }),\r\n  //   })\r\n  // .then(() => {\r\n  //   console.log(\"🚀 ~ requestPasswordReset ~ email:\", email)\r\n\r\n  //   return { success: true, error: null }\r\n  // })\r\n  // .catch((err) => {\r\n  //   console.log(\"🚀 ~ requestPasswordReset ~ err.toString():\", err.toString())\r\n  //   return { success: false, error: err.toString() }\r\n  // })\r\n  return await sdk.auth\r\n    .resetPassword(\"customer\", \"emailpass\", { identifier: email })\r\n    .then(() => {\r\n      return { success: true, error: null }\r\n    })\r\n    .catch((err) => {\r\n      return { success: false, error: err.toString() }\r\n    })\r\n}\r\n\r\nexport const deleteCustomerAddress = async (\r\n  addressId: string\r\n): Promise<void> => {\r\n  const headers = {\r\n    ...(await getAuthHeaders()),\r\n  }\r\n\r\n  await sdk.store.customer\r\n    .deleteAddress(addressId, headers)\r\n    .then(async () => {\r\n      const customerCacheTag = await getCacheTag(\"customers\")\r\n      revalidateTag(customerCacheTag)\r\n      return { success: true, error: null }\r\n    })\r\n    .catch((err) => {\r\n      return { success: false, error: err.toString() }\r\n    })\r\n}\r\n\r\nexport const updateCustomerAddress = async (\r\n  currentState: Record<string, unknown>,\r\n  formData: {\r\n    first_name: string\r\n    last_name: string\r\n    company: string\r\n    address_1: string\r\n    address_2: string\r\n    city: string\r\n    postal_code: string\r\n    province: string\r\n    country_code: string\r\n    phone: string\r\n  }\r\n): Promise<any> => {\r\n  const addressId = currentState.addressId as string\r\n\r\n  const address = {\r\n    first_name: formData.first_name,\r\n    last_name: formData.last_name,\r\n    company: formData.company,\r\n    address_1: formData.address_1,\r\n    address_2: formData.address_2,\r\n    city: formData.city,\r\n    postal_code: formData.postal_code,\r\n    province: formData.province,\r\n    country_code: formData.country_code,\r\n    phone: formData.phone,\r\n  }\r\n\r\n  const headers = {\r\n    ...(await getAuthHeaders()),\r\n  }\r\n\r\n  return sdk.store.customer\r\n    .updateAddress(addressId, address, {}, headers)\r\n    .then(async () => {\r\n      const customerCacheTag = await getCacheTag(\"customers\")\r\n      revalidateTag(customerCacheTag)\r\n      return { success: true, error: null }\r\n    })\r\n    .catch((err) => {\r\n      return { success: false, error: err.toString() }\r\n    })\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;IAiBa;IAuBA;IAgBS;IA4DA;IAoCA;IAQA;IAcT;IA4CA;IA6BA;IAmBA"}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/layout/components/cart-mismatch-banner/index.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { transferCart } from \"@lib/data/customer\"\r\nimport { ExclamationCircleSolid } from \"@medusajs/icons\"\r\nimport { StoreCart, StoreCustomer } from \"@medusajs/types\"\r\nimport { Button } from \"@medusajs/ui\"\r\nimport { useState } from \"react\"\r\n\r\nfunction CartMismatchBanner(props: {\r\n  customer: StoreCustomer\r\n  cart: StoreCart\r\n}) {\r\n  const { customer, cart } = props\r\n  const [isPending, setIsPending] = useState(false)\r\n  const [actionText, setActionText] = useState(\"Run transfer again\")\r\n\r\n  if (!customer || !!cart.customer_id) {\r\n    return\r\n  }\r\n\r\n  const handleSubmit = async () => {\r\n    try {\r\n      setIsPending(true)\r\n      setActionText(\"Transferring..\")\r\n\r\n      await transferCart()\r\n    } catch {\r\n      setActionText(\"Run transfer again\")\r\n      setIsPending(false)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex items-center justify-center small:p-4 p-2 text-center bg-orange-300 small:gap-2 gap-1 text-sm mt-2 text-orange-800\">\r\n      <div className=\"flex flex-col small:flex-row small:gap-2 gap-1 items-center\">\r\n        <span className=\"flex items-center gap-1\">\r\n          <ExclamationCircleSolid className=\"inline\" />\r\n          Something went wrong when we tried to transfer your cart\r\n        </span>\r\n\r\n        <span>·</span>\r\n\r\n        <Button\r\n          variant=\"transparent\"\r\n          className=\"hover:bg-transparent active:bg-transparent focus:bg-transparent disabled:text-orange-500 text-orange-950 p-0 bg-transparent\"\r\n          size=\"base\"\r\n          disabled={isPending}\r\n          onClick={handleSubmit}\r\n        >\r\n          {actionText}\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default CartMismatchBanner\r\n"], "names": [], "mappings": ";;;;AAEA;AAIA;AAHA;AAEA;AALA;;;;;;AAQA,SAAS,mBAAmB,KAG3B;IACC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,IAAI,CAAC,YAAY,CAAC,CAAC,KAAK,WAAW,EAAE;QACnC;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,aAAa;YACb,cAAc;YAEd,MAAM,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;QACnB,EAAE,OAAM;YACN,cAAc;YACd,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAK,WAAU;;sCACd,8OAAC,yOAAA,CAAA,yBAAsB;4BAAC,WAAU;;;;;;wBAAW;;;;;;;8BAI/C,8OAAC;8BAAK;;;;;;8BAEN,8OAAC,iLAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,WAAU;oBACV,MAAK;oBACL,UAAU;oBACV,SAAS;8BAER;;;;;;;;;;;;;;;;;AAKX;uCAEe"}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/hooks/use-media-query.ts"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\n\r\nconst useMediaQuery = (query: string): boolean => {\r\n  const [matches, setMatches] = useState<boolean>(false);\r\n\r\n  useEffect(() => {\r\n    const media = window.matchMedia(query);\r\n    if (media.matches !== matches) {\r\n      setMatches(media.matches);\r\n    }\r\n\r\n    // Define the listener as a separate function to avoid recreating it on each render\r\n    const listener = () => setMatches(media.matches);\r\n\r\n    // Use 'change' instead of 'resize' for better performance\r\n    media.addEventListener(\"change\", listener); \r\n\r\n    // Cleanup function to remove the event listener\r\n    return () => media.removeEventListener(\"change\", listener);\r\n\r\n  }, [matches, query]); // Only recreate the listener when 'matches' or 'query' changes\r\n\r\n  return matches;\r\n};\r\n\r\nexport default useMediaQuery;"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,gBAAgB,CAAC;IACrB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAEhD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,OAAO,UAAU,CAAC;QAChC,IAAI,MAAM,OAAO,KAAK,SAAS;YAC7B,WAAW,MAAM,OAAO;QAC1B;QAEA,mFAAmF;QACnF,MAAM,WAAW,IAAM,WAAW,MAAM,OAAO;QAE/C,0DAA0D;QAC1D,MAAM,gBAAgB,CAAC,UAAU;QAEjC,gDAAgD;QAChD,OAAO,IAAM,MAAM,mBAAmB,CAAC,UAAU;IAEnD,GAAG;QAAC;QAAS;KAAM,GAAG,+DAA+D;IAErF,OAAO;AACT;uCAEe"}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/utils/path.ts"], "sourcesContent": ["export const PAGE_PATH = {\r\n  HOME: \"/\",\r\n  CART: \"/cart\",\r\n  CHECKOUT: {\r\n    root: \"/checkout\",\r\n    step: (step: string) => `/checkout?step=${step}`,\r\n  },\r\n  ACCOUNT: {\r\n    root: \"/account\",\r\n    dashboard: {\r\n      root: \"/account/dashboard\",\r\n      profile: \"/account/profile\",\r\n      addresses: \"/account/addresses\",\r\n      orders: \"/account/orders\",\r\n      orderDetails: (orderId: string) => `/account/orders/details/${orderId}`,\r\n      changePassword: \"/account/change-password\",\r\n    },\r\n  },\r\n  FORGOT_PASSWORD: \"/forgot-password\",\r\n  RESET_PASSWORD: \"/reset-password\",\r\n  CATEGORIES: {\r\n    root: \"/categories\",\r\n    detail: (handle: string) => `/categories/${handle}`,\r\n  },\r\n  COLLECTIONS: {\r\n    root: \"/collections\",\r\n    detail: (handle: string) => `/collections/${handle}`,\r\n  },\r\n  PRODUCT: {\r\n    root: \"/products\",\r\n    detail: (handle: string, variantId: string = \"\") =>\r\n      `/products/${handle}${variantId ? `?variantId=${variantId}` : \"\"}`,\r\n  },\r\n  ORDER: \"/order\",\r\n  WISHLIST: \"/wishlist\",\r\n  BLOGS: {\r\n    root: \"/blogs\",\r\n    detail: (handle: string) => `/blogs/${handle}`,\r\n  },\r\n  PAGES: {\r\n    root: \"/pages\",\r\n    memberPointPolicy: \"/pages/chinh-sach-tich-diem-thanh-vien\",\r\n    deliveryPolicy: \"/pages/chinh-sach-giao-hang\",\r\n    privacyPolicy: \"/pages/chinh-sach-bao-mat-thong-tin\",\r\n    returnPolicy: \"/pages/chinh-sach-bao-hanh-doi-tra-san-pham\",\r\n    paymentOnline: \"/pages/chinh-sach-thanh-toan-khi-mua-hang-online\",\r\n  },\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,YAAY;IACvB,MAAM;IACN,MAAM;IACN,UAAU;QACR,MAAM;QACN,MAAM,CAAC,OAAiB,CAAC,eAAe,EAAE,MAAM;IAClD;IACA,SAAS;QACP,MAAM;QACN,WAAW;YACT,MAAM;YACN,SAAS;YACT,WAAW;YACX,QAAQ;YACR,cAAc,CAAC,UAAoB,CAAC,wBAAwB,EAAE,SAAS;YACvE,gBAAgB;QAClB;IACF;IACA,iBAAiB;IACjB,gBAAgB;IAChB,YAAY;QACV,MAAM;QACN,QAAQ,CAAC,SAAmB,CAAC,YAAY,EAAE,QAAQ;IACrD;IACA,aAAa;QACX,MAAM;QACN,QAAQ,CAAC,SAAmB,CAAC,aAAa,EAAE,QAAQ;IACtD;IACA,SAAS;QACP,MAAM;QACN,QAAQ,CAAC,QAAgB,YAAoB,EAAE,GAC7C,CAAC,UAAU,EAAE,SAAS,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG,IAAI;IACtE;IACA,OAAO;IACP,UAAU;IACV,OAAO;QACL,MAAM;QACN,QAAQ,CAAC,SAAmB,CAAC,OAAO,EAAE,QAAQ;IAChD;IACA,OAAO;QACL,MAAM;QACN,mBAAmB;QACnB,gBAAgB;QAChB,eAAe;QACf,cAAc;QACd,eAAe;IACjB;AACF"}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/common/components/localized-client-link/index.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport React from \"react\"\r\nimport Link from \"next/link\"\r\nimport { useParams } from \"next/navigation\"\r\n\r\n/**\r\n * Use this component to create a Next.js `<Link />` that persists the current country code in the url,\r\n * without having to explicitly pass it as a prop.\r\n */\r\nconst LocalizedClientLink = ({\r\n  children,\r\n  href,\r\n  ...props\r\n}: {\r\n  children?: React.ReactNode\r\n  href: string\r\n  className?: string\r\n  onClick?: () => void\r\n  passHref?: true\r\n  [x: string]: any\r\n}) => {\r\n  const { countryCode, localeLanguage } = useParams();\r\n\r\n  return (\r\n    <Link href={`/${countryCode}/${localeLanguage}${href}`} {...props}>\r\n      {children}\r\n    </Link>\r\n  )\r\n}\r\n\r\nexport default LocalizedClientLink\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMA;;;CAGC,GACD,MAAM,sBAAsB,CAAC,EAC3B,QAAQ,EACR,IAAI,EACJ,GAAG,OAQJ;IACC,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEhD,qBACE,8OAAC,4HAAA,CAAA,UAAI;QAAC,MAAM,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,iBAAiB,MAAM;QAAG,GAAG,KAAK;kBAC9D;;;;;;AAGP;uCAEe"}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/ui/typography.tsx"], "sourcesContent": ["import { cva, type VariantProps } from \"class-variance-authority\"\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"utils\"\r\n\r\nconst typographyVariants = cva(\"leading-6\", {\r\n  variants: {\r\n    variant: {\r\n      h1: \"scroll-m-20 text-3xl font-bold tracking-tight lg:text-5xl\",\r\n      h2: \"scroll-m-20 pb-2 text-2xl font-semibold tracking-tight first:mt-0\",\r\n      h3: \"scroll-m-20 text-xl font-semibold tracking-tight\",\r\n      h4: \"scroll-m-20 text-lg font-semibold tracking-tight\",\r\n      h5: \"scroll-m-20 text-md font-semibold\",\r\n      h6: \"scroll-m-20 text-base font-semibold\",\r\n\r\n      body1: \"text-base\",\r\n      body2: \"text-sm\",\r\n      caption: \"text-xs\",\r\n      p: \"!leading-6 text-base\",\r\n\r\n      blockquote: \"mt-6 border-l-2 pl-6 italic\",\r\n      span: \"text-base leading-6\",\r\n      div: \"text-base leading-6\",\r\n      label: \"text-base leading-6\",\r\n    },\r\n    color: {\r\n      primary: \"text-primary-main\",\r\n      secondary: \"text-secondary\",\r\n      destructive: \"text-destructive\",\r\n      grayscale: \"text-grayscale-500\",\r\n      accent: \"text-accent\",\r\n      background: \"text-background\",\r\n      foreground: \"text-foreground\",\r\n      white: \"text-white\",\r\n      black: \"text-black\",\r\n    },\r\n    size: {\r\n      // \"3xs\": \"text-3xs\",\r\n      // \"2xs\": \"text-4xs lg:text-2xs xl:text-xs\",\r\n      xs: \"text-xs\",\r\n      sm: \"text-xs xl:text-sm\",\r\n      base: \"text-sm xl:text-base\",\r\n      base18: \"text-sm lg:text-base xl:text-base18\",\r\n      md: \"text-sm lg:text-base xl:text-md\",\r\n      lg: \"text-base lg:text-md xl:text-lg\",\r\n      xl: \"text-md lg:text-lg xl:text-xl\",\r\n      \"2xl\": \"text-lg lg:text-xl xl:text-2xl\",\r\n      \"3xl\": \"text-xl lg:text-2xl xl:text-3xl\",\r\n      \"4xl\": \"text-2xl lg:text-3xl xl:text-4xl\",\r\n      \"5xl\": \"text-3xl lg:text-4xl xl:text-5xl\",\r\n      \"6xl\": \"text-4xl lg:text-5xl xl:text-6xl\",\r\n      \"7xl\": \"text-5xl lg:text-6xl xl:text-7xl\",\r\n    },\r\n    defaultVariants: {\r\n      variant: \"p\",\r\n      color: \"foreground\",\r\n      size: \"base\",\r\n    },\r\n  },\r\n})\r\n\r\nexport interface TypographyProps\r\n  extends React.HTMLAttributes<HTMLElement>,\r\n    VariantProps<typeof typographyVariants> {\r\n  component?: React.ElementType\r\n  variant?:\r\n    | \"h1\"\r\n    | \"h2\"\r\n    | \"h3\"\r\n    | \"h4\"\r\n    | \"h5\"\r\n    | \"h6\"\r\n    | \"p\"\r\n    | \"span\"\r\n    | \"div\"\r\n    | \"label\"\r\n    | \"blockquote\"\r\n  size?:\r\n    | \"base\"\r\n    | \"base18\"\r\n    // | \"2xs\"\r\n    // | \"3xs\"\r\n    | \"xs\"\r\n    | \"sm\"\r\n    | \"md\"\r\n    | \"lg\"\r\n    | \"xl\"\r\n    | \"2xl\"\r\n    | \"3xl\"\r\n    | \"4xl\"\r\n    | \"5xl\"\r\n    | \"6xl\"\r\n    | \"7xl\"\r\n  color?:\r\n    | \"primary\"\r\n    | \"secondary\"\r\n    | \"destructive\"\r\n    | \"grayscale\"\r\n    | \"accent\"\r\n    | \"background\"\r\n    | \"foreground\"\r\n    | \"white\"\r\n    | \"black\"\r\n  asChild?: boolean\r\n}\r\n\r\nconst Typography = React.forwardRef<\r\n  React.HTMLAttributes<HTMLElement>,\r\n  TypographyProps\r\n>(({ variant, color, size, children, component, className, ...props }, ref) => {\r\n  const Component = component || variant || \"p\"\r\n\r\n  return (\r\n    <Component\r\n      className={cn(typographyVariants({ variant, size, color }), className)}\r\n      // @ts-ignore\r\n      ref={ref as React.Ref<React.HTMLAttributes<HTMLElement>>}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </Component>\r\n  )\r\n})\r\n\r\nTypography.displayName = \"Typography\"\r\n\r\nexport default Typography\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,qBAAqB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAAE,aAAa;IAC1C,UAAU;QACR,SAAS;YACP,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YAEJ,OAAO;YACP,OAAO;YACP,SAAS;YACT,GAAG;YAEH,YAAY;YACZ,MAAM;YACN,KAAK;YACL,OAAO;QACT;QACA,OAAO;YACL,SAAS;YACT,WAAW;YACX,aAAa;YACb,WAAW;YACX,QAAQ;YACR,YAAY;YACZ,YAAY;YACZ,OAAO;YACP,OAAO;QACT;QACA,MAAM;YACJ,qBAAqB;YACrB,4CAA4C;YAC5C,IAAI;YACJ,IAAI;YACJ,MAAM;YAC<PERSON>,QAAQ;YACR,IAAI;YACJ,IAAI;YACJ,IAAI;YAC<PERSON>,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;QACT;QACA,iBAAiB;YACf,SAAS;YACT,OAAO;YACP,MAAM;QACR;IACF;AACF;AA+CA,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACrE,MAAM,YAAY,aAAa,WAAW;IAE1C,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;YAAE;YAAS;YAAM;QAAM,IAAI;QAC5D,aAAa;QACb,KAAK;QACJ,GAAG,KAAK;kBAER;;;;;;AAGP;AAEA,WAAW,WAAW,GAAG;uCAEV"}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/i18n/settings.ts"], "sourcesContent": ["export const fallbackLng = \"en\"\r\nexport const languages = [fallbackLng, \"de\"]\r\n\r\nexport const DEFAULT_LOCALE_LANGUAGE =\r\n  process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE || \"en\"\r\n\r\nexport const LANGUAGE_CONFIG_LIST = [\"en\", \"vi\"]\r\n\r\nexport const LANGUAGES_KEY_LIST = {\r\n  EN: \"en\",\r\n  VI: \"vi\",\r\n}\r\n\r\nexport enum E_LANGUAGES_KEY_LIST {\r\n  EN = \"en\",\r\n  VI = \"vi\",\r\n}\r\n\r\nexport type TLanguage = {\r\n  value: \"en\" | \"vi\"\r\n  label: string\r\n}\r\n\r\nexport const LANGUAGES_LIST: TLanguage[] = [\r\n  { value: \"en\", label: \"EN\" },\r\n  { value: \"vi\", label: \"VI\" },\r\n]\r\n\r\nexport const LANGUAGE_COOKIE_KEYS = {\r\n  LANGUAGE: \"Language-Cookie\",\r\n  GEOLOCATION: \"Geographic-Location\",\r\n}\r\n\r\nexport const getLanguage = (language: string) => {\r\n  if (language === E_LANGUAGES_KEY_LIST.EN) return \"en\"\r\n  if (language === E_LANGUAGES_KEY_LIST.VI) return \"vi\"\r\n  return \"vi\"\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAO,MAAM,cAAc;AACpB,MAAM,YAAY;IAAC;IAAa;CAAK;AAErC,MAAM,0BACX,0CAA4C;AAEvC,MAAM,uBAAuB;IAAC;IAAM;CAAK;AAEzC,MAAM,qBAAqB;IAChC,IAAI;IACJ,IAAI;AACN;AAEO,IAAA,AAAK,8CAAA;;;WAAA;;AAUL,MAAM,iBAA8B;IACzC;QAAE,OAAO;QAAM,OAAO;IAAK;IAC3B;QAAE,OAAO;QAAM,OAAO;IAAK;CAC5B;AAEM,MAAM,uBAAuB;IAClC,UAAU;IACV,aAAa;AACf;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI,mBAAsC,OAAO;IACjD,IAAI,mBAAsC,OAAO;IACjD,OAAO;AACT"}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 416, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/lib/util/text-translator.ts"], "sourcesContent": ["import { E_LANGUAGES_KEY_LIST, LANGUAGES_KEY_LIST } from \"i18n/settings\"\r\nimport { T_Text_Locale } from \"types/translation\"\r\n\r\nconst dataLocale = {\r\n  en: \"en:\",\r\n  vi: \"vi:\",\r\n}\r\n\r\nconst extractTextByLocale = (text: string, localePrefix: string) => {\r\n  return text.startsWith(localePrefix)\r\n    ? text.replace(localePrefix, \"\")\r\n    : undefined\r\n}\r\n\r\nexport const translateText = (text: string, i18nLocale?: string) => {\r\n  const splittedText = text.split(\"##\")\r\n\r\n  const dataReturn: T_Text_Locale = {\r\n    text,\r\n    en_text: extractTextByLocale(\r\n      splittedText.find((t) => t.startsWith(dataLocale.en)) || \"\",\r\n      dataLocale.en\r\n    ),\r\n    vi_text: extractTextByLocale(\r\n      splittedText.find((t) => t.startsWith(dataLocale.vi)) || \"\",\r\n      dataLocale.vi\r\n    ),\r\n    text_locale: undefined,\r\n  }\r\n\r\n  if (i18nLocale) {\r\n    const localeText = getTextLocale(\r\n      dataReturn,\r\n      i18nLocale as E_LANGUAGES_KEY_LIST\r\n    )\r\n    return { ...dataReturn, text_locale: localeText }\r\n  }\r\n\r\n  return dataReturn\r\n}\r\n\r\n// Get text locale\r\nexport const getTextLocale = (text: T_Text_Locale, locale: string) => {\r\n  switch (locale) {\r\n    case LANGUAGES_KEY_LIST.EN:\r\n      return text.en_text || text.text\r\n    case LANGUAGES_KEY_LIST.VI:\r\n      return text.vi_text || text.text\r\n    default:\r\n      return text.text\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAGA,MAAM,aAAa;IACjB,IAAI;IACJ,IAAI;AACN;AAEA,MAAM,sBAAsB,CAAC,MAAc;IACzC,OAAO,KAAK,UAAU,CAAC,gBACnB,KAAK,OAAO,CAAC,cAAc,MAC3B;AACN;AAEO,MAAM,gBAAgB,CAAC,MAAc;IAC1C,MAAM,eAAe,KAAK,KAAK,CAAC;IAEhC,MAAM,aAA4B;QAChC;QACA,SAAS,oBACP,aAAa,IAAI,CAAC,CAAC,IAAM,EAAE,UAAU,CAAC,WAAW,EAAE,MAAM,IACzD,WAAW,EAAE;QAEf,SAAS,oBACP,aAAa,IAAI,CAAC,CAAC,IAAM,EAAE,UAAU,CAAC,WAAW,EAAE,MAAM,IACzD,WAAW,EAAE;QAEf,aAAa;IACf;IAEA,IAAI,YAAY;QACd,MAAM,aAAa,cACjB,YACA;QAEF,OAAO;YAAE,GAAG,UAAU;YAAE,aAAa;QAAW;IAClD;IAEA,OAAO;AACT;AAGO,MAAM,gBAAgB,CAAC,MAAqB;IACjD,OAAQ;QACN,KAAK,uHAAA,CAAA,qBAAkB,CAAC,EAAE;YACxB,OAAO,KAAK,OAAO,IAAI,KAAK,IAAI;QAClC,KAAK,uHAAA,CAAA,qBAAkB,CAAC,EAAE;YACxB,OAAO,KAAK,OAAO,IAAI,KAAK,IAAI;QAClC;YACE,OAAO,KAAK,IAAI;IACpB;AACF"}}, {"offset": {"line": 456, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 462, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/layout/components/footer-menu/desktop.tsx"], "sourcesContent": ["import { translateText } from \"@lib/util/text-translator\"\r\nimport LocalizedClientLink from \"@modules/common/components/localized-client-link\"\r\nimport Typography from \"components/ui/typography\"\r\nimport { useTranslation } from \"react-i18next\"\r\nimport { T_MenuItem } from \"types/menu\"\r\n\r\ntype DesktopMenuProps = {\r\n  className?: string\r\n  dataMenu: T_MenuItem\r\n}\r\n\r\nconst FooterDesktopMenu = ({ dataMenu }: DesktopMenuProps) => {\r\n  const { i18n } = useTranslation()\r\n  const locale = i18n.language\r\n\r\n  const MenuItem = ({ item }: { item: T_MenuItem }) => {\r\n    return (\r\n      <div className=\"flex flex-col gap-y-6\">\r\n        <Typography variant=\"p\" size=\"base\" className=\"font-bold uppercase\">\r\n          {translateText(item.name, locale).text_locale}\r\n        </Typography>\r\n        {item.children && (\r\n          <ul className=\"flex flex-col gap-y-4\">\r\n            {item.children.map((child, childIdx) => {\r\n              return (\r\n                <li key={childIdx}>\r\n                  <LocalizedClientLink href={`/${child.url}`}>\r\n                    <Typography\r\n                      variant=\"p\"\r\n                      size=\"sm\"\r\n                      className=\"text-gray-800 hover:text-primary-main\"\r\n                    >\r\n                      {translateText(child.name, locale).text_locale}\r\n                    </Typography>\r\n                  </LocalizedClientLink>\r\n                </li>\r\n              )\r\n            })}\r\n          </ul>\r\n        )}\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {dataMenu?.children?.map((menuItem, idx) => (\r\n        <MenuItem key={idx} item={menuItem} />\r\n      ))}\r\n    </>\r\n  )\r\n}\r\n\r\nexport default FooterDesktopMenu\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;;;;;;AAQA,MAAM,oBAAoB,CAAC,EAAE,QAAQ,EAAoB;IACvD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,SAAS,KAAK,QAAQ;IAE5B,MAAM,WAAW,CAAC,EAAE,IAAI,EAAwB;QAC9C,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,UAAU;oBAAC,SAAQ;oBAAI,MAAK;oBAAO,WAAU;8BAC3C,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,IAAI,EAAE,QAAQ,WAAW;;;;;;gBAE9C,KAAK,QAAQ,kBACZ,8OAAC;oBAAG,WAAU;8BACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO;wBACzB,qBACE,8OAAC;sCACC,cAAA,8OAAC,+KAAA,CAAA,UAAmB;gCAAC,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE;0CACxC,cAAA,8OAAC,sIAAA,CAAA,UAAU;oCACT,SAAQ;oCACR,MAAK;oCACL,WAAU;8CAET,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,IAAI,EAAE,QAAQ,WAAW;;;;;;;;;;;2BAP3C;;;;;oBAYb;;;;;;;;;;;;IAKV;IAEA,qBACE;kBACG,UAAU,UAAU,IAAI,CAAC,UAAU,oBAClC,8OAAC;gBAAmB,MAAM;eAAX;;;;;;AAIvB;uCAEe"}}, {"offset": {"line": 543, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 549, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/layout/components/footer-menu/mobile.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { translateText } from \"@lib/util/text-translator\"\r\nimport LocalizedClientLink from \"@modules/common/components/localized-client-link\"\r\nimport Typography from \"components/ui/typography\"\r\nimport { useState } from \"react\"\r\nimport { useTranslation } from \"react-i18next\"\r\nimport { T_MenuItem } from \"types/menu\"\r\nimport { cn } from \"utils\"\r\n\r\ntype MobileMenuProps = {\r\n  menuFooter: T_MenuItem\r\n}\r\n\r\nconst MobileFooterMenu = ({ menuFooter }: MobileMenuProps) => {\r\n  const [openSection, setOpenSection] = useState<string | null>(null)\r\n  const { i18n } = useTranslation()\r\n  const locale = i18n.language\r\n\r\n  const toggleSection = (section: string) => {\r\n    setOpenSection((prevSection) => (prevSection === section ? null : section))\r\n  }\r\n\r\n  return (\r\n    <div className=\"mobile-footer-accordion flex w-full flex-col gap-2\">\r\n      {menuFooter?.children?.map((menuItem, idx) => (\r\n        <div key={idx} className=\"\">\r\n          <button\r\n            type=\"button\"\r\n            className=\"flex w-full touch-manipulation items-center justify-between gap-4 border-b border-black/30 px-1 py-3 transition-colors active:bg-gray-50\"\r\n            onClick={() => toggleSection(menuItem.name)}\r\n          >\r\n            <Typography\r\n              variant=\"p\"\r\n              size=\"base\"\r\n              className=\"text-left font-semibold\"\r\n            >\r\n              {translateText(menuItem.name, locale).text_locale}\r\n            </Typography>\r\n            <Typography\r\n              variant=\"span\"\r\n              size=\"lg\"\r\n              className=\"min-w-[20px] text-center font-bold text-gray-600\"\r\n            >\r\n              {openSection === menuItem.name ? \"−\" : \"+\"}\r\n            </Typography>\r\n          </button>\r\n\r\n          {/* Animated Content */}\r\n          <div\r\n            className={cn(\r\n              \"overflow-hidden transition-all duration-300 ease-in-out\",\r\n              openSection === menuItem.name\r\n                ? \"max-h-96 opacity-100\"\r\n                : \"max-h-0 opacity-0\"\r\n            )}\r\n          >\r\n            <div className=\"pb-2\">\r\n              <ul className=\"mt-2 flex flex-col gap-3 pl-2\">\r\n                {menuItem.children?.map((child, childIdx) => {\r\n                  if (!child.children?.length) {\r\n                    return (\r\n                      <li key={childIdx}>\r\n                        <LocalizedClientLink href={`/${child.url}`}>\r\n                          <Typography\r\n                            variant=\"p\"\r\n                            size=\"sm\"\r\n                            className=\"text-gray-700 transition-colors hover:text-primary-main\"\r\n                          >\r\n                            {translateText(child.name, locale).text_locale}\r\n                          </Typography>\r\n                        </LocalizedClientLink>\r\n                      </li>\r\n                    )\r\n                  }\r\n\r\n                  return (\r\n                    <li key={childIdx}>\r\n                      <LocalizedClientLink href={`/${child.url}`}>\r\n                        <Typography\r\n                          variant=\"p\"\r\n                          size=\"sm\"\r\n                          className=\"text-gray-700 transition-colors hover:text-primary-main\"\r\n                        >\r\n                          {translateText(child.name, locale).text_locale}\r\n                        </Typography>\r\n                      </LocalizedClientLink>\r\n                    </li>\r\n                  )\r\n                })}\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default MobileFooterMenu\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAFA;AANA;;;;;;;;AAcA,MAAM,mBAAmB,CAAC,EAAE,UAAU,EAAmB;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,SAAS,KAAK,QAAQ;IAE5B,MAAM,gBAAgB,CAAC;QACrB,eAAe,CAAC,cAAiB,gBAAgB,UAAU,OAAO;IACpE;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,YAAY,UAAU,IAAI,CAAC,UAAU,oBACpC,8OAAC;gBAAc,WAAU;;kCACvB,8OAAC;wBACC,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,cAAc,SAAS,IAAI;;0CAE1C,8OAAC,sIAAA,CAAA,UAAU;gCACT,SAAQ;gCACR,MAAK;gCACL,WAAU;0CAET,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,IAAI,EAAE,QAAQ,WAAW;;;;;;0CAEnD,8OAAC,sIAAA,CAAA,UAAU;gCACT,SAAQ;gCACR,MAAK;gCACL,WAAU;0CAET,gBAAgB,SAAS,IAAI,GAAG,MAAM;;;;;;;;;;;;kCAK3C,8OAAC;wBACC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,2DACA,gBAAgB,SAAS,IAAI,GACzB,yBACA;kCAGN,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CACX,SAAS,QAAQ,EAAE,IAAI,CAAC,OAAO;oCAC9B,IAAI,CAAC,MAAM,QAAQ,EAAE,QAAQ;wCAC3B,qBACE,8OAAC;sDACC,cAAA,8OAAC,+KAAA,CAAA,UAAmB;gDAAC,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE;0DACxC,cAAA,8OAAC,sIAAA,CAAA,UAAU;oDACT,SAAQ;oDACR,MAAK;oDACL,WAAU;8DAET,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,IAAI,EAAE,QAAQ,WAAW;;;;;;;;;;;2CAP3C;;;;;oCAYb;oCAEA,qBACE,8OAAC;kDACC,cAAA,8OAAC,+KAAA,CAAA,UAAmB;4CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE;sDACxC,cAAA,8OAAC,sIAAA,CAAA,UAAU;gDACT,SAAQ;gDACR,MAAK;gDACL,WAAU;0DAET,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,IAAI,EAAE,QAAQ,WAAW;;;;;;;;;;;uCAP3C;;;;;gCAYb;;;;;;;;;;;;;;;;;eA/DE;;;;;;;;;;AAuElB;uCAEe"}}, {"offset": {"line": 695, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 701, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/layout/components/logo/index.tsx"], "sourcesContent": ["import Image from \"next/image\"\r\n\r\ntype TLogoProps = {\r\n  logo?: string\r\n  isBlack?: boolean\r\n  size?: number\r\n}\r\n\r\nconst Logo: React.FC<TLogoProps> = ({\r\n  logo = \"/images/main_logo.png\",\r\n  size = 100,\r\n}) => {\r\n  return <Image src={logo} alt=\"Logo\" width={size} height={size} priority />\r\n}\r\n\r\nexport default Logo\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAQA,MAAM,OAA6B,CAAC,EAClC,OAAO,uBAAuB,EAC9B,OAAO,GAAG,EACX;IACC,qBAAO,8OAAC,6HAAA,CAAA,UAAK;QAAC,KAAK;QAAM,KAAI;QAAO,OAAO;QAAM,QAAQ;QAAM,QAAQ;;;;;;AACzE;uCAEe"}}, {"offset": {"line": 722, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 728, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"rounded-md inline-flex items-center justify-center gap-2 whitespace-nowrap text-xs md:text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none  [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        icon: \"p-0 bg-transparent border-none\",\r\n        default:\r\n          \"bg-primary-main text-primary-foreground shadow hover:bg-primary-main/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-gray-300 text-gray-700 font-medium bg-white shadow-sm hover:bg-primary-lighter hover:text-primary-main hover:border-primary-main\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary-main underline-offset-4 hover:underline\",\r\n        pagination:\r\n          \"hover:font-bold hover:text-primary-main hover:bg-gray-200 rounded-full\",\r\n        paginationActive:\r\n          \"bg-primary-lighter text-primary-main rounded-full font-bold\",\r\n      },\r\n      size: {\r\n        default: \"h-10 md:h-12 px-4 py-3\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n  VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,sSACA;IACE,UAAU;QACR,SAAS;YACP,MAAM;YACN,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,YACE;YACF,kBACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,sMAAM,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 785, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 791, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/layout/components/main-nav-content/header/contact-info.tsx"], "sourcesContent": ["import { Button } from \"components/ui/button\"\r\nimport Image from \"next/image\"\r\nimport Link from \"next/link\"\r\n\r\nconst CONTACT_PHONE = \"0906.70.70.15\"\r\nconst SOCIAL_LINKS = [\r\n  {\r\n    href: \"https://maps.app.goo.gl/yFHjtvZQyZXtQqhA9\",\r\n    icon: \"/images/footer/location.svg\",\r\n    alt: \"Location\",\r\n  },\r\n  {\r\n    href: \"https://www.facebook.com/efruit.vn\",\r\n    icon: \"/images/footer/facebook.svg\",\r\n    alt: \"Facebook\",\r\n  },\r\n  {\r\n    href: \"https://www.youtube.com/channel/UCK0sCgQW-NXBpQVbu6hOyJQ\",\r\n    icon: \"/images/footer/youtube.svg\",\r\n    alt: \"YouTube\",\r\n  },\r\n]\r\n\r\nexport default function ContactInfo() {\r\n  return (\r\n    <div className=\"flex items-center space-x-4\">\r\n      <PhoneContact />\r\n      {SOCIAL_LINKS.map(({ href, icon, alt }) => (\r\n        <SocialLink key={href} href={href} icon={icon} alt={alt} />\r\n      ))}\r\n    </div>\r\n  )\r\n}\r\n\r\nexport function PhoneContact() {\r\n  const phoneNumber = CONTACT_PHONE.replace(/\\./g, \"\")\r\n  const telLink = `tel:${phoneNumber}`\r\n\r\n  return (\r\n    <Link href={telLink} className=\"flex items-center space-x-2\">\r\n      <Image\r\n        src=\"/images/footer/phone.svg\"\r\n        alt=\"Phone\"\r\n        width={19}\r\n        height={19}\r\n      />\r\n      <span className=\"text-sm font-semibold text-primary-main\">\r\n        {CONTACT_PHONE}\r\n      </span>\r\n    </Link>\r\n  )\r\n}\r\n\r\ntype SocialLinkProps = { href: string; icon: string; alt: string }\r\n\r\nexport function SocialLink({ href, icon, alt }: SocialLinkProps) {\r\n  return (\r\n    <Link href={href} target=\"_blank\">\r\n      <Button variant=\"ghost\" size=\"icon\">\r\n        <Image src={icon} alt={alt} width={24} height={24} />\r\n      </Button>\r\n    </Link>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB;AACtB,MAAM,eAAe;IACnB;QACE,MAAM;QACN,MAAM;QACN,KAAK;IACP;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;IACP;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;IACP;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;;;;YACA,aAAa,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,iBACpC,8OAAC;oBAAsB,MAAM;oBAAM,MAAM;oBAAM,KAAK;mBAAnC;;;;;;;;;;;AAIzB;AAEO,SAAS;IACd,MAAM,cAAc,cAAc,OAAO,CAAC,OAAO;IACjD,MAAM,UAAU,CAAC,IAAI,EAAE,aAAa;IAEpC,qBACE,8OAAC,4HAAA,CAAA,UAAI;QAAC,MAAM;QAAS,WAAU;;0BAC7B,8OAAC,6HAAA,CAAA,UAAK;gBACJ,KAAI;gBACJ,KAAI;gBACJ,OAAO;gBACP,QAAQ;;;;;;0BAEV,8OAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT;AAIO,SAAS,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAmB;IAC7D,qBACE,8OAAC,4HAAA,CAAA,UAAI;QAAC,MAAM;QAAM,QAAO;kBACvB,cAAA,8OAAC,kIAAA,CAAA,SAAM;YAAC,SAAQ;YAAQ,MAAK;sBAC3B,cAAA,8OAAC,6HAAA,CAAA,UAAK;gBAAC,KAAK;gBAAM,KAAK;gBAAK,OAAO;gBAAI,QAAQ;;;;;;;;;;;;;;;;AAIvD"}}, {"offset": {"line": 907, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 913, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/utils/constant.ts"], "sourcesContent": ["export const STORE_INFORMATION = {\r\n  // name: \"Efruit\",\r\n  company_name: \"footer.company_name\",\r\n  company_location: \"footer.company_location\",\r\n  business_registration_number: \"footer.business_license_value\",\r\n  phone_hotline_value: \"footer.phone_hotline_value\",\r\n  email_address_value: \"footer.email_address_value\",\r\n  phone_sales: \"footer.phone_sales\",\r\n  food_safety_certificate_value: \"footer.food_safety_certificate_value\",\r\n}\r\n\r\nexport const GOOGLE_MAP_IFRAME =\r\n  \"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3766.088966315491!2d106.66466057804575!3d10.797367561899268!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3175292eb990d699%3A0x85834f5e740a9367!2zMzA1LzkgTmd1eeG7hW4gVHLhu41uZyBUdXnhu4NuLCBQaMaw4budbmcgMTAsIFBow7ogTmh14bqtbiwgSOG7kyBDaMOtIE1pbmgsIFZp4buHdCBOYW0!5e1!3m2!1svi!2s!4v1751202074734!5m2!1svi!2s\"\r\n\r\nexport const HANDLE_MENU = {\r\n  MAIN_MENU: \"main-menu\",\r\n  FOOTER_MENU: \"footer-menu\",\r\n  LEFT_MENU: \"left-menu\",\r\n  RIGHT_MENU: \"right-menu\",\r\n}\r\n\r\nexport const CHECKOUT_STEP = {\r\n  ADDRESS: \"address\",\r\n  SHIPPING: \"shipping\",\r\n  PAYMENT: \"payment\",\r\n  REVIEW: \"review\",\r\n}\r\n\r\nexport const DEFAULT_REGION = process.env.NEXT_PUBLIC_DEFAULT_REGION || \"vn\"\r\nexport const DEFAULT_LANGUAGE = process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE || \"vi\"\r\n\r\nexport const REGEX_PHONE =\r\n  /^(\\+\\d{1,2}\\s?)?\\(?\\d{3}\\)?[\\s.-]?\\d{3}[\\s.-]?\\d{4}$/\r\n"], "names": [], "mappings": ";;;;;;;;;AAAO,MAAM,oBAAoB;IAC/B,kBAAkB;IAClB,cAAc;IACd,kBAAkB;IAClB,8BAA8B;IAC9B,qBAAqB;IACrB,qBAAqB;IACrB,aAAa;IACb,+BAA+B;AACjC;AAEO,MAAM,oBACX;AAEK,MAAM,cAAc;IACzB,WAAW;IACX,aAAa;IACb,WAAW;IACX,YAAY;AACd;AAEO,MAAM,gBAAgB;IAC3B,SAAS;IACT,UAAU;IACV,SAAS;IACT,QAAQ;AACV;AAEO,MAAM,iBAAiB,0CAA0C;AACjE,MAAM,mBAAmB,0CAA4C;AAErE,MAAM,cACX"}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 954, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/layout/components/store-information/index.tsx"], "sourcesContent": ["\"use client\"\r\nimport Typography from \"components/ui/typography\"\r\nimport { useTranslation } from \"react-i18next\"\r\nimport { STORE_INFORMATION } from \"utils/constant\"\r\n\r\nexport default function StoreInformation() {\r\n  const { t } = useTranslation(\"layout\")\r\n\r\n  const informationItems = [\r\n    {\r\n      key: \"company_name\",\r\n      label: t(\"footer.company_name\"),\r\n      value: null,\r\n    },\r\n    {\r\n      key: \"company_location\",\r\n      label: t(STORE_INFORMATION.company_location),\r\n      value: null,\r\n    },\r\n    {\r\n      key: \"business_registration_number\",\r\n      label: t(\"footer.business_license\"),\r\n      value: t(STORE_INFORMATION.business_registration_number),\r\n    },\r\n    // {\r\n    //   key: \"email_address\",\r\n    //   label: t(\"footer.email\"),\r\n    //   value: t(STORE_INFORMATION.email_address_value),\r\n    // },\r\n    // {\r\n    //   key: \"phone_hotline\",\r\n    //   label: t(\"footer.phone_hotline_key\"),\r\n    //   value: t(STORE_INFORMATION.phone_hotline_value),\r\n    // },\r\n    {\r\n      key: \"food_safety_management_number\",\r\n      label: t(\"footer.food_safety_standards\"),\r\n      value: null,\r\n    },\r\n    {\r\n      key: \"food_safety_certificate_number\",\r\n      label: t(\"footer.food_safety_certificate_number\"),\r\n      value: t(STORE_INFORMATION.food_safety_certificate_value),\r\n    },\r\n  ]\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-2 md:gap-3\">\r\n      {informationItems.map(({ key, label, value }) => (\r\n        <Typography\r\n          key={key}\r\n          variant=\"p\"\r\n          size=\"sm\"\r\n          className=\"font-normal !leading-5\"\r\n        >\r\n          {value ? (\r\n            <>\r\n              {label}: {value}\r\n            </>\r\n          ) : (\r\n            <>{label}</>\r\n          )}\r\n        </Typography>\r\n      ))}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AADA;AAFA;;;;;AAKe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,MAAM,mBAAmB;QACvB;YACE,KAAK;YACL,OAAO,EAAE;YACT,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO,EAAE,wHAAA,CAAA,oBAAiB,CAAC,gBAAgB;YAC3C,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO,EAAE;YACT,OAAO,EAAE,wHAAA,CAAA,oBAAiB,CAAC,4BAA4B;QACzD;QACA,IAAI;QACJ,0BAA0B;QAC1B,8BAA8B;QAC9B,qDAAqD;QACrD,KAAK;QACL,IAAI;QACJ,0BAA0B;QAC1B,0CAA0C;QAC1C,qDAAqD;QACrD,KAAK;QACL;YACE,KAAK;YACL,OAAO,EAAE;YACT,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO,EAAE;YACT,OAAO,EAAE,wHAAA,CAAA,oBAAiB,CAAC,6BAA6B;QAC1D;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACZ,iBAAiB,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,iBAC1C,8OAAC,sIAAA,CAAA,UAAU;gBAET,SAAQ;gBACR,MAAK;gBACL,WAAU;0BAET,sBACC;;wBACG;wBAAM;wBAAG;;iDAGZ;8BAAG;;eAVA;;;;;;;;;;AAgBf"}}, {"offset": {"line": 1032, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1038, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/layout/components/subscribe-newsletter/index.tsx"], "sourcesContent": ["\"use client\"\r\nimport Typography from \"components/ui/typography\"\r\nimport useMediaQuery from \"hooks/use-media-query\"\r\nimport { useTranslation } from \"react-i18next\"\r\n\r\nexport default function SubscribeNewsletter() {\r\n  const { t } = useTranslation(\"layout\")\r\n  const isMobile = useMediaQuery(\"(max-width: 768px)\")\r\n\r\n  const contactInfo = [\r\n    {\r\n      label: t(\"footer.store\"),\r\n      value: t(\"footer.store_location\"),\r\n    },\r\n    {\r\n      label: t(\"footer.phone_hotline_key\"),\r\n      value: t(\"footer.phone_hotline_value\"),\r\n    },\r\n    { label: t(\"footer.email\"), value: t(\"footer.email_address_value\") },\r\n    { label: \"Facebook\", value: \"https://www.facebook.com/efruit.vn\" },\r\n  ]\r\n\r\n  return (\r\n    <div className=\"flex flex-col\">\r\n      <Typography variant=\"h6\" size=\"base\" className=\"font-semibold\">\r\n        {t(\"footer.contact\")}\r\n      </Typography>\r\n      {contactInfo.map(({ label, value }) => (\r\n        <Typography\r\n          key={label}\r\n          variant=\"p\"\r\n          size=\"sm\"\r\n          className=\"font-medium !leading-6 text-gray-700 md:mt-2\"\r\n        >\r\n          {label === \"Facebook\" && isMobile ? null : <strong>{label}: </strong>}\r\n          {label === \"Email\" ? (\r\n            <a\r\n              href={`mailto:${value}`}\r\n              className=\"cursor-pointer hover:text-primary-main\"\r\n            >\r\n              {value}\r\n            </a>\r\n          ) : label === \"Facebook\" ? (\r\n            <>\r\n              {!isMobile && (\r\n                <a\r\n                  href={value}\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"cursor-pointer hover:text-primary-main\"\r\n                >\r\n                  {value}\r\n                </a>\r\n              )}\r\n            </>\r\n          ) : (\r\n            value\r\n          )}\r\n        </Typography>\r\n      ))}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AAHA;;;;;AAKe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE;IAC7B,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAa,AAAD,EAAE;IAE/B,MAAM,cAAc;QAClB;YACE,OAAO,EAAE;YACT,OAAO,EAAE;QACX;QACA;YACE,OAAO,EAAE;YACT,OAAO,EAAE;QACX;QACA;YAAE,OAAO,EAAE;YAAiB,OAAO,EAAE;QAA8B;QACnE;YAAE,OAAO;YAAY,OAAO;QAAqC;KAClE;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAU;gBAAC,SAAQ;gBAAK,MAAK;gBAAO,WAAU;0BAC5C,EAAE;;;;;;YAEJ,YAAY,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,iBAChC,8OAAC,sIAAA,CAAA,UAAU;oBAET,SAAQ;oBACR,MAAK;oBACL,WAAU;;wBAET,UAAU,cAAc,WAAW,qBAAO,8OAAC;;gCAAQ;gCAAM;;;;;;;wBACzD,UAAU,wBACT,8OAAC;4BACC,MAAM,CAAC,OAAO,EAAE,OAAO;4BACvB,WAAU;sCAET;;;;;mCAED,UAAU,2BACZ;sCACG,CAAC,0BACA,8OAAC;gCACC,MAAM;gCACN,QAAO;gCACP,KAAI;gCACJ,WAAU;0CAET;;;;;;4CAKP;;mBA3BG;;;;;;;;;;;AAiCf"}}, {"offset": {"line": 1134, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1140, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/layout/components/footer-content/index.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport Image from \"next/image\"\r\nimport { useTranslation } from \"react-i18next\"\r\n\r\nimport { T_MenuByHandleResponse } from \"types/menu\"\r\nimport { T_SettingPreferences } from \"types/setting-preferences\"\r\n\r\nimport useMediaQuery from \"hooks/use-media-query\"\r\n\r\nimport { PAGE_PATH } from \"utils/path\"\r\n\r\nimport LocalizedClientLink from \"@modules/common/components/localized-client-link\"\r\nimport Typography from \"components/ui/typography\"\r\nimport FooterDesktopMenu from \"../footer-menu/desktop\"\r\nimport MobileFooterMenu from \"../footer-menu/mobile\"\r\nimport Logo from \"../logo\"\r\nimport {\r\n  PhoneContact,\r\n  SocialLink,\r\n} from \"../main-nav-content/header/contact-info\"\r\nimport StoreInformation from \"../store-information\"\r\nimport SubscribeNewsletter from \"../subscribe-newsletter\"\r\n\r\ntype FooterContentProps = {\r\n  settings: T_SettingPreferences\r\n  dataFooterMenu: T_MenuByHandleResponse\r\n}\r\n\r\nconst SOCIAL_LINKS = [\r\n  {\r\n    href: \"https://maps.app.goo.gl/yFHjtvZQyZXtQqhA9\",\r\n    icon: \"/images/footer/location.svg\",\r\n    alt: \"Location\",\r\n  },\r\n  {\r\n    href: \"https://www.facebook.com/efruit.vn\",\r\n    icon: \"/images/footer/facebook.svg\",\r\n    alt: \"Facebook\",\r\n  },\r\n  {\r\n    href: \"https://www.youtube.com/channel/UCK0sCgQW-NXBpQVbu6hOyJQ\",\r\n    icon: \"/images/footer/youtube.svg\",\r\n    alt: \"YouTube\",\r\n  },\r\n]\r\n\r\nconst FooterContent: React.FC<FooterContentProps> = ({\r\n  settings,\r\n  dataFooterMenu,\r\n}) => {\r\n  const isMobile = useMediaQuery(\"(max-width: 768px)\")\r\n  const isLgAndUp = useMediaQuery(\"(min-width: 1440px)\")\r\n  const { t } = useTranslation(\"layout\")\r\n\r\n  if (isMobile)\r\n    return (\r\n      <footer className=\"bg-gray-100 py-10\">\r\n        <div className=\"container mx-auto flex w-full flex-col gap-6 px-4\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <LocalizedClientLink href=\"/\">\r\n              <Logo logo={settings?.logo?.url} isBlack size={80} />\r\n            </LocalizedClientLink>\r\n            <PhoneContact />\r\n            {/* <SocialNetwork /> */}\r\n          </div>\r\n\r\n          <div className=\"flex w-full flex-col gap-4\">\r\n            <StoreInformation />\r\n          </div>\r\n\r\n          <div>\r\n            <SubscribeNewsletter />\r\n            <div className=\"relative mt-2 h-auto w-[120px]\">\r\n              <a\r\n                href=\"http://online.gov.vn/Home/WebDetails/39997?AspxAutoDetectCookieSupport=1\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"inline-block\"\r\n              >\r\n                <Image\r\n                  src=\"/images/bo-cong-thuong.png\"\r\n                  alt=\"Bo Cong Thuong\"\r\n                  width={120}\r\n                  height={120}\r\n                  className=\"h-full w-full cursor-pointer object-contain transition-opacity hover:opacity-80\"\r\n                />\r\n              </a>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"w-full overflow-hidden\">\r\n            <MobileFooterMenu menuFooter={dataFooterMenu.data} />\r\n          </div>\r\n          <div className=\"flex items-center justify-center\">\r\n            {SOCIAL_LINKS.map(({ href, icon, alt }) => (\r\n              <SocialLink key={href} href={href} icon={icon} alt={alt} />\r\n            ))}\r\n          </div>\r\n        </div>\r\n        <div className=\"mt-4 border-t border-gray-300 text-center\">\r\n          <Typography variant=\"p\" size=\"sm\" className=\"text-gray-600\">\r\n            {t(\"footer.copy_right\")}\r\n          </Typography>\r\n        </div>\r\n      </footer>\r\n    )\r\n\r\n  return (\r\n    <footer className=\"bg-gray-100/80 pb-12 pt-20\">\r\n      <div className=\"container grid grid-cols-1 gap-4 md:flex md:flex-row md:gap-8 xl:gap-16 2xl:justify-between\">\r\n        <div className=\"mb-4 flex w-full flex-col gap-4 md:max-w-sm\">\r\n          <div className=\"flex items-center gap-x-2\">\r\n            <LocalizedClientLink href={PAGE_PATH.HOME}>\r\n              <Logo size={80} />\r\n            </LocalizedClientLink>\r\n            {/* <Typography variant=\"h5\" className=\"text-primary-main\">\r\n              eFruit\r\n            </Typography> */}\r\n          </div>\r\n          <StoreInformation />\r\n        </div>\r\n        {isLgAndUp ? (\r\n          <div className=\"flex flex-1 justify-between gap-6\">\r\n            <FooterDesktopMenu\r\n              className=\"flex flex-row justify-between gap-4\"\r\n              dataMenu={dataFooterMenu.data}\r\n            />\r\n            <div className=\"lg:max-w-sm 2xl:max-w-md\">\r\n              <SubscribeNewsletter />\r\n              <div className=\"relative mt-6 w-[140px]\">\r\n                <a\r\n                  href=\"http://online.gov.vn/Home/WebDetails/39997?AspxAutoDetectCookieSupport=1\"\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"inline-block\"\r\n                >\r\n                  <Image\r\n                    src=\"/images/bo-cong-thuong.png\"\r\n                    alt=\"Bo Cong Thuong\"\r\n                    width={120}\r\n                    height={120}\r\n                    className=\"h-auto w-full cursor-pointer object-contain transition-opacity hover:opacity-80\"\r\n                  />\r\n                </a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <div className=\"flex flex-col gap-6\">\r\n            <div className=\"flex flex-1 justify-between gap-6\">\r\n              <FooterDesktopMenu dataMenu={dataFooterMenu.data} />\r\n            </div>\r\n            <div className=\"flex flex-col\">\r\n              <SubscribeNewsletter />\r\n              <div className=\"relative mt-6 h-auto w-[140px]\">\r\n                <a\r\n                  href=\"http://online.gov.vn/Home/WebDetails/39997?AspxAutoDetectCookieSupport=1\"\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"inline-block\"\r\n                >\r\n                  <Image\r\n                    src=\"/images/bo-cong-thuong.png\"\r\n                    alt=\"Bo Cong Thuong\"\r\n                    width={120}\r\n                    height={120}\r\n                    className=\"h-auto w-full cursor-pointer object-contain\"\r\n                  />\r\n                </a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n      <div className=\"mt-6 border-t border-gray-300 pt-6 text-center\">\r\n        <Typography variant=\"p\" size=\"sm\" className=\"text-gray-600\">\r\n          {t(\"footer.copy_right\")}\r\n        </Typography>\r\n      </div>\r\n    </footer>\r\n  )\r\n}\r\n\r\nexport default FooterContent\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAKA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AAnBA;AAHA;;;;;;;;;;;;;;AA6BA,MAAM,eAAe;IACnB;QACE,MAAM;QACN,MAAM;QACN,KAAK;IACP;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;IACP;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;IACP;CACD;AAED,MAAM,gBAA8C,CAAC,EACnD,QAAQ,EACR,cAAc,EACf;IACC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAa,AAAD,EAAE;IAC/B,MAAM,YAAY,CAAA,GAAA,qIAAA,CAAA,UAAa,AAAD,EAAE;IAChC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE;IAE7B,IAAI,UACF,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,+KAAA,CAAA,UAAmB;gCAAC,MAAK;0CACxB,cAAA,8OAAC,wJAAA,CAAA,UAAI;oCAAC,MAAM,UAAU,MAAM;oCAAK,OAAO;oCAAC,MAAM;;;;;;;;;;;0CAEjD,8OAAC,8LAAA,CAAA,eAAY;;;;;;;;;;;kCAIf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,wKAAA,CAAA,UAAgB;;;;;;;;;;kCAGnB,8OAAC;;0CACC,8OAAC,2KAAA,CAAA,UAAmB;;;;;0CACpB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;8CAEV,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAMlB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,mKAAA,CAAA,UAAgB;4BAAC,YAAY,eAAe,IAAI;;;;;;;;;;;kCAEnD,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,iBACpC,8OAAC,8LAAA,CAAA,aAAU;gCAAY,MAAM;gCAAM,MAAM;gCAAM,KAAK;+BAAnC;;;;;;;;;;;;;;;;0BAIvB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,sIAAA,CAAA,UAAU;oBAAC,SAAQ;oBAAI,MAAK;oBAAK,WAAU;8BACzC,EAAE;;;;;;;;;;;;;;;;;IAMb,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,+KAAA,CAAA,UAAmB;oCAAC,MAAM,oHAAA,CAAA,YAAS,CAAC,IAAI;8CACvC,cAAA,8OAAC,wJAAA,CAAA,UAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;0CAMhB,8OAAC,wKAAA,CAAA,UAAgB;;;;;;;;;;;oBAElB,0BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oKAAA,CAAA,UAAiB;gCAChB,WAAU;gCACV,UAAU,eAAe,IAAI;;;;;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2KAAA,CAAA,UAAmB;;;;;kDACpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;6CAOpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oKAAA,CAAA,UAAiB;oCAAC,UAAU,eAAe,IAAI;;;;;;;;;;;0CAElD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2KAAA,CAAA,UAAmB;;;;;kDACpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,sIAAA,CAAA,UAAU;oBAAC,SAAQ;oBAAI,MAAK;oBAAK,WAAU;8BACzC,EAAE;;;;;;;;;;;;;;;;;AAKb;uCAEe"}}, {"offset": {"line": 1535, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}