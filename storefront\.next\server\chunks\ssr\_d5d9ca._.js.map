{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/lib/data/pages.ts"], "sourcesContent": ["import { sdk } from \"@lib/config\"\r\nimport { cache } from \"react\"\r\nimport { T_Page, T_PageListResp, T_PageResponse } from \"types/pages\"\r\n\r\nexport const listPages = cache(async function () {\r\n  return sdk.client\r\n    .fetch(\"/store/cms/pages\", {\r\n      query: {\r\n        order: \"-created_at\",\r\n      },\r\n      headers: { next: { tags: [\"cms\", \"pages\"] } },\r\n    })\r\n    .then((res) => res as T_PageListResp)\r\n})\r\n\r\n// type T_PostListParams = {\r\n//   filters?: {\r\n//     page: number\r\n//     limit: number\r\n//     order?: string\r\n//     fields?: (keyof T_Post)[]\r\n//   }\r\n//   countryCode?: string\r\n//   fields?: (keyof T_Page)[]\r\n// }\r\n\r\n// export const getPagesList = cache(async function ({\r\n//   filters,\r\n//   countryCode = DEFAULT_LOCALE_LANGUAGE,\r\n//   fields,\r\n// }: T_PostListParams = {}) {\r\n//   const { page, limit, order } = filters || {}\r\n//   const region = await getRegion(countryCode)\r\n\r\n//   if (!region) {\r\n//     return {\r\n//       pages: [],\r\n//       paging: null,\r\n//     }\r\n//   }\r\n\r\n//   const fieldsString = fields ? fields.join(\",\") : undefined\r\n\r\n//   return sdk.client\r\n//     .fetch(\"/store/cms/pages\", {\r\n//       query: {\r\n//         limit,\r\n//         page,\r\n//         order: order || \"-created_at\",\r\n//         fields: fieldsString,\r\n//       },\r\n//       headers: { next: { tags: [\"cms\", \"pages\"] } },\r\n//     })\r\n//     .then((res) => res as any)\r\n//     .catch((err) => {\r\n//       console.error(\"Error fetching pages\", err)\r\n//       return {\r\n//         pages: [],\r\n//         paging: null,\r\n//       }\r\n//     })\r\n// })\r\n\r\nexport const getPageByHandle = cache(async function (handle: string): Promise<T_Page> {\r\n  return sdk.client\r\n    .fetch(`/store/cms/pages/${handle}`, {\r\n      // query: { handle },\r\n      headers: { next: { tags: [\"cms\", \"pages\"] } },\r\n    })\r\n    .then((res) => {\r\n      if (!res) {\r\n        throw new Error(\"Page not found\")\r\n      }\r\n\r\n      const { data } = res as T_PageResponse\r\n\r\n      return data as T_Page\r\n    })\r\n})\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE;IAC7B,OAAO,oHAAA,CAAA,MAAG,CAAC,MAAM,CACd,KAAK,CAAC,oBAAoB;QACzB,OAAO;YACL,OAAO;QACT;QACA,SAAS;YAAE,MAAM;gBAAE,MAAM;oBAAC;oBAAO;iBAAQ;YAAC;QAAE;IAC9C,GACC,IAAI,CAAC,CAAC,MAAQ;AACnB;AAkDO,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,eAAgB,MAAc;IACjE,OAAO,oHAAA,CAAA,MAAG,CAAC,MAAM,CACd,KAAK,CAAC,CAAC,iBAAiB,EAAE,QAAQ,EAAE;QACnC,qBAAqB;QACrB,SAAS;YAAE,MAAM;gBAAE,MAAM;oBAAC;oBAAO;iBAAQ;YAAC;QAAE;IAC9C,GACC,IAAI,CAAC,CAAC;QACL,IAAI,CAAC,KAAK;YACR,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,IAAI,EAAE,GAAG;QAEjB,OAAO;IACT;AACJ"}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/i18n/settings.ts"], "sourcesContent": ["export const fallbackLng = \"en\"\r\nexport const languages = [fallbackLng, \"de\"]\r\n\r\nexport const DEFAULT_LOCALE_LANGUAGE =\r\n  process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE || \"en\"\r\n\r\nexport const LANGUAGE_CONFIG_LIST = [\"en\", \"vi\"]\r\n\r\nexport const LANGUAGES_KEY_LIST = {\r\n  EN: \"en\",\r\n  VI: \"vi\",\r\n}\r\n\r\nexport enum E_LANGUAGES_KEY_LIST {\r\n  EN = \"en\",\r\n  VI = \"vi\",\r\n}\r\n\r\nexport type TLanguage = {\r\n  value: \"en\" | \"vi\"\r\n  label: string\r\n}\r\n\r\nexport const LANGUAGES_LIST: TLanguage[] = [\r\n  { value: \"en\", label: \"EN\" },\r\n  { value: \"vi\", label: \"VI\" },\r\n]\r\n\r\nexport const LANGUAGE_COOKIE_KEYS = {\r\n  LANGUAGE: \"Language-Cookie\",\r\n  GEOLOCATION: \"Geographic-Location\",\r\n}\r\n\r\nexport const getLanguage = (language: string) => {\r\n  if (language === E_LANGUAGES_KEY_LIST.EN) return \"en\"\r\n  if (language === E_LANGUAGES_KEY_LIST.VI) return \"vi\"\r\n  return \"vi\"\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAO,MAAM,cAAc;AACpB,MAAM,YAAY;IAAC;IAAa;CAAK;AAErC,MAAM,0BACX,0CAA4C;AAEvC,MAAM,uBAAuB;IAAC;IAAM;CAAK;AAEzC,MAAM,qBAAqB;IAChC,IAAI;IACJ,IAAI;AACN;AAEO,IAAA,AAAK,8CAAA;;;WAAA;;AAUL,MAAM,iBAA8B;IACzC;QAAE,OAAO;QAAM,OAAO;IAAK;IAC3B;QAAE,OAAO;QAAM,OAAO;IAAK;CAC5B;AAEM,MAAM,uBAAuB;IAClC,UAAU;IACV,aAAa;AACf;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI,mBAAsC,OAAO;IACjD,IAAI,mBAAsC,OAAO;IACjD,OAAO;AACT"}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/lib/util/structured-data.ts"], "sourcesContent": ["export interface OrganizationStructuredData {\r\n  name: string\r\n  url: string\r\n  logo: string\r\n  description: string\r\n  address?: {\r\n    streetAddress: string\r\n    addressLocality: string\r\n    addressRegion: string\r\n    postalCode: string\r\n    addressCountry: string\r\n  }\r\n  contactPoint?: {\r\n    telephone: string\r\n    contactType: string\r\n  }\r\n}\r\n\r\nexport interface ProductStructuredData {\r\n  name: string\r\n  description: string\r\n  image: string\r\n  url: string\r\n  sku: string\r\n  brand: string\r\n  category: string\r\n  price: number\r\n  priceCurrency: string\r\n  availability: \"InStock\" | \"OutOfStock\" | \"PreOrder\"\r\n  aggregateRating?: {\r\n    ratingValue: number\r\n    reviewCount: number\r\n  }\r\n}\r\n\r\nexport interface BreadcrumbStructuredData {\r\n  items: Array<{\r\n    name: string\r\n    url: string\r\n  }>\r\n}\r\n\r\nexport interface ArticleStructuredData {\r\n  headline: string\r\n  description: string\r\n  image: string\r\n  url: string\r\n  datePublished: string\r\n  dateModified: string\r\n  author: {\r\n    name: string\r\n    url: string\r\n  }\r\n  publisher: {\r\n    name: string\r\n    logo: string\r\n  }\r\n}\r\n\r\nexport function generateOrganizationStructuredData(\r\n  data: OrganizationStructuredData\r\n) {\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"Organization\",\r\n    name: data.name,\r\n    url: data.url,\r\n    logo: data.logo,\r\n    description: data.description,\r\n    ...(data.address && {\r\n      address: {\r\n        \"@type\": \"PostalAddress\",\r\n        streetAddress: data.address.streetAddress,\r\n        addressLocality: data.address.addressLocality,\r\n        addressRegion: data.address.addressRegion,\r\n        postalCode: data.address.postalCode,\r\n        addressCountry: data.address.addressCountry,\r\n      },\r\n    }),\r\n    ...(data.contactPoint && {\r\n      contactPoint: {\r\n        \"@type\": \"ContactPoint\",\r\n        telephone: data.contactPoint.telephone,\r\n        contactType: data.contactPoint.contactType,\r\n      },\r\n    }),\r\n  }\r\n}\r\n\r\nexport function generateProductStructuredData(data: ProductStructuredData) {\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"Product\",\r\n    name: data.name,\r\n    description: data.description,\r\n    image: data.image,\r\n    url: data.url,\r\n    sku: data.sku,\r\n    brand: {\r\n      \"@type\": \"Brand\",\r\n      name: data.brand,\r\n    },\r\n    category: data.category,\r\n    offers: {\r\n      \"@type\": \"Offer\",\r\n      price: data.price,\r\n      priceCurrency: data.priceCurrency,\r\n      availability: `https://schema.org/${data.availability}`,\r\n      url: data.url,\r\n    },\r\n    ...(data.aggregateRating && {\r\n      aggregateRating: {\r\n        \"@type\": \"AggregateRating\",\r\n        ratingValue: data.aggregateRating.ratingValue,\r\n        reviewCount: data.aggregateRating.reviewCount,\r\n      },\r\n    }),\r\n  }\r\n}\r\n\r\nexport function generateBreadcrumbStructuredData(\r\n  data: BreadcrumbStructuredData\r\n) {\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BreadcrumbList\",\r\n    itemListElement: data.items.map((item, index) => ({\r\n      \"@type\": \"ListItem\",\r\n      position: index + 1,\r\n      name: item.name,\r\n      item: item.url,\r\n    })),\r\n  }\r\n}\r\n\r\nexport function generateArticleStructuredData(data: ArticleStructuredData) {\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"Article\",\r\n    headline: data.headline,\r\n    description: data.description,\r\n    image: data.image,\r\n    url: data.url,\r\n    datePublished: data.datePublished,\r\n    dateModified: data.dateModified,\r\n    author: {\r\n      \"@type\": \"Person\",\r\n      name: data.author.name,\r\n      url: data.author.url,\r\n    },\r\n    publisher: {\r\n      \"@type\": \"Organization\",\r\n      name: data.publisher.name,\r\n      logo: {\r\n        \"@type\": \"ImageObject\",\r\n        url: data.publisher.logo,\r\n      },\r\n    },\r\n  }\r\n}\r\n\r\nexport function generateWebsiteStructuredData() {\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"WebSite\",\r\n    name: \"eFruit\",\r\n    url: \"https://efruit.vn\",\r\n    potentialAction: {\r\n      \"@type\": \"SearchAction\",\r\n      target: {\r\n        \"@type\": \"EntryPoint\",\r\n        urlTemplate: \"https://efruit.vn/search?q={search_term_string}\",\r\n      },\r\n      \"query-input\": \"required name=search_term_string\",\r\n    },\r\n  }\r\n}\r\n\r\nexport function generateLocalBusinessStructuredData() {\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"LocalBusiness\",\r\n    name: \"eFruit\",\r\n    description:\r\n      \"Fresh fruits and premium quality products delivered to your door\",\r\n    url: \"https://efruit.vn\",\r\n    telephone: \"+84-xxx-xxx-xxxx\",\r\n    address: {\r\n      \"@type\": \"PostalAddress\",\r\n      streetAddress: \"Your Street Address\",\r\n      addressLocality: \"Ho Chi Minh City\",\r\n      addressRegion: \"Ho Chi Minh\",\r\n      postalCode: \"70000\",\r\n      addressCountry: \"VN\",\r\n    },\r\n    geo: {\r\n      \"@type\": \"GeoCoordinates\",\r\n      latitude: 10.8231,\r\n      longitude: 106.6297,\r\n    },\r\n    openingHoursSpecification: [\r\n      {\r\n        \"@type\": \"OpeningHoursSpecification\",\r\n        dayOfWeek: [\r\n          \"Monday\",\r\n          \"Tuesday\",\r\n          \"Wednesday\",\r\n          \"Thursday\",\r\n          \"Friday\",\r\n          \"Saturday\",\r\n          \"Sunday\",\r\n        ],\r\n        opens: \"08:00\",\r\n        closes: \"22:00\",\r\n      },\r\n    ],\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AA2DO,SAAS,mCACd,IAAgC;IAEhC,OAAO;QACL,YAAY;QACZ,SAAS;QACT,MAAM,KAAK,IAAI;QACf,KAAK,KAAK,GAAG;QACb,MAAM,KAAK,IAAI;QACf,aAAa,KAAK,WAAW;QAC7B,GAAI,KAAK,OAAO,IAAI;YAClB,SAAS;gBACP,SAAS;gBACT,eAAe,KAAK,OAAO,CAAC,aAAa;gBACzC,iBAAiB,KAAK,OAAO,CAAC,eAAe;gBAC7C,eAAe,KAAK,OAAO,CAAC,aAAa;gBACzC,YAAY,KAAK,OAAO,CAAC,UAAU;gBACnC,gBAAgB,KAAK,OAAO,CAAC,cAAc;YAC7C;QACF,CAAC;QACD,GAAI,KAAK,YAAY,IAAI;YACvB,cAAc;gBACZ,SAAS;gBACT,WAAW,KAAK,YAAY,CAAC,SAAS;gBACtC,aAAa,KAAK,YAAY,CAAC,WAAW;YAC5C;QACF,CAAC;IACH;AACF;AAEO,SAAS,8BAA8B,IAA2B;IACvE,OAAO;QACL,YAAY;QACZ,SAAS;QACT,MAAM,KAAK,IAAI;QACf,aAAa,KAAK,WAAW;QAC7B,OAAO,KAAK,KAAK;QACjB,KAAK,KAAK,GAAG;QACb,KAAK,KAAK,GAAG;QACb,OAAO;YACL,SAAS;YACT,MAAM,KAAK,KAAK;QAClB;QACA,UAAU,KAAK,QAAQ;QACvB,QAAQ;YACN,SAAS;YACT,OAAO,KAAK,KAAK;YACjB,eAAe,KAAK,aAAa;YACjC,cAAc,CAAC,mBAAmB,EAAE,KAAK,YAAY,EAAE;YACvD,KAAK,KAAK,GAAG;QACf;QACA,GAAI,KAAK,eAAe,IAAI;YAC1B,iBAAiB;gBACf,SAAS;gBACT,aAAa,KAAK,eAAe,CAAC,WAAW;gBAC7C,aAAa,KAAK,eAAe,CAAC,WAAW;YAC/C;QACF,CAAC;IACH;AACF;AAEO,SAAS,iCACd,IAA8B;IAE9B,OAAO;QACL,YAAY;QACZ,SAAS;QACT,iBAAiB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAChD,SAAS;gBACT,UAAU,QAAQ;gBAClB,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,GAAG;YAChB,CAAC;IACH;AACF;AAEO,SAAS,8BAA8B,IAA2B;IACvE,OAAO;QACL,YAAY;QACZ,SAAS;QACT,UAAU,KAAK,QAAQ;QACvB,aAAa,KAAK,WAAW;QAC7B,OAAO,KAAK,KAAK;QACjB,KAAK,KAAK,GAAG;QACb,eAAe,KAAK,aAAa;QACjC,cAAc,KAAK,YAAY;QAC/B,QAAQ;YACN,SAAS;YACT,MAAM,KAAK,MAAM,CAAC,IAAI;YACtB,KAAK,KAAK,MAAM,CAAC,GAAG;QACtB;QACA,WAAW;YACT,SAAS;YACT,MAAM,KAAK,SAAS,CAAC,IAAI;YACzB,MAAM;gBACJ,SAAS;gBACT,KAAK,KAAK,SAAS,CAAC,IAAI;YAC1B;QACF;IACF;AACF;AAEO,SAAS;IACd,OAAO;QACL,YAAY;QACZ,SAAS;QACT,MAAM;QACN,KAAK;QACL,iBAAiB;YACf,SAAS;YACT,QAAQ;gBACN,SAAS;gBACT,aAAa;YACf;YACA,eAAe;QACjB;IACF;AACF;AAEO,SAAS;IACd,OAAO;QACL,YAAY;QACZ,SAAS;QACT,MAAM;QACN,aACE;QACF,KAAK;QACL,WAAW;QACX,SAAS;YACP,SAAS;YACT,eAAe;YACf,iBAAiB;YACjB,eAAe;YACf,YAAY;YACZ,gBAAgB;QAClB;QACA,KAAK;YACH,SAAS;YACT,UAAU;YACV,WAAW;QACb;QACA,2BAA2B;YACzB;gBACE,SAAS;gBACT,WAAW;oBACT;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,OAAO;gBACP,QAAQ;YACV;SACD;IACH;AACF"}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/pages/templates/pages-template.tsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/modules/pages/templates/pages-template.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/modules/pages/templates/pages-template.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkT,GAC/U,gFACA"}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/pages/templates/pages-template.tsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/modules/pages/templates/pages-template.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/modules/pages/templates/pages-template.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA"}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/ui/structured-data.tsx"], "sourcesContent": ["import Script from \"next/script\"\r\n\r\ninterface StructuredDataProps {\r\n  data: any\r\n}\r\n\r\nexport function StructuredData({ data }: StructuredDataProps) {\r\n  return (\r\n    <Script\r\n      id=\"structured-data\"\r\n      type=\"application/ld+json\"\r\n      dangerouslySetInnerHTML={{\r\n        __html: JSON.stringify(data),\r\n      }}\r\n    />\r\n  )\r\n} "], "names": [], "mappings": ";;;;AAAA;;;AAMO,SAAS,eAAe,EAAE,IAAI,EAAuB;IAC1D,qBACE,8OAAC,8HAAA,CAAA,UAAM;QACL,IAAG;QACH,MAAK;QACL,yBAAyB;YACvB,QAAQ,KAAK,SAAS,CAAC;QACzB;;;;;;AAGN"}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/app/%5BcountryCode%5D/%5BlocaleLanguage%5D/%28main%29/%28other-pages%29/pages/%5Bhandle%5D/page.tsx"], "sourcesContent": ["import { isEmpty } from \"lodash\"\r\nimport { Metadata } from \"next\"\r\nimport { notFound } from \"next/navigation\"\r\nimport { Suspense } from \"react\"\r\n\r\nimport { getPageByHandle, listPages } from \"@lib/data/pages\"\r\nimport { listRegions } from \"@lib/data/regions\"\r\nimport { LANGUAGES_LIST } from \"i18n/settings\"\r\n\r\nimport { generateBreadcrumbStructuredData } from \"@lib/util/structured-data\"\r\nimport PagesTemplate from \"@modules/pages/templates/pages-template\"\r\nimport LoadingOverlay from \"components/loading/loading-overlay\"\r\nimport { StructuredData } from \"components/ui/structured-data\"\r\n\r\n// -------------------------------------------------------------------------------------------------\r\n// Fallback: METADATA_FALLBACK\r\n// -------------------------------------------------------------------------------------------------\r\nconst METADATA_FALLBACK = {\r\n  title: \"Efruit Store\",\r\n  description: \"Efruit Page\",\r\n}\r\n\r\n// -------------------------------------------------------------------------------------------------\r\n// Dynamic: generateMetadata\r\n// -------------------------------------------------------------------------------------------------\r\nexport async function generateMetadata({\r\n  params,\r\n}: DynamicPageProps & {\r\n  params: Promise<{ handle: string; localeLanguage: string }>\r\n}): Promise<Metadata> {\r\n  const { handle, localeLanguage } = await params\r\n  const details = await getPageByHandle(handle)\r\n  const metadata = details?.metadata?.seo_metadata || {}\r\n\r\n  if (isEmpty(metadata)) {\r\n    return METADATA_FALLBACK\r\n  }\r\n\r\n  // Import translateText function to handle ##en:##vi: format\r\n  const { translateText } = await import(\"@lib/util/text-translator\")\r\n\r\n  const pageTitle = details?.title\r\n    ? translateText(details.title, localeLanguage).text_locale || details.title\r\n    : METADATA_FALLBACK.title\r\n\r\n  const pageContent = details?.content\r\n    ? translateText(details.content, localeLanguage).text_locale ||\r\n      details.content\r\n    : METADATA_FALLBACK.description\r\n\r\n  return {\r\n    title:\r\n      metadata.meta_title ||\r\n      (pageTitle ? `${pageTitle} | eFruit` : METADATA_FALLBACK.title),\r\n    description: metadata.meta_description || pageContent,\r\n    openGraph: {\r\n      title:\r\n        metadata.meta_title ||\r\n        (pageTitle ? `${pageTitle} | eFruit` : METADATA_FALLBACK.title),\r\n      description: metadata.meta_description || pageContent,\r\n      type: \"website\",\r\n      locale: localeLanguage === \"vi\" ? \"vi_VN\" : \"en_US\",\r\n      images: details?.image\r\n        ? [\r\n            {\r\n              url: details.image,\r\n              width: 1200,\r\n              height: 630,\r\n              alt: pageTitle || \"eFruit Page\",\r\n            },\r\n          ]\r\n        : [\r\n            {\r\n              url: \"/images/efruit-page-default.jpg\",\r\n              width: 1200,\r\n              height: 630,\r\n              alt: \"eFruit Page\",\r\n            },\r\n          ],\r\n    },\r\n    twitter: {\r\n      card: \"summary_large_image\",\r\n      title:\r\n        metadata.meta_title ||\r\n        (pageTitle ? `${pageTitle} | eFruit` : METADATA_FALLBACK.title),\r\n      description: metadata.meta_description || pageContent,\r\n      images: details?.image\r\n        ? [details.image]\r\n        : [\"/images/efruit-page-default.jpg\"],\r\n    },\r\n    alternates: {\r\n      canonical: `/pages/${handle}`,\r\n    },\r\n    icons: details?.image ? [details.image] : [],\r\n  }\r\n}\r\n\r\n// -------------------------------------------------------------------------------------------------\r\n// Page: DynamicPage\r\n// -------------------------------------------------------------------------------------------------\r\ntype DynamicPageProps = {\r\n  params: Promise<{ handle: string; localeLanguage: string }>\r\n}\r\nexport default async function DynamicPage({ params }: DynamicPageProps) {\r\n  const { handle } = await params\r\n  const details = await getPageByHandle(handle)\r\n  console.log(\"🚀 ~ DynamicPage ~ details:\", details)\r\n  if (!details) {\r\n    return notFound()\r\n  }\r\n\r\n  // Generate breadcrumb structured data\r\n  const breadcrumbItems = [\r\n    { name: \"Home\", url: \"/\" },\r\n    { name: details?.title || \"Page\", url: `/pages/${handle}` },\r\n  ]\r\n  const breadcrumbStructuredData = generateBreadcrumbStructuredData({\r\n    items: breadcrumbItems,\r\n  })\r\n\r\n  return (\r\n    <>\r\n      <StructuredData data={breadcrumbStructuredData} />\r\n      <Suspense fallback={<LoadingOverlay />}>\r\n        <PagesTemplate details={details} />\r\n      </Suspense>\r\n    </>\r\n  )\r\n}\r\n\r\n// -------------------------------------------------------------------------------------------------\r\n// Dynamic: generateStaticParams\r\n// -------------------------------------------------------------------------------------------------\r\nexport async function generateStaticParams() {\r\n  const regions = await listRegions()\r\n\r\n  const countryCodes =\r\n    regions?.flatMap((r) => r.countries?.map((c) => c.iso_2) || []) || []\r\n\r\n  if (!countryCodes.length) return []\r\n\r\n  const pagesResp = await listPages()\r\n\r\n  const pages = pagesResp?.pages || []\r\n\r\n  if (!pages) {\r\n    return []\r\n  }\r\n\r\n  const staticParams = pagesResp.pages.flatMap((item) =>\r\n    countryCodes.flatMap((countryCode) =>\r\n      LANGUAGES_LIST.map((localeLanguage) => ({\r\n        countryCode,\r\n        localeLanguage: localeLanguage.value,\r\n        handle: item.handle,\r\n      }))\r\n    )\r\n  )\r\n  return staticParams\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAVA;;;;;;;;;;;;AAYA,oGAAoG;AACpG,8BAA8B;AAC9B,oGAAoG;AACpG,MAAM,oBAAoB;IACxB,OAAO;IACP,aAAa;AACf;AAKO,eAAe,iBAAiB,EACrC,MAAM,EAGP;IACC,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,GAAG,MAAM;IACzC,MAAM,UAAU,MAAM,CAAA,GAAA,2HAAA,CAAA,kBAAe,AAAD,EAAE;IACtC,MAAM,WAAW,SAAS,UAAU,gBAAgB,CAAC;IAErD,IAAI,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD,EAAE,WAAW;QACrB,OAAO;IACT;IAEA,4DAA4D;IAC5D,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,MAAM,YAAY,SAAS,QACvB,cAAc,QAAQ,KAAK,EAAE,gBAAgB,WAAW,IAAI,QAAQ,KAAK,GACzE,kBAAkB,KAAK;IAE3B,MAAM,cAAc,SAAS,UACzB,cAAc,QAAQ,OAAO,EAAE,gBAAgB,WAAW,IAC1D,QAAQ,OAAO,GACf,kBAAkB,WAAW;IAEjC,OAAO;QACL,OACE,SAAS,UAAU,IACnB,CAAC,YAAY,GAAG,UAAU,SAAS,CAAC,GAAG,kBAAkB,KAAK;QAChE,aAAa,SAAS,gBAAgB,IAAI;QAC1C,WAAW;YACT,OACE,SAAS,UAAU,IACnB,CAAC,YAAY,GAAG,UAAU,SAAS,CAAC,GAAG,kBAAkB,KAAK;YAChE,aAAa,SAAS,gBAAgB,IAAI;YAC1C,MAAM;YACN,QAAQ,mBAAmB,OAAO,UAAU;YAC5C,QAAQ,SAAS,QACb;gBACE;oBACE,KAAK,QAAQ,KAAK;oBAClB,OAAO;oBACP,QAAQ;oBACR,KAAK,aAAa;gBACpB;aACD,GACD;gBACE;oBACE,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,KAAK;gBACP;aACD;QACP;QACA,SAAS;YACP,MAAM;YACN,OACE,SAAS,UAAU,IACnB,CAAC,YAAY,GAAG,UAAU,SAAS,CAAC,GAAG,kBAAkB,KAAK;YAChE,aAAa,SAAS,gBAAgB,IAAI;YAC1C,QAAQ,SAAS,QACb;gBAAC,QAAQ,KAAK;aAAC,GACf;gBAAC;aAAkC;QACzC;QACA,YAAY;YACV,WAAW,CAAC,OAAO,EAAE,QAAQ;QAC/B;QACA,OAAO,SAAS,QAAQ;YAAC,QAAQ,KAAK;SAAC,GAAG,EAAE;IAC9C;AACF;AAQe,eAAe,YAAY,EAAE,MAAM,EAAoB;IACpE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,UAAU,MAAM,CAAA,GAAA,2HAAA,CAAA,kBAAe,AAAD,EAAE;IACtC,QAAQ,GAAG,CAAC,+BAA+B;IAC3C,IAAI,CAAC,SAAS;QACZ,OAAO,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IAChB;IAEA,sCAAsC;IACtC,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAQ,KAAK;QAAI;QACzB;YAAE,MAAM,SAAS,SAAS;YAAQ,KAAK,CAAC,OAAO,EAAE,QAAQ;QAAC;KAC3D;IACD,MAAM,2BAA2B,CAAA,GAAA,wIAAA,CAAA,mCAAgC,AAAD,EAAE;QAChE,OAAO;IACT;IAEA,qBACE;;0BACE,8OAAC,8IAAA,CAAA,iBAAc;gBAAC,MAAM;;;;;;0BACtB,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBAAU,8OAAC,mJAAA,CAAA,UAAc;;;;;0BACjC,cAAA,8OAAC,0JAAA,CAAA,UAAa;oBAAC,SAAS;;;;;;;;;;;;;AAIhC;AAKO,eAAe;IACpB,MAAM,UAAU,MAAM,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD;IAEhC,MAAM,eACJ,SAAS,QAAQ,CAAC,IAAM,EAAE,SAAS,EAAE,IAAI,CAAC,IAAM,EAAE,KAAK,KAAK,EAAE,KAAK,EAAE;IAEvE,IAAI,CAAC,aAAa,MAAM,EAAE,OAAO,EAAE;IAEnC,MAAM,YAAY,MAAM,CAAA,GAAA,2HAAA,CAAA,YAAS,AAAD;IAEhC,MAAM,QAAQ,WAAW,SAAS,EAAE;IAEpC,uCAAY;;IAEZ;IAEA,MAAM,eAAe,UAAU,KAAK,CAAC,OAAO,CAAC,CAAC,OAC5C,aAAa,OAAO,CAAC,CAAC,cACpB,uHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC,iBAAmB,CAAC;oBACtC;oBACA,gBAAgB,eAAe,KAAK;oBACpC,QAAQ,KAAK,MAAM;gBACrB,CAAC;IAGL,OAAO;AACT"}}, {"offset": {"line": 492, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 504, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/lib/data/onboarding.ts"], "sourcesContent": ["\"use server\"\r\nimport { cookies as nextCookies } from \"next/headers\"\r\nimport { redirect } from \"next/navigation\"\r\n\r\nexport async function resetOnboardingState(orderId: string) {\r\n  const cookies = await nextCookies()\r\n  cookies.set(\"_medusa_onboarding\", \"false\", { maxAge: -1 })\r\n  redirect(`http://localhost:7001/a/orders/${orderId}`)\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;;AAAA;;;;;AAEO,eAAe,qBAAqB,OAAe;IACxD,MAAM,UAAU,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAW,AAAD;IAChC,QAAQ,GAAG,CAAC,sBAAsB,SAAS;QAAE,QAAQ,CAAC;IAAE;IACxD,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,+BAA+B,EAAE,SAAS;AACtD;;;IAJsB;;AAAA,+OAAA"}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/lib/data/orders.ts"], "sourcesContent": ["\"use server\"\r\n\r\nimport { sdk } from \"@lib/config\"\r\nimport medusaError from \"@lib/util/medusa-error\"\r\nimport { HttpTypes } from \"@medusajs/types\"\r\nimport { getAuthHeaders, getCacheOptions } from \"./cookies\"\r\n\r\nexport const retrieveOrder = async (id: string) => {\r\n  const headers = {\r\n    ...(await getAuthHeaders()),\r\n  }\r\n\r\n  const next = {\r\n    ...(await getCacheOptions(\"orders\")),\r\n  }\r\n  const fieldDefault = [\r\n    \"*cart\",\r\n    \"*payment_collections.payments\",\r\n    \"*items\",\r\n    \"*items.metadata\",\r\n    \"*items.variant\",\r\n    \"*items.product\",\r\n    \"*product_reviews.*\",\r\n  ]\r\n\r\n  return sdk.client\r\n    .fetch<HttpTypes.StoreOrderResponse>(`/store/orders/${id}`, {\r\n      method: \"GET\",\r\n      query: {\r\n        fields: fieldDefault.join(\",\"),\r\n      },\r\n      headers,\r\n      next,\r\n    })\r\n    .then(({ order }) => {\r\n      return order\r\n    })\r\n    .catch((err) => medusaError(err))\r\n}\r\n\r\nexport const listOrders = async ({\r\n  limit = 10,\r\n  offset = 0,\r\n  filters,\r\n  queryString,\r\n}: {\r\n  limit?: number\r\n  offset?: number\r\n  filters?: Record<string, any>\r\n  queryString?: string\r\n}) => {\r\n  const headers = {\r\n    ...(await getAuthHeaders()),\r\n  }\r\n\r\n  const next = {\r\n    ...(await getCacheOptions(\"orders\")),\r\n  }\r\n\r\n  return sdk.client\r\n    .fetch<HttpTypes.StoreOrderListResponse>(`/store/orders`, {\r\n      method: \"GET\",\r\n      query: {\r\n        limit,\r\n        offset,\r\n        order: \"-created_at\",\r\n        fields:\r\n          \"*items,+items.metadata,*items.variant,*items.product\" +\r\n          (queryString ? `,${queryString}` : \"\"),\r\n        ...filters,\r\n      },\r\n      headers,\r\n      next,\r\n    })\r\n    .then(({ orders }) => orders)\r\n    .catch((err) => medusaError(err))\r\n}\r\n\r\nexport const createTransferRequest = async (\r\n  state: {\r\n    success: boolean\r\n    error: string | null\r\n    order: HttpTypes.StoreOrder | null\r\n  },\r\n  formData: FormData\r\n): Promise<{\r\n  success: boolean\r\n  error: string | null\r\n  order: HttpTypes.StoreOrder | null\r\n}> => {\r\n  const id = formData.get(\"order_id\") as string\r\n\r\n  if (!id) {\r\n    return { success: false, error: \"Order ID is required\", order: null }\r\n  }\r\n\r\n  const headers = await getAuthHeaders()\r\n\r\n  return await sdk.store.order\r\n    .requestTransfer(\r\n      id,\r\n      {},\r\n      {\r\n        fields: \"id, email\",\r\n      },\r\n      headers\r\n    )\r\n    .then(({ order }) => ({ success: true, error: null, order }))\r\n    .catch((err) => ({ success: false, error: err.message, order: null }))\r\n}\r\n\r\nexport const acceptTransferRequest = async (id: string, token: string) => {\r\n  const headers = await getAuthHeaders()\r\n\r\n  return await sdk.store.order\r\n    .acceptTransfer(id, { token }, {}, headers)\r\n    .then(({ order }) => ({ success: true, error: null, order }))\r\n    .catch((err) => ({ success: false, error: err.message, order: null }))\r\n}\r\n\r\nexport const declineTransferRequest = async (id: string, token: string) => {\r\n  const headers = await getAuthHeaders()\r\n\r\n  return await sdk.store.order\r\n    .declineTransfer(id, { token }, {}, headers)\r\n    .then(({ order }) => ({ success: true, error: null, order }))\r\n    .catch((err) => ({ success: false, error: err.message, order: null }))\r\n}\r\n\r\nexport const checkOrderAllowReview = async ({\r\n  orderId,\r\n  productId,\r\n}: {\r\n  orderId: string\r\n  productId: string\r\n}) => {\r\n  const headers = {\r\n    ...(await getAuthHeaders()),\r\n  }\r\n  return sdk.client\r\n    .fetch<HttpTypes.StoreOrderListResponse>(\r\n      `/store/product-reviews/${productId}/status`,\r\n      {\r\n        method: \"GET\",\r\n        query: {\r\n          order_id: orderId,\r\n        },\r\n        headers,\r\n      }\r\n    )\r\n    .then((res) => res)\r\n    .catch((err) => medusaError(err))\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AAEA;;;;;;;AAEO,MAAM,gBAAgB,OAAO;IAClC,MAAM,UAAU;QACd,GAAI,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,GAAG;IAC5B;IAEA,MAAM,OAAO;QACX,GAAI,MAAM,CAAA,GAAA,6HAAA,CAAA,kBAAe,AAAD,EAAE,SAAS;IACrC;IACA,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,oHAAA,CAAA,MAAG,CAAC,MAAM,CACd,KAAK,CAA+B,CAAC,cAAc,EAAE,IAAI,EAAE;QAC1D,QAAQ;QACR,OAAO;YACL,QAAQ,aAAa,IAAI,CAAC;QAC5B;QACA;QACA;IACF,GACC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE;QACd,OAAO;IACT,GACC,KAAK,CAAC,CAAC,MAAQ,CAAA,GAAA,qIAAA,CAAA,UAAW,AAAD,EAAE;AAChC;AAEO,MAAM,aAAa,OAAO,EAC/B,QAAQ,EAAE,EACV,SAAS,CAAC,EACV,OAAO,EACP,WAAW,EAMZ;IACC,MAAM,UAAU;QACd,GAAI,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,GAAG;IAC5B;IAEA,MAAM,OAAO;QACX,GAAI,MAAM,CAAA,GAAA,6HAAA,CAAA,kBAAe,AAAD,EAAE,SAAS;IACrC;IAEA,OAAO,oHAAA,CAAA,MAAG,CAAC,MAAM,CACd,KAAK,CAAmC,CAAC,aAAa,CAAC,EAAE;QACxD,QAAQ;QACR,OAAO;YACL;YACA;YACA,OAAO;YACP,QACE,yDACA,CAAC,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,EAAE;YACvC,GAAG,OAAO;QACZ;QACA;QACA;IACF,GACC,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,GAAK,QACrB,KAAK,CAAC,CAAC,MAAQ,CAAA,GAAA,qIAAA,CAAA,UAAW,AAAD,EAAE;AAChC;AAEO,MAAM,wBAAwB,OACnC,OAKA;IAMA,MAAM,KAAK,SAAS,GAAG,CAAC;IAExB,IAAI,CAAC,IAAI;QACP,OAAO;YAAE,SAAS;YAAO,OAAO;YAAwB,OAAO;QAAK;IACtE;IAEA,MAAM,UAAU,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD;IAEnC,OAAO,MAAM,oHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,KAAK,CACzB,eAAe,CACd,IACA,CAAC,GACD;QACE,QAAQ;IACV,GACA,SAED,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;YAAE,SAAS;YAAM,OAAO;YAAM;QAAM,CAAC,GAC1D,KAAK,CAAC,CAAC,MAAQ,CAAC;YAAE,SAAS;YAAO,OAAO,IAAI,OAAO;YAAE,OAAO;QAAK,CAAC;AACxE;AAEO,MAAM,wBAAwB,OAAO,IAAY;IACtD,MAAM,UAAU,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD;IAEnC,OAAO,MAAM,oHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,KAAK,CACzB,cAAc,CAAC,IAAI;QAAE;IAAM,GAAG,CAAC,GAAG,SAClC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;YAAE,SAAS;YAAM,OAAO;YAAM;QAAM,CAAC,GAC1D,KAAK,CAAC,CAAC,MAAQ,CAAC;YAAE,SAAS;YAAO,OAAO,IAAI,OAAO;YAAE,OAAO;QAAK,CAAC;AACxE;AAEO,MAAM,yBAAyB,OAAO,IAAY;IACvD,MAAM,UAAU,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD;IAEnC,OAAO,MAAM,oHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,KAAK,CACzB,eAAe,CAAC,IAAI;QAAE;IAAM,GAAG,CAAC,GAAG,SACnC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;YAAE,SAAS;YAAM,OAAO;YAAM;QAAM,CAAC,GAC1D,KAAK,CAAC,CAAC,MAAQ,CAAC;YAAE,SAAS;YAAO,OAAO,IAAI,OAAO;YAAE,OAAO;QAAK,CAAC;AACxE;AAEO,MAAM,wBAAwB,OAAO,EAC1C,OAAO,EACP,SAAS,EAIV;IACC,MAAM,UAAU;QACd,GAAI,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,GAAG;IAC5B;IACA,OAAO,oHAAA,CAAA,MAAG,CAAC,MAAM,CACd,KAAK,CACJ,CAAC,uBAAuB,EAAE,UAAU,OAAO,CAAC,EAC5C;QACE,QAAQ;QACR,OAAO;YACL,UAAU;QACZ;QACA;IACF,GAED,IAAI,CAAC,CAAC,MAAQ,KACd,KAAK,CAAC,CAAC,MAAQ,CAAA,GAAA,qIAAA,CAAA,UAAW,AAAD,EAAE;AAChC;;;IAjJa;IAiCA;IAsCA;IAiCA;IASA;IASA;;AA1HA,+OAAA;AAiCA,+OAAA;AAsCA,+OAAA;AAiCA,+OAAA;AASA,+OAAA;AASA,+OAAA"}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 684, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/lib/data/fulfillment.ts"], "sourcesContent": ["\"use server\"\r\n\r\nimport { sdk } from \"@lib/config\"\r\nimport { HttpTypes } from \"@medusajs/types\"\r\nimport { getAuthHeaders, getCacheOptions } from \"./cookies\"\r\n\r\nexport const listCartShippingMethods = async (cartId: string) => {\r\n  const headers = {\r\n    ...(await getAuthHeaders()),\r\n  }\r\n\r\n  const next = {\r\n    ...(await getCacheOptions(\"fulfillment\")),\r\n  }\r\n\r\n  try {\r\n    const response =\r\n      await sdk.client.fetch<HttpTypes.StoreShippingOptionListResponse>(\r\n        `/store/shipping-options`,\r\n        {\r\n          method: \"GET\",\r\n          query: { cart_id: cartId },\r\n          headers,\r\n          next,\r\n        }\r\n      )\r\n\r\n    return response.shipping_options\r\n  } catch (err) {\r\n    console.error(\"Shipping Options Error:\", err)\r\n    return null\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;;;;AAEO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,UAAU;QACd,GAAI,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,GAAG;IAC5B;IAEA,MAAM,OAAO;QACX,GAAI,MAAM,CAAA,GAAA,6HAAA,CAAA,kBAAe,AAAD,EAAE,cAAc;IAC1C;IAEA,IAAI;QACF,MAAM,WACJ,MAAM,oHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,KAAK,CACpB,CAAC,uBAAuB,CAAC,EACzB;YACE,QAAQ;YACR,OAAO;gBAAE,SAAS;YAAO;YACzB;YACA;QACF;QAGJ,OAAO,SAAS,gBAAgB;IAClC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF;;;IA1Ba;;AAAA,+OAAA"}}, {"offset": {"line": 723, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/lib/data/payment.ts"], "sourcesContent": ["\"use server\"\r\n\r\nimport { sdk } from \"@lib/config\"\r\nimport { getAuthHeaders, getCacheOptions } from \"./cookies\"\r\nimport { HttpTypes } from \"@medusajs/types\"\r\n\r\nexport const listCartPaymentMethods = async (regionId: string) => {\r\n  const headers = {\r\n    ...(await getAuthHeaders()),\r\n  }\r\n\r\n  const next = {\r\n    ...(await getCacheOptions(\"payment_providers\")),\r\n  }\r\n\r\n  return sdk.client\r\n    .fetch<HttpTypes.StorePaymentProviderListResponse>(\r\n      `/store/payment-providers`,\r\n      {\r\n        method: \"GET\",\r\n        query: { region_id: regionId },\r\n        headers,\r\n        next,\r\n      }\r\n    )\r\n    .then(({ payment_providers }) => payment_providers)\r\n    .catch(() => {\r\n      return null\r\n    })\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;;;;AAGO,MAAM,yBAAyB,OAAO;IAC3C,MAAM,UAAU;QACd,GAAI,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,GAAG;IAC5B;IAEA,MAAM,OAAO;QACX,GAAI,MAAM,CAAA,GAAA,6HAAA,CAAA,kBAAe,AAAD,EAAE,oBAAoB;IAChD;IAEA,OAAO,oHAAA,CAAA,MAAG,CAAC,MAAM,CACd,KAAK,CACJ,CAAC,wBAAwB,CAAC,EAC1B;QACE,QAAQ;QACR,OAAO;YAAE,WAAW;QAAS;QAC7B;QACA;IACF,GAED,IAAI,CAAC,CAAC,EAAE,iBAAiB,EAAE,GAAK,mBAChC,KAAK,CAAC;QACL,OAAO;IACT;AACJ;;;IAvBa;;AAAA,+OAAA"}}, {"offset": {"line": 764, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 770, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 826, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}