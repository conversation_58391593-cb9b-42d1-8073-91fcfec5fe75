"use client"

import Image from "next/image"
import { useMemo } from "react"
import { useTranslation } from "react-i18next"

import { getTranslation } from "@lib/util/translation"
import { Heading } from "@medusajs/ui"
import { LexicalContent } from "components/lexical-content"
import { T_Page } from "types/pages"

type LocalizedContent = Partial<Record<"en" | "vi" | string, string>>

interface PagesTemplateProps {
  details: T_Page & {
    content?: string | LocalizedContent
    title?: string | LocalizedContent
  }
}

// Extract locale resolution logic into a reusable hook
const useLocaleString = () => {
  const { i18n } = useTranslation()

  return useMemo(() => {
    return (field: string | LocalizedContent | undefined): string => {
      if (!field) return ""

      if (typeof field === "object" && field !== null) {
        return field[i18n.language] || field.vi || field.en || ""
      }

      if (typeof field === "string") {
        try {
          const parsed = JSON.parse(field)
          if (typeof parsed === "object" && parsed !== null) {
            return parsed[i18n.language] || parsed.vi || parsed.en || ""
          }
          return field
        } catch {
          return field
        }
      }

      return ""
    }
  }, [i18n.language])
}

// Extract translation logic into a custom hook
const usePageTranslations = (details: PagesTemplateProps["details"]) => {
  const { i18n } = useTranslation()
  const getLocaleString = useLocaleString()

  return useMemo(() => {
    const titleTranslations = getTranslation({
      translationList: details?.translations,
      attribute_key: ["title"],
      locale: i18n.language,
    })

    const contentTranslations = getTranslation({
      translationList: details?.translations,
      attribute_key: ["content"],
      locale: i18n.language,
    })

    // Fallback to direct field values if translations are not available
    const pageTitle = titleTranslations?.[0] || getLocaleString(details.title)
    const pageContent =
      contentTranslations?.[0] || getLocaleString(details.content)

    return {
      title: pageTitle,
      content: pageContent,
    }
  }, [details, i18n.language, getLocaleString])
}

export default function PagesTemplate({ details }: PagesTemplateProps) {
  const { title, content } = usePageTranslations(details)

  return (
    <div className="flex w-full flex-col">
      {/* Page Banner */}
      {(details.image || details.image_cover) && (
        <div className="relative h-32 w-full overflow-hidden md:h-80 2xl:h-[520px]">
          <Image
            src={details.image_cover || details.image || ""}
            alt={title || "Page Banner"}
            fill
            className="object-cover"
            sizes="100vw"
            priority
          />
        </div>
      )}

      {/* Page Content */}
      <div className="container mx-auto my-8 flex w-full max-w-screen-lg flex-col space-y-2 sm:my-12 sm:space-y-10">
        {title && (
          <Heading
            level="h2"
            className="mb-6 text-center text-3xl font-bold text-primary-main"
          >
            {title}
          </Heading>
        )}
        <LexicalContent content={content} />
      </div>
    </div>
  )
}
