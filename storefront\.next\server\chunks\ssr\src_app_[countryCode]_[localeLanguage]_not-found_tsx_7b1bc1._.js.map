{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/app/%5BcountryCode%5D/%5BlocaleLanguage%5D/not-found.tsx"], "sourcesContent": ["import NotFoundLayout from \"components/not-found-layout\"\r\nimport { Metadata } from \"next\"\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"404\",\r\n  description: \"Something went wrong\",\r\n}\r\n\r\nexport default function NotFound() {\r\n  return (\r\n    <div className=\"fixed inset-0 flex flex-col items-center justify-center gap-4 bg-[#eef1cf]\">\r\n      <NotFoundLayout />\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,4IAAA,CAAA,UAAc;;;;;;;;;;AAGrB"}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}