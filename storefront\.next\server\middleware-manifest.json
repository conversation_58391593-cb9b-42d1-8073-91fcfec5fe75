{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_4380d7._.js", "server/edge/chunks/[root of the server]__a96925._.js", "server/edge/chunks/edge-wrapper_882b0c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(|\\\\.json|\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|assets|_next\\/static|favicon.ico|_next\\/image|images|robots.txt|public|static).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|assets|_next/static|favicon.ico|_next/image|images|robots.txt|public|static).*)"}], "wasm": [], "assets": [{"name": "server/edge/chunks/_4380d7._.js.map", "filePath": "server/edge/chunks/_4380d7._.js.map"}, {"name": "server/edge/chunks/_4380d7._.js", "filePath": "server/edge/chunks/_4380d7._.js"}, {"name": "server/edge/chunks/[root of the server]__a96925._.js.map", "filePath": "server/edge/chunks/[root of the server]__a96925._.js.map"}, {"name": "server/edge/chunks/[root of the server]__a96925._.js", "filePath": "server/edge/chunks/[root of the server]__a96925._.js"}, {"name": "server/edge/chunks/edge-wrapper_882b0c.js.map", "filePath": "server/edge/chunks/edge-wrapper_882b0c.js.map"}, {"name": "server/edge/chunks/edge-wrapper_882b0c.js", "filePath": "server/edge/chunks/edge-wrapper_882b0c.js"}], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "P05OuEt5furfyEF66ygpaKH/w9vOxc9kXCOUgMXeQGw=", "__NEXT_PREVIEW_MODE_ID": "5d8300b76f4aeb61f592609a58621669", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e38f17d897441510db715a05ef8787ff26879b1825bd020c436f8814c8b9bb97", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2425a216c1384d6c3f3244a08277420e08c621b41e986db0b7845c56db9a314b"}}}, "sortedMiddleware": ["/"], "functions": {}}