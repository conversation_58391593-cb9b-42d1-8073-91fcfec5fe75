{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_4380d7._.js", "server/edge/chunks/[root of the server]__a96925._.js", "server/edge/chunks/edge-wrapper_882b0c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(|\\\\.json|\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|assets|_next\\/static|favicon.ico|_next\\/image|images|robots.txt|public|static).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|assets|_next/static|favicon.ico|_next/image|images|robots.txt|public|static).*)"}], "wasm": [], "assets": [{"name": "server/edge/chunks/_4380d7._.js.map", "filePath": "server/edge/chunks/_4380d7._.js.map"}, {"name": "server/edge/chunks/_4380d7._.js", "filePath": "server/edge/chunks/_4380d7._.js"}, {"name": "server/edge/chunks/[root of the server]__a96925._.js.map", "filePath": "server/edge/chunks/[root of the server]__a96925._.js.map"}, {"name": "server/edge/chunks/[root of the server]__a96925._.js", "filePath": "server/edge/chunks/[root of the server]__a96925._.js"}, {"name": "server/edge/chunks/edge-wrapper_882b0c.js.map", "filePath": "server/edge/chunks/edge-wrapper_882b0c.js.map"}, {"name": "server/edge/chunks/edge-wrapper_882b0c.js", "filePath": "server/edge/chunks/edge-wrapper_882b0c.js"}], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "P05OuEt5furfyEF66ygpaKH/w9vOxc9kXCOUgMXeQGw=", "__NEXT_PREVIEW_MODE_ID": "0e0078123e7693c5dd311ee81b491f7d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "87b27c6da769778c57cca2936f6941b6247731772f876b0231e003e9815a27c8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e94f46987a9f64a9ad113274d61e6393cac67d0d3674874c048128c5eedb5f36"}}}, "sortedMiddleware": ["/"], "functions": {}}