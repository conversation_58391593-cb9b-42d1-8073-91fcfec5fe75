import "yet-another-react-lightbox/plugins/thumbnails.css"
import "yet-another-react-lightbox/styles.css"
import "../styles/globals.css"

import "swiper/css"
import "swiper/css/navigation"
import "swiper/css/pagination"
import "swiper/css/scrollbar"
import "swiper/swiper-bundle.css"

import { getBaseURL } from "@lib/util/env"
import FloatingSupport from "components/ui/floating-support"
import ScrollToTopButton from "components/ui/scroll-top"
import { Metadata } from "next"

export const metadata: Metadata = {
  title: "eFruit - Fresh Fruits & Premium Quality",
  description:
    "Fresh fruits and premium quality products delivered to your door. Experience the best selection of seasonal fruits with guaranteed freshness.",
  icons: [],
  metadataBase: new URL(getBaseURL()),
}

type Props = {
  children: React.ReactNode
}
export default async function RootLayout(props: Props) {
  return (
    <html data-mode="light" className="antialiased">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Mulish:ital,wght@0,200..1000;1,200..1000&display=swap"
          rel="stylesheet"
        />
      </head>
      <body>
        <main className="relative">{props.children}</main>
        <ScrollToTopButton />
        <FloatingSupport />
        {/* <FollowCursor /> */}
      </body>
    </html>
  )
}
