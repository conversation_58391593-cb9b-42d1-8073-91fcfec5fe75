{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind'\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR'\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: any\ndeclare const __next_app_load_chunk__: any\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base'\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AAoCA,4DAA4D;AAJ5D,EAAC;IAFCO,SAASC;AAVX,SAASJ,WAAWC,WAAW,QAAQ,0BAAyB;AAOhE,iCAAiC;AA1BjC,SAASL,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;AACpI,SAASC,SAAS,QAAQ,0BAAyB;AAYnD,UAAU;AAEV,eAAe;IAsBbW,YAAY;QAGVI,UAAU;;;;;;;;;;;;;;QACV,2CAA2C,sBAAA;QAC3CC,EAAAA,UAAY;QACZC,KAAAA;IAAAA;IAAU;gBACVC,IAAAA;YAAAA,KAAU,EAAE;YAAA;oBACd,QAAA;oBAAA;oBAAA;4BACAC,QAAAA;4BAAAA,CAAU;4BAAA;wCACRC,IAAAA;oCAAAA,OAAYnB;oCAAAA;4CACd,QAAA;4CAAA;4CAAA,CACF;4CAAE", "ignoreList": [0]}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}