{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/lib/util/translation.ts"], "sourcesContent": ["import { LANGUAGES_KEY_LIST } from \"i18n/settings\"\r\nimport { T_Translation } from \"types/translation\"\r\n\r\nexport const getTranslation = ({\r\n  translationList,\r\n  attribute_key,\r\n  locale = LANGUAGES_KEY_LIST.EN,\r\n}: {\r\n  translationList?: T_Translation[]\r\n  attribute_key: T_Translation[\"attribute_key\"][]\r\n  locale?: string\r\n}) => {\r\n  if (!translationList || translationList.length === 0 || !attribute_key) return null\r\n  const filterLocale = Array.isArray(translationList) ? translationList.filter((item) => {\r\n    if (locale === \"vn\" || locale === \"vi\") {\r\n      return item.language_code == \"vn\";\r\n    }\r\n    return locale.includes(item.language_code);\r\n  }) : [];\r\n\r\n  const translateValueList = attribute_key.map((itemAttributeKey) => {\r\n    const findTranslate = filterLocale?.find(\r\n      (item) => item.attribute_key === itemAttributeKey\r\n    )\r\n    return findTranslate?.attribute_value\r\n  })\r\n\r\n  return translateValueList\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAGO,MAAM,iBAAiB,CAAC,EAC7B,eAAe,EACf,aAAa,EACb,SAAS,uHAAA,CAAA,qBAAkB,CAAC,EAAE,EAK/B;IACC,IAAI,CAAC,mBAAmB,gBAAgB,MAAM,KAAK,KAAK,CAAC,eAAe,OAAO;IAC/E,MAAM,eAAe,MAAM,OAAO,CAAC,mBAAmB,gBAAgB,MAAM,CAAC,CAAC;QAC5E,IAAI,WAAW,QAAQ,WAAW,MAAM;YACtC,OAAO,KAAK,aAAa,IAAI;QAC/B;QACA,OAAO,OAAO,QAAQ,CAAC,KAAK,aAAa;IAC3C,KAAK,EAAE;IAEP,MAAM,qBAAqB,cAAc,GAAG,CAAC,CAAC;QAC5C,MAAM,gBAAgB,cAAc,KAClC,CAAC,OAAS,KAAK,aAAa,KAAK;QAEnC,OAAO,eAAe;IACxB;IAEA,OAAO;AACT"}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/lexical-content/index.tsx"], "sourcesContent": ["import SafeHTML from \"components/ui/safe-html\"\r\nimport \"./styles.css\"\r\nexport const LexicalContent = ({ content }: { content: string }) => {\r\n  // lexical-wrapper is required by lexical\r\n  return (\r\n    <div id=\"lexical-wrapper\">\r\n      <SafeHTML id=\"lexical-content\" html={content || \"\"} />\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;;AAEO,MAAM,iBAAiB,CAAC,EAAE,OAAO,EAAuB;IAC7D,yCAAyC;IACzC,qBACE,8OAAC;QAAI,IAAG;kBACN,cAAA,8OAAC,wIAAA,CAAA,UAAQ;YAAC,IAAG;YAAkB,MAAM,WAAW;;;;;;;;;;;AAGtD"}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/pages/templates/pages-template.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport Image from \"next/image\"\r\nimport { useMemo } from \"react\"\r\nimport { useTranslation } from \"react-i18next\"\r\n\r\nimport { getTranslation } from \"@lib/util/translation\"\r\nimport { Heading } from \"@medusajs/ui\"\r\nimport { LexicalContent } from \"components/lexical-content\"\r\nimport { T_Page } from \"types/pages\"\r\n\r\ntype LocalizedContent = Partial<Record<\"en\" | \"vi\" | string, string>>\r\n\r\ninterface PagesTemplateProps {\r\n  details: T_Page & {\r\n    content?: string | LocalizedContent\r\n    title?: string | LocalizedContent\r\n  }\r\n}\r\n\r\n// Extract locale resolution logic into a reusable hook\r\nconst useLocaleString = () => {\r\n  const { i18n } = useTranslation()\r\n\r\n  return useMemo(() => {\r\n    return (field: string | LocalizedContent | undefined): string => {\r\n      if (!field) return \"\"\r\n\r\n      if (typeof field === \"object\" && field !== null) {\r\n        return field[i18n.language] || field.vi || field.en || \"\"\r\n      }\r\n\r\n      if (typeof field === \"string\") {\r\n        try {\r\n          const parsed = JSON.parse(field)\r\n          if (typeof parsed === \"object\" && parsed !== null) {\r\n            return parsed[i18n.language] || parsed.vi || parsed.en || \"\"\r\n          }\r\n          return field\r\n        } catch {\r\n          return field\r\n        }\r\n      }\r\n\r\n      return \"\"\r\n    }\r\n  }, [i18n.language])\r\n}\r\n\r\n// Extract translation logic into a custom hook\r\nconst usePageTranslations = (details: PagesTemplateProps[\"details\"]) => {\r\n  const { i18n } = useTranslation()\r\n  const getLocaleString = useLocaleString()\r\n\r\n  return useMemo(() => {\r\n    const titleTranslations = getTranslation({\r\n      translationList: details?.translations,\r\n      attribute_key: [\"title\"],\r\n      locale: i18n.language,\r\n    })\r\n\r\n    const contentTranslations = getTranslation({\r\n      translationList: details?.translations,\r\n      attribute_key: [\"content\"],\r\n      locale: i18n.language,\r\n    })\r\n\r\n    // Fallback to direct field values if translations are not available\r\n    const pageTitle = titleTranslations?.[0] || getLocaleString(details.title)\r\n    const pageContent =\r\n      contentTranslations?.[0] || getLocaleString(details.content)\r\n\r\n    return {\r\n      title: pageTitle,\r\n      content: pageContent,\r\n    }\r\n  }, [details, i18n.language, getLocaleString])\r\n}\r\n\r\nexport default function PagesTemplate({ details }: PagesTemplateProps) {\r\n  const { title, content } = usePageTranslations(details)\r\n\r\n  return (\r\n    <div className=\"flex w-full flex-col\">\r\n      {/* Page Banner */}\r\n      {(details.image || details.image_cover) && (\r\n        <div className=\"relative h-32 w-full overflow-hidden md:h-80 2xl:h-[520px]\">\r\n          <Image\r\n            src={details.image_cover || details.image || \"\"}\r\n            alt={title || \"Page Banner\"}\r\n            fill\r\n            className=\"object-cover\"\r\n            sizes=\"100vw\"\r\n            priority\r\n          />\r\n        </div>\r\n      )}\r\n\r\n      {/* Page Content */}\r\n      <div className=\"container mx-auto my-8 flex w-full max-w-screen-lg flex-col space-y-2 sm:my-12 sm:space-y-10\">\r\n        {title && (\r\n          <Heading\r\n            level=\"h2\"\r\n            className=\"mb-6 text-center text-3xl font-bold text-primary-main\"\r\n          >\r\n            {title}\r\n          </Heading>\r\n        )}\r\n        <LexicalContent content={content} />\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAEA;AAJA;AAGA;AAPA;;;;;;;;AAoBA,uDAAuD;AACvD,MAAM,kBAAkB;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD;IAE9B,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACb,OAAO,CAAC;YACN,IAAI,CAAC,OAAO,OAAO;YAEnB,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM;gBAC/C,OAAO,KAAK,CAAC,KAAK,QAAQ,CAAC,IAAI,MAAM,EAAE,IAAI,MAAM,EAAE,IAAI;YACzD;YAEA,IAAI,OAAO,UAAU,UAAU;gBAC7B,IAAI;oBACF,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,IAAI,OAAO,WAAW,YAAY,WAAW,MAAM;wBACjD,OAAO,MAAM,CAAC,KAAK,QAAQ,CAAC,IAAI,OAAO,EAAE,IAAI,OAAO,EAAE,IAAI;oBAC5D;oBACA,OAAO;gBACT,EAAE,OAAM;oBACN,OAAO;gBACT;YACF;YAEA,OAAO;QACT;IACF,GAAG;QAAC,KAAK,QAAQ;KAAC;AACpB;AAEA,+CAA+C;AAC/C,MAAM,sBAAsB,CAAC;IAC3B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,kBAAkB;IAExB,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACb,MAAM,oBAAoB,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE;YACvC,iBAAiB,SAAS;YAC1B,eAAe;gBAAC;aAAQ;YACxB,QAAQ,KAAK,QAAQ;QACvB;QAEA,MAAM,sBAAsB,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE;YACzC,iBAAiB,SAAS;YAC1B,eAAe;gBAAC;aAAU;YAC1B,QAAQ,KAAK,QAAQ;QACvB;QAEA,oEAAoE;QACpE,MAAM,YAAY,mBAAmB,CAAC,EAAE,IAAI,gBAAgB,QAAQ,KAAK;QACzE,MAAM,cACJ,qBAAqB,CAAC,EAAE,IAAI,gBAAgB,QAAQ,OAAO;QAE7D,OAAO;YACL,OAAO;YACP,SAAS;QACX;IACF,GAAG;QAAC;QAAS,KAAK,QAAQ;QAAE;KAAgB;AAC9C;AAEe,SAAS,cAAc,EAAE,OAAO,EAAsB;IACnE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,oBAAoB;IAE/C,qBACE,8OAAC;QAAI,WAAU;;YAEZ,CAAC,QAAQ,KAAK,IAAI,QAAQ,WAAW,mBACpC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oBACJ,KAAK,QAAQ,WAAW,IAAI,QAAQ,KAAK,IAAI;oBAC7C,KAAK,SAAS;oBACd,IAAI;oBACJ,WAAU;oBACV,OAAM;oBACN,QAAQ;;;;;;;;;;;0BAMd,8OAAC;gBAAI,WAAU;;oBACZ,uBACC,8OAAC,mLAAA,CAAA,UAAO;wBACN,OAAM;wBACN,WAAU;kCAET;;;;;;kCAGL,8OAAC,iJAAA,CAAA,iBAAc;wBAAC,SAAS;;;;;;;;;;;;;;;;;;AAIjC"}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/Logo.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { ImageProps } from \"next/image\"\r\nimport { useEffect, useState } from \"react\"\r\n\r\nimport LocalizedClientLink from \"@modules/common/components/localized-client-link\"\r\nimport { PAGE_PATH } from \"utils/path\"\r\nimport { ResponsiveImage } from \"./custom-image\"\r\n\r\nconst logoBlack = \"/images/main_logo.png\"\r\n\r\ntype TPropsLogoStorefront = {\r\n  isLink?: boolean\r\n  src?: string\r\n  ratio?: string\r\n} & Omit<ImageProps, \"src\">\r\n\r\nconst LogoStorefront = ({\r\n  isLink = false,\r\n  src,\r\n  alt = \"Logo\",\r\n  // ratio = \"450/108\",\r\n  ...props\r\n}: TPropsLogoStorefront) => {\r\n  const [imageSrc, setImageSrc] = useState(src || logoBlack)\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setImageSrc(window.innerWidth < 768 ? logoBlack : src || logoBlack)\r\n    }\r\n\r\n    handleResize() // Set initial state\r\n    window.addEventListener(\"resize\", handleResize)\r\n\r\n    return () => {\r\n      window.removeEventListener(\"resize\", handleResize)\r\n    }\r\n  }, [src])\r\n\r\n  const renderImage = () =>\r\n    typeof src === \"string\" ? (\r\n      <div className=\"w-40\">\r\n        <ResponsiveImage src={imageSrc} alt={alt} {...props} />\r\n      </div>\r\n    ) : (\r\n      <div className=\"w-40\">\r\n        <ResponsiveImage src={logoBlack} alt={alt} {...props} />\r\n      </div>\r\n    )\r\n\r\n  return isLink ? (\r\n    <LocalizedClientLink href={PAGE_PATH.HOME}>\r\n      {renderImage()}\r\n    </LocalizedClientLink>\r\n  ) : (\r\n    <div>{renderImage()}</div>\r\n  )\r\n}\r\n\r\nexport default LogoStorefront\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AACA;AAAA;AAPA;;;;;;AASA,MAAM,YAAY;AAQlB,MAAM,iBAAiB,CAAC,EACtB,SAAS,KAAK,EACd,GAAG,EACH,MAAM,MAAM,EACZ,qBAAqB;AACrB,GAAG,OACkB;IACrB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;IAEhD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,UAAU,GAAG,MAAM,YAAY,OAAO;QAC3D;QAEA,eAAe,oBAAoB;;QACnC,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;QACvC;IACF,GAAG;QAAC;KAAI;IAER,MAAM,cAAc,IAClB,OAAO,QAAQ,yBACb,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0MAAA,CAAA,kBAAe;gBAAC,KAAK;gBAAU,KAAK;gBAAM,GAAG,KAAK;;;;;;;;;;iCAGrD,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0MAAA,CAAA,kBAAe;gBAAC,KAAK;gBAAW,KAAK;gBAAM,GAAG,KAAK;;;;;;;;;;;IAI1D,OAAO,uBACL,8OAAC,+KAAA,CAAA,UAAmB;QAAC,MAAM,oHAAA,CAAA,YAAS,CAAC,IAAI;kBACtC;;;;;6BAGH,8OAAC;kBAAK;;;;;;AAEV;uCAEe"}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "file": "heading.js", "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/%40medusajs/ui/src/components/heading/heading.tsx"], "sourcesContent": ["import { cva, type VariantProps } from \"cva\"\nimport * as React from \"react\"\n\nimport { clx } from \"@/utils/clx\"\n\nconst headingVariants = cva({\n  base: \"font-sans font-medium\",\n  variants: {\n    level: {\n      h1: \"h1-core\",\n      h2: \"h2-core\",\n      h3: \"h3-core\",\n    },\n  },\n  defaultVariants: {\n    level: \"h1\",\n  },\n})\n\ninterface HeadingProps extends VariantProps<typeof headingVariants>,\n  React.HTMLAttributes<HTMLHeadingElement> {}\n\n/**\n * This component is based on the heading element (`h1`, `h2`, etc...) depeneding on the specified level\n * and supports all of its props\n */\nconst Heading = ({ \n  /**\n   * The heading level which specifies which heading element is used.\n   */\n  level = \"h1\", \n  className, \n  ...props\n}: HeadingProps) => {\n  const Component = level || \"h1\"\n\n  return (\n    <Component\n      className={clx(headingVariants({ level }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Heading, headingVariants }\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,GAAG,EAAqB,MAAM,KAAK,CAAA;AAC5C,OAAO,KAAK,KAAK,MAAM,OAAO,CAAA;AAE9B,OAAO,EAAE,GAAG,EAAE,MAAM,aAAa,CAAA;;;;AAEjC,MAAM,eAAe,6IAAG,MAAA,AAAG,EAAC;IAC1B,IAAI,EAAE,uBAAuB;IAC7B,QAAQ,EAAE;QACR,KAAK,EAAE;YACL,EAAE,EAAE,SAAS;YACb,EAAE,EAAE,SAAS;YACb,EAAE,EAAE,SAAS;SACd;KACF;IACD,eAAe,EAAE;QACf,KAAK,EAAE,IAAI;KACZ;CACF,CAAC,CAAA;AAKF;;;GAGG,CACH,MAAM,OAAO,GAAG,CAAC,EACf;;GAEG,CACH,KAAK,GAAG,IAAI,EACZ,SAAS,EACT,GAAG,KAAK,EACK,EAAE,EAAE;IACjB,MAAM,SAAS,GAAG,KAAK,IAAI,IAAI,CAAA;IAE/B,OAAO,AACL,sMAAA,aAAA,CAAC,SAAS,EAAA;QACR,SAAS,sKAAE,MAAA,AAAG,EAAC,eAAe,CAAC;YAAE,KAAK;QAAA,CAAE,CAAC,EAAE,SAAS,CAAC;QAAA,GACjD,KAAK;IAAA,EACT,CACH,CAAA;AACH,CAAC,CAAA", "ignoreList": [0]}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/client/set-attributes-from-props.ts"], "sourcesContent": ["const DOMAttributeNames: Record<string, string> = {\n  acceptCharset: 'accept-charset',\n  className: 'class',\n  htmlFor: 'for',\n  httpEquiv: 'http-equiv',\n  noModule: 'noModule',\n}\n\nconst ignoreProps = [\n  'onLoad',\n  'onReady',\n  'dangerouslySetInnerHTML',\n  'children',\n  'onError',\n  'strategy',\n  'stylesheets',\n]\n\nfunction isBooleanScriptAttribute(\n  attr: string\n): attr is 'async' | 'defer' | 'noModule' {\n  return ['async', 'defer', 'noModule'].includes(attr)\n}\n\nexport function setAttributesFromProps(el: HTMLElement, props: object) {\n  for (const [p, value] of Object.entries(props)) {\n    if (!props.hasOwnProperty(p)) continue\n    if (ignoreProps.includes(p)) continue\n\n    // we don't render undefined props to the DOM\n    if (value === undefined) {\n      continue\n    }\n\n    const attr = DOMAttributeNames[p] || p.toLowerCase()\n\n    if (el.tagName === 'SCRIPT' && isBooleanScriptAttribute(attr)) {\n      // Correctly assign boolean script attributes\n      // https://github.com/vercel/next.js/pull/20748\n      ;(el as HTMLScriptElement)[attr] = !!value\n    } else {\n      el.setAttribute(attr, String(value))\n    }\n\n    // Remove falsy non-zero boolean attributes so they are correctly interpreted\n    // (e.g. if we set them to false, this coerces to the string \"false\", which the browser interprets as true)\n    if (\n      value === false ||\n      (el.tagName === 'SCRIPT' &&\n        isBooleanScriptAttribute(attr) &&\n        (!value || value === 'false'))\n    ) {\n      // Call setAttribute before, as we need to set and unset the attribute to override force async:\n      // https://html.spec.whatwg.org/multipage/scripting.html#script-force-async\n      el.setAttribute(attr, '')\n      el.removeAttribute(attr)\n    }\n  }\n}\n"], "names": ["setAttributesFromProps", "DOMAttributeNames", "acceptCharset", "className", "htmlFor", "httpEquiv", "noModule", "ignoreProps", "isBooleanScriptAttribute", "attr", "includes", "el", "props", "p", "value", "Object", "entries", "hasOwnProperty", "undefined", "toLowerCase", "tagName", "setAttribute", "String", "removeAttribute"], "mappings": ";;;;+BAwBgBA,0BAAAA;;;eAAAA;;;AAxBhB,MAAMC,oBAA4C;IAChDC,eAAe;IACfC,WAAW;IACXC,SAAS;IACTC,WAAW;IACXC,UAAU;AACZ;AAEA,MAAMC,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAASC,yBACPC,IAAY;IAEZ,OAAO;QAAC;QAAS;QAAS;KAAW,CAACC,QAAQ,CAACD;AACjD;AAEO,SAAST,uBAAuBW,EAAe,EAAEC,KAAa;IACnE,KAAK,MAAM,CAACC,GAAGC,MAAM,IAAIC,OAAOC,OAAO,CAACJ,OAAQ;QAC9C,IAAI,CAACA,MAAMK,cAAc,CAACJ,IAAI;QAC9B,IAAIN,YAAYG,QAAQ,CAACG,IAAI;QAE7B,6CAA6C;QAC7C,IAAIC,UAAUI,WAAW;YACvB;QACF;QAEA,MAAMT,OAAOR,iBAAiB,CAACY,EAAE,IAAIA,EAAEM,WAAW;QAElD,IAAIR,GAAGS,OAAO,KAAK,YAAYZ,yBAAyBC,OAAO;YAG3DE,EAAwB,CAACF,KAAK,GAAG,CAAC,CAACK;QACvC,OAAO;YACLH,GAAGU,YAAY,CAACZ,MAAMa,OAAOR;QAC/B;QAEA,6EAA6E;QAC7E,2GAA2G;QAC3G,IACEA,UAAU,SACTH,GAAGS,OAAO,KAAK,YACdZ,yBAAyBC,SACxB,CAAA,CAACK,SAASA,UAAU,OAAM,GAC7B;YACA,+FAA+F;YAC/F,2EAA2E;YAC3EH,GAAGU,YAAY,CAACZ,MAAM;YACtBE,GAAGY,eAAe,CAACd;QACrB;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 406, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/client/script.tsx"], "sourcesContent": ["'use client'\n\nimport ReactDOM from 'react-dom'\nimport React, { useEffect, useContext, useRef, type JSX } from 'react'\nimport type { ScriptHTMLAttributes } from 'react'\nimport { HeadManagerContext } from '../shared/lib/head-manager-context.shared-runtime'\nimport { setAttributesFromProps } from './set-attributes-from-props'\nimport { requestIdleCallback } from './request-idle-callback'\n\nconst ScriptCache = new Map()\nconst LoadCache = new Set()\n\nexport interface ScriptProps extends ScriptHTMLAttributes<HTMLScriptElement> {\n  strategy?: 'afterInteractive' | 'lazyOnload' | 'beforeInteractive' | 'worker'\n  id?: string\n  onLoad?: (e: any) => void\n  onReady?: () => void | null\n  onError?: (e: any) => void\n  children?: React.ReactNode\n  stylesheets?: string[]\n}\n\n/**\n * @deprecated Use `ScriptProps` instead.\n */\nexport type Props = ScriptProps\n\nconst insertStylesheets = (stylesheets: string[]) => {\n  // Case 1: Styles for afterInteractive/lazyOnload with appDir injected via handleClientScriptLoad\n  //\n  // Using ReactDOM.preinit to feature detect appDir and inject styles\n  // Stylesheets might have already been loaded if initialized with Script component\n  // Re-inject styles here to handle scripts loaded via handleClientScriptLoad\n  // ReactDOM.preinit handles dedup and ensures the styles are loaded only once\n  if (ReactDOM.preinit) {\n    stylesheets.forEach((stylesheet: string) => {\n      ReactDOM.preinit(stylesheet, { as: 'style' })\n    })\n\n    return\n  }\n\n  // Case 2: Styles for afterInteractive/lazyOnload with pages injected via handleClientScriptLoad\n  //\n  // We use this function to load styles when appdir is not detected\n  // TODO: Use React float APIs to load styles once available for pages dir\n  if (typeof window !== 'undefined') {\n    let head = document.head\n    stylesheets.forEach((stylesheet: string) => {\n      let link = document.createElement('link')\n\n      link.type = 'text/css'\n      link.rel = 'stylesheet'\n      link.href = stylesheet\n\n      head.appendChild(link)\n    })\n  }\n}\n\nconst loadScript = (props: ScriptProps): void => {\n  const {\n    src,\n    id,\n    onLoad = () => {},\n    onReady = null,\n    dangerouslySetInnerHTML,\n    children = '',\n    strategy = 'afterInteractive',\n    onError,\n    stylesheets,\n  } = props\n\n  const cacheKey = id || src\n\n  // Script has already loaded\n  if (cacheKey && LoadCache.has(cacheKey)) {\n    return\n  }\n\n  // Contents of this script are already loading/loaded\n  if (ScriptCache.has(src)) {\n    LoadCache.add(cacheKey)\n    // It is possible that multiple `next/script` components all have same \"src\", but has different \"onLoad\"\n    // This is to make sure the same remote script will only load once, but \"onLoad\" are executed in order\n    ScriptCache.get(src).then(onLoad, onError)\n    return\n  }\n\n  /** Execute after the script first loaded */\n  const afterLoad = () => {\n    // Run onReady for the first time after load event\n    if (onReady) {\n      onReady()\n    }\n    // add cacheKey to LoadCache when load successfully\n    LoadCache.add(cacheKey)\n  }\n\n  const el = document.createElement('script')\n\n  const loadPromise = new Promise<void>((resolve, reject) => {\n    el.addEventListener('load', function (e) {\n      resolve()\n      if (onLoad) {\n        onLoad.call(this, e)\n      }\n      afterLoad()\n    })\n    el.addEventListener('error', function (e) {\n      reject(e)\n    })\n  }).catch(function (e) {\n    if (onError) {\n      onError(e)\n    }\n  })\n\n  if (dangerouslySetInnerHTML) {\n    // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n    el.innerHTML = (dangerouslySetInnerHTML.__html as string) || ''\n\n    afterLoad()\n  } else if (children) {\n    el.textContent =\n      typeof children === 'string'\n        ? children\n        : Array.isArray(children)\n          ? children.join('')\n          : ''\n\n    afterLoad()\n  } else if (src) {\n    el.src = src\n    // do not add cacheKey into LoadCache for remote script here\n    // cacheKey will be added to LoadCache when it is actually loaded (see loadPromise above)\n\n    ScriptCache.set(src, loadPromise)\n  }\n\n  setAttributesFromProps(el, props)\n\n  if (strategy === 'worker') {\n    el.setAttribute('type', 'text/partytown')\n  }\n\n  el.setAttribute('data-nscript', strategy)\n\n  // Load styles associated with this script\n  if (stylesheets) {\n    insertStylesheets(stylesheets)\n  }\n\n  document.body.appendChild(el)\n}\n\nexport function handleClientScriptLoad(props: ScriptProps) {\n  const { strategy = 'afterInteractive' } = props\n  if (strategy === 'lazyOnload') {\n    window.addEventListener('load', () => {\n      requestIdleCallback(() => loadScript(props))\n    })\n  } else {\n    loadScript(props)\n  }\n}\n\nfunction loadLazyScript(props: ScriptProps) {\n  if (document.readyState === 'complete') {\n    requestIdleCallback(() => loadScript(props))\n  } else {\n    window.addEventListener('load', () => {\n      requestIdleCallback(() => loadScript(props))\n    })\n  }\n}\n\nfunction addBeforeInteractiveToCache() {\n  const scripts = [\n    ...document.querySelectorAll('[data-nscript=\"beforeInteractive\"]'),\n    ...document.querySelectorAll('[data-nscript=\"beforePageRender\"]'),\n  ]\n  scripts.forEach((script) => {\n    const cacheKey = script.id || script.getAttribute('src')\n    LoadCache.add(cacheKey)\n  })\n}\n\nexport function initScriptLoader(scriptLoaderItems: ScriptProps[]) {\n  scriptLoaderItems.forEach(handleClientScriptLoad)\n  addBeforeInteractiveToCache()\n}\n\n/**\n * Load a third-party scripts in an optimized way.\n *\n * Read more: [Next.js Docs: `next/script`](https://nextjs.org/docs/app/api-reference/components/script)\n */\nfunction Script(props: ScriptProps): JSX.Element | null {\n  const {\n    id,\n    src = '',\n    onLoad = () => {},\n    onReady = null,\n    strategy = 'afterInteractive',\n    onError,\n    stylesheets,\n    ...restProps\n  } = props\n\n  // Context is available only during SSR\n  const { updateScripts, scripts, getIsSsr, appDir, nonce } =\n    useContext(HeadManagerContext)\n\n  /**\n   * - First mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script hasn't loaded yet (not in LoadCache)\n   *      onReady is skipped, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. hasLoadScriptEffectCalled.current is false, loadScript executes\n   *      Once the script is loaded, the onLoad and onReady will be called by then\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   *\n   * - Second mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script has already loaded (found in LoadCache)\n   *      onReady is called, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. The script is already loaded, loadScript bails out\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   */\n  const hasOnReadyEffectCalled = useRef(false)\n\n  useEffect(() => {\n    const cacheKey = id || src\n    if (!hasOnReadyEffectCalled.current) {\n      // Run onReady if script has loaded before but component is re-mounted\n      if (onReady && cacheKey && LoadCache.has(cacheKey)) {\n        onReady()\n      }\n\n      hasOnReadyEffectCalled.current = true\n    }\n  }, [onReady, id, src])\n\n  const hasLoadScriptEffectCalled = useRef(false)\n\n  useEffect(() => {\n    if (!hasLoadScriptEffectCalled.current) {\n      if (strategy === 'afterInteractive') {\n        loadScript(props)\n      } else if (strategy === 'lazyOnload') {\n        loadLazyScript(props)\n      }\n\n      hasLoadScriptEffectCalled.current = true\n    }\n  }, [props, strategy])\n\n  if (strategy === 'beforeInteractive' || strategy === 'worker') {\n    if (updateScripts) {\n      scripts[strategy] = (scripts[strategy] || []).concat([\n        {\n          id,\n          src,\n          onLoad,\n          onReady,\n          onError,\n          ...restProps,\n        },\n      ])\n      updateScripts(scripts)\n    } else if (getIsSsr && getIsSsr()) {\n      // Script has already loaded during SSR\n      LoadCache.add(id || src)\n    } else if (getIsSsr && !getIsSsr()) {\n      loadScript(props)\n    }\n  }\n\n  // For the app directory, we need React Float to preload these scripts.\n  if (appDir) {\n    // Injecting stylesheets here handles beforeInteractive and worker scripts correctly\n    // For other strategies injecting here ensures correct stylesheet order\n    // ReactDOM.preinit handles loading the styles in the correct order,\n    // also ensures the stylesheet is loaded only once and in a consistent manner\n    //\n    // Case 1: Styles for beforeInteractive/worker with appDir - handled here\n    // Case 2: Styles for beforeInteractive/worker with pages dir - Not handled yet\n    // Case 3: Styles for afterInteractive/lazyOnload with appDir - handled here\n    // Case 4: Styles for afterInteractive/lazyOnload with pages dir - handled in insertStylesheets function\n    if (stylesheets) {\n      stylesheets.forEach((styleSrc) => {\n        ReactDOM.preinit(styleSrc, { as: 'style' })\n      })\n    }\n\n    // Before interactive scripts need to be loaded by Next.js' runtime instead\n    // of native <script> tags, because they no longer have `defer`.\n    if (strategy === 'beforeInteractive') {\n      if (!src) {\n        // For inlined scripts, we put the content in `children`.\n        if (restProps.dangerouslySetInnerHTML) {\n          // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n          restProps.children = restProps.dangerouslySetInnerHTML\n            .__html as string\n          delete restProps.dangerouslySetInnerHTML\n        }\n\n        return (\n          <script\n            nonce={nonce}\n            dangerouslySetInnerHTML={{\n              __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                0,\n                { ...restProps, id },\n              ])})`,\n            }}\n          />\n        )\n      } else {\n        // @ts-ignore\n        ReactDOM.preload(\n          src,\n          restProps.integrity\n            ? {\n                as: 'script',\n                integrity: restProps.integrity,\n                nonce,\n                crossOrigin: restProps.crossOrigin,\n              }\n            : { as: 'script', nonce, crossOrigin: restProps.crossOrigin }\n        )\n        return (\n          <script\n            nonce={nonce}\n            dangerouslySetInnerHTML={{\n              __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                src,\n                { ...restProps, id },\n              ])})`,\n            }}\n          />\n        )\n      }\n    } else if (strategy === 'afterInteractive') {\n      if (src) {\n        // @ts-ignore\n        ReactDOM.preload(\n          src,\n          restProps.integrity\n            ? {\n                as: 'script',\n                integrity: restProps.integrity,\n                nonce,\n                crossOrigin: restProps.crossOrigin,\n              }\n            : { as: 'script', nonce, crossOrigin: restProps.crossOrigin }\n        )\n      }\n    }\n  }\n\n  return null\n}\n\nObject.defineProperty(Script, '__nextScript', { value: true })\n\nexport default Script\n"], "names": ["handleClientScriptLoad", "initScriptLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Map", "Load<PERSON>ache", "Set", "insertStylesheets", "stylesheets", "ReactDOM", "preinit", "for<PERSON>ach", "stylesheet", "as", "window", "head", "document", "link", "createElement", "type", "rel", "href", "append<PERSON><PERSON><PERSON>", "loadScript", "props", "src", "id", "onLoad", "onReady", "dangerouslySetInnerHTML", "children", "strategy", "onError", "cache<PERSON>ey", "has", "add", "get", "then", "afterLoad", "el", "loadPromise", "Promise", "resolve", "reject", "addEventListener", "e", "call", "catch", "innerHTML", "__html", "textContent", "Array", "isArray", "join", "set", "setAttributesFromProps", "setAttribute", "body", "requestIdleCallback", "loadLazyScript", "readyState", "addBeforeInteractiveToCache", "scripts", "querySelectorAll", "script", "getAttribute", "scriptLoaderItems", "<PERSON><PERSON><PERSON>", "restProps", "updateScripts", "getIsSsr", "appDir", "nonce", "useContext", "HeadManagerContext", "hasOnReadyEffectCalled", "useRef", "useEffect", "current", "hasLoadScriptEffectCalled", "concat", "styleSrc", "JSON", "stringify", "preload", "integrity", "crossOrigin", "Object", "defineProperty", "value"], "mappings": "AAAA;;;;;;;;;;;;;;;;IAyXA,IAAA,GAAqB,MAAA;eAArB;;IA7NgBA,sBAAsB;eAAtBA,aAAAA;;IAgCAC,gBAAgB;eAAhBA,OAAAA;;;;;;mEA1LK;iEAC0C;iDAE5B,MAAA,CAAA;wCACI;qCACH;AAEpC,MAAMC,cAAc,IAAIC;AACxB,MAAMC,YAAY,EAAA,EAAIC,EAAAA;AAiBtB,MAAMC,YAAAA,IAAAA,IAAoB,CAACC;IACzB,EAAA,oBAAA,CAAA,0EAAiG;IACjG,EAAE,+FAAA;IACF,EAAA,kEAAoE;IACpE,oEAAA,cAAkF;IAClF,4EAA4E,MAAA;IAC5E,4EAAA,CAA6E;IAC7E,IAAIC,iBAAQ,CAACC,OAAO,EAAE,8CAAA;QACpBF,UAAAA,EAAYG,KAAAA,CAAAA,CAAO,CAAC,CAACC,IAAAA,EAAAA;YACnBH,QAAAA,OAAAA,CAAAA,CAAQ,CAACC,OAAO,CAACE,YAAY;gBAAEC,IAAI,EAAA,OAAA,CAAA,OAAA,CAAA,YAAA;gBAAQ,IAAA;YAC7C;QAEA;QACF;IAEA,gGAAgG;IAChG,EAAE,8FAAA;IACF,EAAA,gEAAkE;IAClE,kEAAA,OAAyE;IACzE,IAAI,OAAOC,WAAW,aAAa,sCAAA;QACjC,IAAIC,GAAAA,IAAOC,OAAAA,EAASD,IAAI,OAAA;QACxBP,IAAAA,OAAAA,CAAYG,OAAO,CAAC,CAACC,GAAAA;YACnB,IAAIK,IAAAA,GAAOD,IAAAA,CAAAA,CAAAA,GAASE,aAAa,CAAC;YAElCD,IAAAA,CAAKE,IAAI,EAAA,CAAG,QAAA,aAAA,CAAA;YACZF,KAAKG,GAAG,CAAA,EAAG,CAAA;YACXH,KAAKI,GAAAA,CAAI,EAAA,CAAGT;YAEZG,KAAKO,IAAAA,GAAAA,IAAW,CAACL;YACnB,KAAA,WAAA,CAAA;QACF;IACF;AAEA,MAAMM,aAAa,CAACC;IAClB,EAAA,IAAM,EACJC,GAAG,EACHC,EAAE,CAAA,CACFC,SAAS,KAAO,CAAC,EACjBC,UAAU,IAAI,EACdC,uBAAuB,EACvBC,WAAW,EAAE,EACbC,WAAW,kBAAkB,EAC7BC,OAAO,EACPxB,WAAW,EACZ,GAAGgB;IAEJ,MAAMS,EAAAA,GAAAA,EAAAA,EAAAA,EAAWP,MAAMD,GAAAA,KAAAA,CAAAA,EAAAA,UAAAA,IAAAA,EAAAA,uBAAAA,EAAAA,WAAAA,EAAAA,EAAAA,WAAAA,kBAAAA,EAAAA,OAAAA,EAAAA,WAAAA,EAAAA,GAAAA;IAEvB,MAAA,WAAA,MAAA,KAA4B;IAC5B,IAAIQ,YAAY5B,UAAU6B,EAAAA,CAAG,CAACD,WAAW;QACvC,YAAA,UAAA,GAAA,CAAA,WAAA;QACF;IAEA,qDAAqD;IACrD,IAAI9B,YAAY+B,GAAG,CAACT,MAAM,2BAAA;QACxBpB,UAAU8B,EAAAA,CAAG,CAACF,CAAAA,CAAAA,MAAAA;QACd,UAAA,GAAA,CAAA,0FAAwG;QACxG,sGAAsG,EAAA;QACtG9B,YAAYiC,GAAG,CAACX,KAAKY,IAAI,CAACV,QAAQK,oEAAAA;QAClC,YAAA,GAAA,CAAA,KAAA,IAAA,CAAA,QAAA;QACF;IAEA,0CAA0C,GAC1C,MAAMM,YAAY;QAChB,sCAAA,GAAA,MAAA,GAAkD,SAAA;QAClD,IAAIV,SAAS,qCAAA;YACXA,SAAAA;YACF;QACA,mDAAmD;QACnDvB,UAAU8B,GAAG,CAACF,qCAAAA;QAChB,UAAA,GAAA,CAAA;IAEA,MAAMM,KAAKvB,SAASE,aAAa,CAAC;IAElC,MAAMsB,KAAAA,SAAc,IAAIC,QAAc,CAACC,CAAAA,QAASC;QAC9CJ,EAAAA,CAAGK,aAAAA,GAAgB,CAAC,QAAQ,CAAA,QAAUC,CAAC;YACrCH,eAAAA,CAAAA,QAAAA,SAAAA,CAAAA;YACA,IAAIf,QAAQ;gBACVA,OAAOmB,CAAAA,GAAI,CAAC,IAAI,EAAED;gBACpB,OAAA,IAAA,CAAA,IAAA,EAAA;YACAP;YACF;QACAC,GAAGK,gBAAgB,CAAC,SAAS,SAAUC,CAAC;YACtCF,OAAOE,QAAAA,CAAAA,SAAAA,SAAAA,CAAAA;YACT,OAAA;QACCE,IAAK,CAAC,SAAUF,CAAC;QAClB,IAAIb,CAAAA,QAAS,CAAA,CAAA;YACXA,QAAQa,CAAAA;YACV,QAAA;QACF;IAEA,IAAIhB,yBAAyB;QAC3B,yBAAA,kCAA2D;QAC3DU,GAAGS,SAAS,GAAG,AAACnB,wBAAwBoB,MAAM,IAAe,UAAA;QAE7DX,GAAAA,SAAAA,GAAAA,wBAAAA,MAAAA,IAAAA;QACF,GAAO,IAAIR,UAAU;QACnBS,GAAGW,IAAAA,OAAW,GACZ,OAAOpB,aAAa,WAChBA,WACAqB,MAAMC,OAAO,CAACtB,YACZA,SAASuB,IAAI,CAAC,MACd;QAERf,GAAAA,WAAAA,GAAAA,OAAAA,aAAAA,WAAAA,WAAAA,MAAAA,OAAAA,CAAAA,YAAAA,SAAAA,IAAAA,CAAAA,MAAAA;QACF,GAAO,IAAIb,KAAK;QACdc,GAAGd,GAAG,CAAA,EAAGA,GAAAA;QACT,GAAA,GAAA,GAAA,mDAA4D;QAC5D,4DAAA,6BAAyF;QAEzFtB,YAAYmD,GAAG,CAAC7B,KAAKe,oEAAAA;QACvB,YAAA,GAAA,CAAA,KAAA;IAEAe,IAAAA,8CAAsB,EAAChB,IAAIf;IAE3B,CAAA,GAAIO,aAAa,UAAU,CAAA,sBAAA,EAAA,IAAA;QACzBQ,GAAGiB,UAAAA,EAAY,CAAC,OAAA,CAAQ;QAC1B,GAAA,YAAA,CAAA,QAAA;IAEAjB,GAAGiB,YAAY,CAAC,gBAAgBzB;IAEhC,GAAA,YAAA,CAAA,gBAAA,UAA0C;IAC1C,IAAIvB,aAAa,yBAAA;QACfD,aAAAA,KAAkBC;QACpB,kBAAA;IAEAQ,SAASyC,IAAI,CAACnC,WAAW,CAACiB;IAC5B,SAAA,IAAA,CAAA,WAAA,CAAA;AAEO,SAAStC,uBAAuBuB,KAAkB;IACvD,KAAA,CAAM,EAAEO,WAAW,SAAA,KAAA,IAAkB,EAAE,GAAGP;IAC1C,IAAIO,EAAAA,EAAAA,SAAa,EAAA,YAAc,MAAA,EAAA,GAAA;QAC7BjB,OAAO8B,MAAAA,UAAgB,CAAC,GAAA,KAAQ;YAC9Bc,GAAAA,CAAAA,eAAAA,CAAAA,QAAAA,gBAAmB,EAAC,IAAMnC,WAAWC;YACvC,CAAA,GAAA,qBAAA,mBAAA,EAAA,IAAA,WAAA;QACF,GAAO;QACLD,GAAAA,QAAWC;QACb,WAAA;IACF;AAEA,SAASmC,eAAenC,KAAkB;IACxC,IAAIR,CAAAA,QAAS4C,OAAAA,GAAU,EAAA,GAAK,YAAY;QACtCF,IAAAA,KAAAA,UAAAA,KAAAA,YAAAA,QAAmB,EAAC,IAAMnC,WAAWC;QACvC,CAAA,EAAO,CAAA,qBAAA,mBAAA,EAAA,IAAA,WAAA;QACLV,GAAAA,IAAO8B,gBAAgB,CAAC,QAAQ;YAC9Bc,GAAAA,CAAAA,eAAAA,CAAAA,QAAAA,gBAAmB,EAAC,IAAMnC,WAAWC;YACvC,CAAA,GAAA,qBAAA,mBAAA,EAAA,IAAA,WAAA;QACF;IACF;AAEA,SAASqC;IACP,KAAA,CAAMC,UAAU;WACX9C,SAAS+C,gBAAgB,CAAC;WAC1B/C,SAAS+C,gBAAgB,CAAC;WAC9B,SAAA,gBAAA,CAAA;KACDD,OAAQnD,OAAO,CAAC,CAACqD;QACf,IAAA,EAAM/B,KAAAA,CAAAA,CAAAA,IAAW+B,OAAOtC,EAAE,IAAIsC,OAAOC,YAAY,CAAC;QAClD5D,MAAAA,IAAU8B,GAAG,CAACF,GAAAA,OAAAA,EAAAA,IAAAA,OAAAA,YAAAA,CAAAA;QAChB,UAAA,GAAA,CAAA;IACF;AAEO,SAAS/B,iBAAiBgE,iBAAgC;IAC/DA,KAAAA,aAAkBvD,IAAAA,GAAO,CAACV,aAAAA;IAC1B4D,kBAAAA,OAAAA,CAAAA;IACF;AAEA;;;;;IAME,MAAM,EACJnC,CAAAA,CAAE,EACFD,IAAAA,EAAM,EAAE,CAAA,CACRE,SAAS,KAAO,CAAC,EACjBC,UAAU,IAAI,EACdG,WAAW,kBAAkB,EAC7BC,OAAO,EACPxB,WAAW,EACX,GAAG4D,WACJ,GAAG5C;IAEJ,MAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,SAAA,KAAA,CAAA,EAAuC,UAAA,IAAA,EAAA,WAAA,kBAAA,EAAA,OAAA,EAAA,WAAA,EAAA,GAAA,WAAA,GAAA;IACvC,MAAM,EAAE6C,aAAa,EAAEP,OAAO,EAAEQ,OAAAA,CAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAE,GACvDC,IAAAA,iBAAU,EAACC,mDAAkB;IAE/B,MAAA,EAAA,aAAA,EAAA,OAAA,EAAA,QAAA,EAAA,MAAA,EAAA,KAAA,EAAA,GAAA,CAAA,GAAA,OAAA,UAAA,EAAA,iCAAA,kBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;IA4BAG,EAAAA,EAAAA,IAAAA,YAAS,EAAC,WAAA,CAAA,GAAA,OAAA,MAAA,EAAA;QACR,MAAM5C,CAAAA,SAAAA,CAAWP,CAAAA,KAAMD;QACvB,IAAI,CAACkD,CAAAA,WAAAA,MAAAA,KAAuBG,OAAO,EAAE;YACnC,CAAA,uBAAA,OAAA,EAAA,qCAAsE;YACtE,IAAIlD,WAAWK,YAAY5B,UAAU6B,GAAG,CAACD,WAAW,kBAAA;gBAClDL,WAAAA,YAAAA,UAAAA,GAAAA,CAAAA,WAAAA;gBACF;YAEA+C,uBAAuBG,OAAO,GAAG;YACnC,uBAAA,OAAA,GAAA;QACC;QAAClD;QAASF;QAAID;QAAI;KAErB,KAAMsD,4BAA4BH,IAAAA,aAAM,EAAC;IAEzCC,IAAAA,EAAAA,cAAS,EAAC,YAAA,CAAA,GAAA,OAAA,MAAA,EAAA;QACR,IAAI,CAACE,EAAAA,SAAAA,EAAAA,aAA0BD,OAAO,EAAE;YACtC,CAAA,GAAI/C,aAAa,UAAA,OAAA,EAAA,CAAoB;gBACnCR,WAAWC,EAAAA,oBAAAA;gBACb,GAAO,IAAIO,IAAAA,SAAa,cAAc;gBACpC4B,GAAAA,IAAAA,QAAenC,KAAAA,cAAAA;gBACjB,eAAA;YAEAuD,0BAA0BD,OAAO,GAAG;YACtC,0BAAA,OAAA,GAAA;QACC;QAACtD;QAAOO;QAAS;KAEpB,GAAIA,aAAa,uBAAuBA,aAAa,UAAU;QAC7D,IAAIsC,SAAAA,MAAe,iBAAA,aAAA,UAAA;YACjBP,OAAO,CAAC/B,OAAAA,EAAS,GAAG,AAAC+B,CAAAA,OAAO,CAAC/B,SAAS,IAAI,EAAE,AAAD,EAAGiD,MAAM,CAAC;gBACnD,GAAA,CAAA,SAAA,GAAA,CAAA,OAAA,CAAA,SAAA,IAAA,EAAA,EAAA,MAAA,CAAA;oBACEtD;oBACAD;oBACAE;oBACAC;oBACAI;oBACA,GAAGoC,SAAS;oBACd,GAAA,SAAA;gBACD;aACDC,aAAcP;YAChB,GAAO,IAAIQ,OAAAA,KAAYA,YAAY;YACjC,GAAA,IAAA,YAAA,YAAA,QAAuC;YACvCjE,UAAU8B,GAAG,CAACT,MAAMD,mBAAAA;YACtB,GAAO,IAAI6C,GAAAA,GAAAA,CAAAA,KAAY,CAACA,YAAY;YAClC/C,GAAAA,IAAAA,IAAWC,QAAAA,CAAAA,YAAAA;YACb,WAAA;QACF;IAEA,uEAAuE;IACvE,IAAI+C,QAAQ,2DAAA;QACV,QAAA,4EAAoF;QACpF,uEAAuE,aAAA;QACvE,oEAAoE,GAAA;QACpE,oEAAA,SAA6E;QAC7E,EAAE,2EAAA;QACF,EAAA,uEAAyE;QACzE,yEAAA,MAA+E;QAC/E,4EAA4E,GAAA;QAC5E,4EAAA,4BAAwG;QACxG,IAAI/D,aAAa,uFAAA;YACfA,YAAYG,CAAAA,MAAO,CAAC,CAACsE;gBACnBxE,QAAAA,OAAAA,CAAAA,CAAQ,CAACC,OAAO,CAACuE,UAAU;oBAAEpE,IAAI,EAAA,OAAA,CAAA,OAAA,CAAA,UAAA;oBAAQ,IAAA;gBAC3C;YACF;QAEA,2EAA2E;QAC3E,gEAAgE,WAAA;QAChE,IAAIkB,aAAa,qBAAqB,0BAAA;YACpC,IAAI,CAACN,KAAK,GAAA,qBAAA;gBACR,CAAA,KAAA,mDAAyD;gBACzD,IAAI2C,UAAUvC,uBAAuB,EAAE,kBAAA;oBACrC,UAAA,uBAAA,EAAA,wBAA2D;oBAC3DuC,UAAUtC,QAAQ,GAAGsC,UAAUvC,uBAAuB,CACnDoB,IAAAA,EAAM;oBACT,OAAOmB,GAAAA,OAAUvC,CAAAA,GAAAA,UAAAA,SAAuB,cAAA,CAAA,MAAA;oBAC1C,OAAA,UAAA,uBAAA;gBAEA,qBACE,qBAACmC;oBACCQ,GAAAA,IAAOA,OAAAA,GAAAA,CAAAA,GAAAA,YAAAA,GAAAA,EAAAA,UAAAA;oBACP3C,OAAAA,kBAAyB;wBACvBoB,QAAQ,AAAC,aAAA,+BAAyCiC,KAAKC,SAAS,CAAC;4BAC/D,IAAA,4CAAA,KAAA,SAAA,CAAA;4BACA;gCAAE,GAAGf,SAAS;gCAAE1C,GAAAA,SAAAA;gCAAG;4BACpB,CAAE;yBACL,IAAA;;gBAGN,GAAO;gBACL,GAAA,UAAa;gBACbjB,aAAAA,IAAQ,CAAC2E,OAAO,CACd3D,KACA2C,UAAUiB,SAAS,GACf;oBACExE,IAAI,EAAA,OAAA,CAAA,OAAA,CAAA,KAAA,UAAA,SAAA,GAAA;oBACJwE,IAAAA,OAAWjB,UAAUiB,SAAS;oBAC9Bb,WAAAA,UAAAA,SAAAA;oBACAc,aAAalB,UAAUkB,WAAW;oBAEpC,aAAA,UAAA,WAAA;oBAAEzE,IAAI;oBAAU2D,IAAAA;oBAAOc,aAAalB,UAAUkB,WAAW;oBAAC,aAAA,UAAA,WAAA;gBAEhE,qBACE,qBAACtB;oBACCQ,GAAAA,IAAOA,OAAAA,GAAAA,CAAAA,GAAAA,YAAAA,GAAAA,EAAAA,UAAAA;oBACP3C,OAAAA,kBAAyB;wBACvBoB,QAAQ,AAAC,aAAA,+BAAyCiC,KAAKC,SAAS,CAAC;4BAC/D1D,IAAAA,4CAAAA,KAAAA,SAAAA,CAAAA;4BACA;gCAAE,GAAG2C,SAAS;gCAAE1C,GAAAA,SAAAA;gCAAG;4BACpB,CAAE;yBACL,IAAA;;gBAGN;YACF,GAAO,IAAIK,aAAa,oBAAoB;YAC1C,GAAA,CAAIN,GAAAA,EAAK,WAAA,oBAAA;gBACP,KAAA,QAAa;gBACbhB,aAAAA,IAAQ,CAAC2E,OAAO,CACd3D,KACA2C,UAAUiB,SAAS,GACf;oBACExE,IAAI,EAAA,OAAA,CAAA,OAAA,CAAA,KAAA,UAAA,SAAA,GAAA;oBACJwE,IAAAA,OAAWjB,UAAUiB,SAAS;oBAC9Bb,WAAAA,UAAAA,SAAAA;oBACAc,aAAalB,UAAUkB,WAAW;oBAEpC,aAAA,UAAA,WAAA;oBAAEzE,IAAI;oBAAU2D,IAAAA;oBAAOc,aAAalB,UAAUkB,WAAW;oBAAC,aAAA,UAAA,WAAA;gBAElE;YACF;QACF;IAEA,OAAO;IACT,OAAA;AAEAC,OAAOC,cAAc,CAACrB,QAAQ,gBAAgB;IAAEsB,GAAAA,IAAO,UAAA,CAAA,QAAA,gBAAA;IAAK,OAAA;MAE5D,WAAetB", "ignoreList": [0]}}, {"offset": {"line": 746, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}