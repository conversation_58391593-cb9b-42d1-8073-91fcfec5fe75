{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/src/components/lexical-content/styles.css"], "sourcesContent": ["@import url(\"https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Lora:ital,wght@0,400..700;1,400..700&family=Montserrat:ital,wght@0,100..900;1,100..900&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Playfair+Display:ital,wght@0,400..900;1,400..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Roboto+Mono:ital,wght@0,100..700;1,100..700&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap\");\r\n\r\n/** * Copyright (c) Meta Platforms, Inc. and affiliates. * * This source code is licensed under the MIT license found in the * LICENSE file in the root directory of this source tree. * */\r\n#lexical-wrapper .editor-shell {\r\n  margin: 20px auto;\r\n  border-radius: 8px;\r\n  max-width: 1100px;\r\n  color: #000;\r\n  position: relative;\r\n  line-height: 1.7;\r\n  font-weight: 400;\r\n  border: 1px solid #ddd;\r\n  padding: 8px;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .editor-container {\r\n  background: #fff;\r\n  position: relative;\r\n  display: block;\r\n  border-bottom-left-radius: 10px;\r\n  border-bottom-right-radius: 10px;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .editor-container.tree-view {\r\n  border-radius: 0;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .editor-container.plain-text {\r\n  border-top-left-radius: 10px;\r\n  border-top-right-radius: 10px;\r\n}\r\n\r\n#lexical-wrapper .editor-scroller {\r\n  min-height: 150px;\r\n  border: 0;\r\n  display: flex;\r\n  position: relative;\r\n  outline: 0;\r\n  z-index: 0;\r\n  overflow: auto;\r\n  resize: vertical;\r\n}\r\n\r\n#lexical-wrapper .editor {\r\n  flex: auto;\r\n  position: relative;\r\n  resize: vertical;\r\n  z-index: -1;\r\n}\r\n\r\n#lexical-wrapper .test-recorder-output {\r\n  margin: 20px auto 20px auto;\r\n  width: 100%;\r\n}\r\n\r\n#lexical-wrapper pre {\r\n  line-height: 1.1;\r\n  background: #222;\r\n  color: #fff;\r\n  margin: 0;\r\n  padding: 10px;\r\n  font-size: 12px;\r\n  overflow: auto;\r\n  max-height: 400px;\r\n}\r\n\r\n#lexical-wrapper .tree-view-output {\r\n  display: block;\r\n  background: #222;\r\n  color: #fff;\r\n  padding: 0;\r\n  font-size: 12px;\r\n  margin: 1px auto 10px auto;\r\n  position: relative;\r\n  overflow: hidden;\r\n  border-bottom-left-radius: 10px;\r\n  border-bottom-right-radius: 10px;\r\n}\r\n\r\n#lexical-wrapper pre::-webkit-scrollbar {\r\n  background: transparent;\r\n  width: 10px;\r\n}\r\n\r\n#lexical-wrapper pre::-webkit-scrollbar-thumb {\r\n  background: #999;\r\n}\r\n\r\n#lexical-wrapper .editor-dev-button {\r\n  position: relative;\r\n  display: block;\r\n  width: 40px;\r\n  height: 40px;\r\n  font-size: 12px;\r\n  border-radius: 20px;\r\n  border: none;\r\n  cursor: pointer;\r\n  outline: none;\r\n  box-shadow: 0px 1px 10px rgba(0, 0, 0, 0.3);\r\n  background-color: #444;\r\n}\r\n\r\n#lexical-wrapper .editor-dev-button::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 10px;\r\n  right: 10px;\r\n  bottom: 10px;\r\n  left: 10px;\r\n  display: block;\r\n  background-size: contain;\r\n  filter: invert(1);\r\n}\r\n\r\n#lexical-wrapper .editor-dev-button:hover {\r\n  background-color: #555;\r\n}\r\n\r\n#lexical-wrapper .editor-dev-button.active {\r\n  background-color: #e92323;\r\n}\r\n\r\n#lexical-wrapper .test-recorder-toolbar {\r\n  display: flex;\r\n}\r\n\r\n#lexical-wrapper .test-recorder-button {\r\n  position: relative;\r\n  display: block;\r\n  width: 32px;\r\n  height: 32px;\r\n  font-size: 10px;\r\n  padding: 6px 6px;\r\n  border-radius: 4px;\r\n  border: none;\r\n  cursor: pointer;\r\n  outline: none;\r\n  box-shadow: 1px 2px 2px rgba(0, 0, 0, 0.4);\r\n  background-color: #222;\r\n  transition: box-shadow 50ms ease-out;\r\n}\r\n\r\n#lexical-wrapper .test-recorder-button:active {\r\n  box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n#lexical-wrapper .test-recorder-button + .test-recorder-button {\r\n  margin-left: 4px;\r\n}\r\n\r\n#lexical-wrapper .test-recorder-button::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 8px;\r\n  right: 8px;\r\n  bottom: 8px;\r\n  left: 8px;\r\n  display: block;\r\n  background-size: contain;\r\n  filter: invert(1);\r\n}\r\n\r\n#lexical-wrapper #options-button {\r\n  position: fixed;\r\n  left: 20px;\r\n  bottom: 20px;\r\n}\r\n\r\n#lexical-wrapper #test-recorder-button {\r\n  position: fixed;\r\n  left: 70px;\r\n  bottom: 20px;\r\n}\r\n\r\n#lexical-wrapper #paste-log-button {\r\n  position: fixed;\r\n  left: 120px;\r\n  bottom: 20px;\r\n}\r\n\r\n#lexical-wrapper #docs-button {\r\n  position: fixed;\r\n  left: 170px;\r\n  bottom: 20px;\r\n}\r\n\r\n#lexical-wrapper #options-button::after {\r\n  background-image: url(/public/lexical/icons/gear.svg);\r\n}\r\n\r\n#lexical-wrapper #test-recorder-button::after {\r\n  background-image: url(/public/lexical/icons/journal-code.svg);\r\n}\r\n\r\n#lexical-wrapper #paste-log-button::after {\r\n  background-image: url(/public/lexical/icons/clipboard.svg);\r\n}\r\n\r\n#lexical-wrapper #docs-button::after {\r\n  background-image: url(/public/lexical/icons/file-earmark-text.svg);\r\n}\r\n\r\n#lexical-wrapper #test-recorder-button-snapshot {\r\n  margin-right: auto;\r\n}\r\n\r\n#lexical-wrapper #test-recorder-button-snapshot::after {\r\n  background-image: url(/public/lexical/icons/camera.svg);\r\n}\r\n\r\n#lexical-wrapper #test-recorder-button-copy::after {\r\n  background-image: url(/public/lexical/icons/clipboard.svg);\r\n}\r\n\r\n#lexical-wrapper #test-recorder-button-download::after {\r\n  background-image: url(/public/lexical/icons/download.svg);\r\n}\r\n\r\n#lexical-wrapper .typeahead-popover {\r\n  background: #fff;\r\n  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.3);\r\n  border-radius: 8px;\r\n  position: fixed;\r\n}\r\n\r\n#lexical-wrapper .typeahead-popover ul {\r\n  padding: 0;\r\n  list-style: none;\r\n  margin: 0;\r\n  border-radius: 8px;\r\n  max-height: 200px;\r\n  overflow-y: scroll;\r\n}\r\n\r\n#lexical-wrapper .typeahead-popover ul::-webkit-scrollbar {\r\n  display: none;\r\n}\r\n\r\n#lexical-wrapper .typeahead-popover ul {\r\n  -ms-overflow-style: none;\r\n  scrollbar-width: none;\r\n}\r\n\r\n#lexical-wrapper .typeahead-popover ul li {\r\n  margin: 0;\r\n  min-width: 180px;\r\n  font-size: 14px;\r\n  outline: none;\r\n  cursor: pointer;\r\n  border-radius: 8px;\r\n}\r\n\r\n#lexical-wrapper .typeahead-popover ul li.selected {\r\n  background: #eee;\r\n}\r\n\r\n#lexical-wrapper .typeahead-popover li {\r\n  margin: 0 8px 0 8px;\r\n  padding: 8px;\r\n  color: #050505;\r\n  cursor: pointer;\r\n  line-height: 16px;\r\n  font-size: 15px;\r\n  display: flex;\r\n  align-content: center;\r\n  flex-direction: row;\r\n  flex-shrink: 0;\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  border: 0;\r\n}\r\n\r\n#lexical-wrapper .typeahead-popover li.active {\r\n  display: flex;\r\n  width: 20px;\r\n  height: 20px;\r\n  background-size: contain;\r\n}\r\n\r\n#lexical-wrapper .typeahead-popover li:first-child {\r\n  border-radius: 8px 8px 0px 0px;\r\n}\r\n\r\n#lexical-wrapper .typeahead-popover li:last-child {\r\n  border-radius: 0px 0px 8px 8px;\r\n}\r\n\r\n#lexical-wrapper .typeahead-popover li:hover {\r\n  background-color: #eee;\r\n}\r\n\r\n#lexical-wrapper .typeahead-popover li .text {\r\n  display: flex;\r\n  line-height: 20px;\r\n  flex-grow: 1;\r\n  min-width: 150px;\r\n}\r\n\r\n#lexical-wrapper .typeahead-popover li .icon {\r\n  display: flex;\r\n  width: 20px;\r\n  height: 20px;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n          user-select: none;\r\n  margin-right: 8px;\r\n  line-height: 16px;\r\n  background-size: contain;\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n}\r\n\r\n#lexical-wrapper .component-picker-menu {\r\n  width: 200px;\r\n}\r\n\r\n#lexical-wrapper .mentions-menu {\r\n  width: 250px;\r\n}\r\n\r\n#lexical-wrapper .auto-embed-menu {\r\n  width: 150px;\r\n}\r\n\r\n#lexical-wrapper .emoji-menu {\r\n  width: 200px;\r\n}\r\n\r\n#lexical-wrapper i.palette {\r\n  background-image: url(/public/lexical/icons/palette.svg);\r\n}\r\n\r\n#lexical-wrapper i.bucket {\r\n  background-image: url(/public/lexical/icons/paint-bucket.svg);\r\n}\r\n\r\n#lexical-wrapper i.bold {\r\n  background-image: url(/public/lexical/icons/type-bold.svg);\r\n}\r\n\r\n#lexical-wrapper i.italic {\r\n  background-image: url(/public/lexical/icons/type-italic.svg);\r\n}\r\n\r\n#lexical-wrapper i.clear {\r\n  background-image: url(/public/lexical/icons/trash.svg);\r\n}\r\n\r\n#lexical-wrapper i.code {\r\n  background-image: url(/public/lexical/icons/code.svg);\r\n}\r\n\r\n#lexical-wrapper i.underline {\r\n  background-image: url(/public/lexical/icons/type-underline.svg);\r\n}\r\n\r\n#lexical-wrapper i.strikethrough {\r\n  background-image: url(/public/lexical/icons/type-strikethrough.svg);\r\n}\r\n\r\n#lexical-wrapper i.subscript {\r\n  background-image: url(/public/lexical/icons/type-subscript.svg);\r\n}\r\n\r\n#lexical-wrapper i.superscript {\r\n  background-image: url(/public/lexical/icons/type-superscript.svg);\r\n}\r\n\r\n#lexical-wrapper i.link {\r\n  background-image: url(/public/lexical/icons/link.svg);\r\n}\r\n\r\n#lexical-wrapper i.horizontal-rule {\r\n  background-image: url(/public/lexical/icons/horizontal-rule.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.plus {\r\n  background-image: url(/public/lexical/icons/plus.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.caret-right {\r\n  background-image: url(/public/lexical/icons/caret-right-fill.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.dropdown-more {\r\n  background-image: url(/public/lexical/icons/dropdown-more.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.font-color {\r\n  background-image: url(/public/lexical/icons/font-color.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.font-family {\r\n  background-image: url(/public/lexical/icons/font-family.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.bg-color {\r\n  background-image: url(/public/lexical/icons/bg-color.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.table {\r\n  background-color: #6c757d;\r\n  mask-image: url(/public/lexical/icons/table.svg);\r\n  -webkit-mask-image: url(/public/lexical/icons/table.svg);\r\n  mask-repeat: no-repeat;\r\n  -webkit-mask-repeat: no-repeat;\r\n  mask-size: contain;\r\n  -webkit-mask-size: contain;\r\n}\r\n\r\n#lexical-wrapper i.image {\r\n  background-image: url(/public/lexical/icons/file-image.svg);\r\n}\r\n\r\n#lexical-wrapper i.table {\r\n  background-image: url(/public/lexical/icons/table.svg);\r\n}\r\n\r\n#lexical-wrapper i.close {\r\n  background-image: url(/public/lexical/icons/close.svg);\r\n}\r\n\r\n#lexical-wrapper i.figma {\r\n  background-image: url(/public/lexical/icons/figma.svg);\r\n}\r\n\r\n#lexical-wrapper i.poll {\r\n  background-image: url(/public/lexical/icons/card-checklist.svg);\r\n}\r\n\r\n#lexical-wrapper i.columns {\r\n  background-image: url(/public/lexical/icons/3-columns.svg);\r\n}\r\n\r\n#lexical-wrapper i.tweet {\r\n  background-image: url(/public/lexical/icons/tweet.svg);\r\n}\r\n\r\n#lexical-wrapper i.youtube {\r\n  background-image: url(/public/lexical/icons/youtube.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.left-align,\r\n#lexical-wrapper i.left-align {\r\n  background-image: url(/public/lexical/icons/text-left.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.center-align,\r\n#lexical-wrapper i.center-align {\r\n  background-image: url(/public/lexical/icons/text-center.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.right-align,\r\n#lexical-wrapper i.right-align {\r\n  background-image: url(/public/lexical/icons/text-right.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.justify-align,\r\n#lexical-wrapper i.justify-align {\r\n  background-image: url(/public/lexical/icons/justify.svg);\r\n}\r\n\r\n#lexical-wrapper i.indent {\r\n  background-image: url(/public/lexical/icons/indent.svg);\r\n}\r\n\r\n#lexical-wrapper i.markdown {\r\n  background-image: url(/public/lexical/icons/markdown.svg);\r\n}\r\n\r\n#lexical-wrapper i.outdent {\r\n  background-image: url(/public/lexical/icons/outdent.svg);\r\n}\r\n\r\n#lexical-wrapper i.undo {\r\n  background-image: url(/public/lexical/icons/arrow-counterclockwise.svg);\r\n}\r\n\r\n#lexical-wrapper i.redo {\r\n  background-image: url(/public/lexical/icons/arrow-clockwise.svg);\r\n}\r\n\r\n#lexical-wrapper i.sticky {\r\n  background-image: url(/public/lexical/icons/sticky.svg);\r\n}\r\n\r\n#lexical-wrapper i.mic {\r\n  background-image: url(/public/lexical/icons/mic.svg);\r\n}\r\n\r\n#lexical-wrapper i.import {\r\n  background-image: url(/public/lexical/icons/upload.svg);\r\n}\r\n\r\n#lexical-wrapper i.export {\r\n  background-image: url(/public/lexical/icons/download.svg);\r\n}\r\n\r\n#lexical-wrapper i.share {\r\n  background-image: url(/public/lexical/icons/send.svg);\r\n}\r\n\r\n#lexical-wrapper i.diagram-2 {\r\n  background-image: url(/public/lexical/icons/diagram-2.svg);\r\n}\r\n\r\n#lexical-wrapper i.user {\r\n  background-image: url(/public/lexical/icons/user.svg);\r\n}\r\n\r\n#lexical-wrapper i.equation {\r\n  background-image: url(/public/lexical/icons/plus-slash-minus.svg);\r\n}\r\n\r\n#lexical-wrapper i.gif {\r\n  background-image: url(/public/lexical/icons/filetype-gif.svg);\r\n}\r\n\r\n#lexical-wrapper i.copy {\r\n  background-image: url(/public/lexical/icons/copy.svg);\r\n}\r\n\r\n#lexical-wrapper i.success {\r\n  background-image: url(/public/lexical/icons/success.svg);\r\n}\r\n\r\n#lexical-wrapper i.prettier {\r\n  background-image: url(/public/lexical/icons/prettier.svg);\r\n}\r\n\r\n#lexical-wrapper i.prettier-error {\r\n  background-image: url(/public/lexical/icons/prettier-error.svg);\r\n}\r\n\r\n#lexical-wrapper i.page-break,\r\n#lexical-wrapper .icon.page-break {\r\n  background-image: url(/public/lexical/icons/scissors.svg);\r\n}\r\n\r\n#lexical-wrapper .link-editor .button.active,\r\n#lexical-wrapper .toolbar .button.active {\r\n  background-color: #dfe8fa;\r\n}\r\n\r\n#lexical-wrapper .link-editor .link-input {\r\n  display: block;\r\n  width: calc(100% - 75px);\r\n  box-sizing: border-box;\r\n  margin: 12px 12px;\r\n  padding: 8px 12px;\r\n  border-radius: 15px;\r\n  background-color: #eee;\r\n  font-size: 15px;\r\n  color: #050505;\r\n  border: 0;\r\n  outline: 0;\r\n  position: relative;\r\n  font-family: inherit;\r\n}\r\n\r\n#lexical-wrapper .link-editor .link-view {\r\n  display: block;\r\n  width: calc(100% - 24px);\r\n  margin: 8px 12px;\r\n  padding: 8px 12px;\r\n  border-radius: 15px;\r\n  font-size: 15px;\r\n  color: #050505;\r\n  border: 0;\r\n  outline: 0;\r\n  position: relative;\r\n  font-family: inherit;\r\n}\r\n\r\n#lexical-wrapper .link-editor .link-view a {\r\n  display: block;\r\n  word-break: break-word;\r\n  width: calc(100% - 33px);\r\n}\r\n\r\n#lexical-wrapper .link-editor div.link-edit {\r\n  background-image: url(/public/lexical/icons/pencil-fill.svg);\r\n  background-size: 16px;\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n  width: 35px;\r\n  vertical-align: -0.25em;\r\n  position: absolute;\r\n  right: 30px;\r\n  top: 0;\r\n  bottom: 0;\r\n  cursor: pointer;\r\n}\r\n\r\n#lexical-wrapper .link-editor div.link-trash {\r\n  background-image: url(/public/lexical/icons/trash.svg);\r\n  background-size: 16px;\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n  width: 35px;\r\n  vertical-align: -0.25em;\r\n  position: absolute;\r\n  right: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  cursor: pointer;\r\n}\r\n\r\n#lexical-wrapper .link-editor div.link-cancel {\r\n  background-image: url(/public/lexical/icons/close.svg);\r\n  background-size: 16px;\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n  width: 35px;\r\n  vertical-align: -0.25em;\r\n  margin-right: 28px;\r\n  position: absolute;\r\n  right: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  cursor: pointer;\r\n}\r\n\r\n#lexical-wrapper .link-editor div.link-confirm {\r\n  background-image: url(/public/lexical/icons/success-alt.svg);\r\n  background-size: 16px;\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n  width: 35px;\r\n  vertical-align: -0.25em;\r\n  margin-right: 2px;\r\n  position: absolute;\r\n  right: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  cursor: pointer;\r\n}\r\n\r\n#lexical-wrapper .link-editor .link-input a {\r\n  color: #216fdb;\r\n  text-decoration: underline;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  margin-right: 30px;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n#lexical-wrapper .link-editor .link-input a:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n#lexical-wrapper .link-editor .font-size-wrapper,\r\n#lexical-wrapper .link-editor .font-family-wrapper {\r\n  display: flex;\r\n  margin: 0 4px;\r\n}\r\n\r\n#lexical-wrapper .link-editor select {\r\n  padding: 6px;\r\n  border: none;\r\n  background-color: rgba(0, 0, 0, 0.075);\r\n  border-radius: 4px;\r\n}\r\n\r\n#lexical-wrapper .mention:focus {\r\n  box-shadow: #000 0px 0px 0px 2px;\r\n  outline: none;\r\n}\r\n\r\n#lexical-wrapper .characters-limit {\r\n  color: #888;\r\n  font-size: 12px;\r\n  text-align: right;\r\n  display: block;\r\n  position: absolute;\r\n  left: 12px;\r\n  bottom: 5px;\r\n}\r\n\r\n#lexical-wrapper .characters-limit.characters-limit-exceeded {\r\n  color: red;\r\n}\r\n\r\n#lexical-wrapper .dropdown {\r\n  z-index: 100;\r\n  display: block;\r\n  position: fixed;\r\n  box-shadow:\r\n    0 12px 28px 0 rgba(0, 0, 0, 0.2),\r\n    0 2px 4px 0 rgba(0, 0, 0, 0.1),\r\n    inset 0 0 0 1px rgba(255, 255, 255, 0.5);\r\n  border-radius: 8px;\r\n  min-height: 40px;\r\n  background-color: #fff;\r\n}\r\n\r\n#lexical-wrapper .dropdown .item {\r\n  margin: 0 8px 0 8px;\r\n  padding: 8px;\r\n  color: #050505;\r\n  cursor: pointer;\r\n  line-height: 16px;\r\n  font-size: 15px;\r\n  display: flex;\r\n  align-content: center;\r\n  flex-direction: row;\r\n  flex-shrink: 0;\r\n  justify-content: space-between;\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  border: 0;\r\n  max-width: 250px;\r\n  min-width: 100px;\r\n}\r\n\r\n#lexical-wrapper .dropdown .item.fontsize-item,\r\n#lexical-wrapper .dropdown .item.fontsize-item .text {\r\n  min-width: unset;\r\n}\r\n\r\n#lexical-wrapper .dropdown .item .active {\r\n  display: flex;\r\n  width: 20px;\r\n  height: 20px;\r\n  background-size: contain;\r\n}\r\n\r\n#lexical-wrapper .dropdown .item:first-child {\r\n  margin-top: 8px;\r\n}\r\n\r\n#lexical-wrapper .dropdown .item:last-child {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n#lexical-wrapper .dropdown .item:hover {\r\n  background-color: #eee;\r\n}\r\n\r\n#lexical-wrapper .dropdown .item .text {\r\n  display: flex;\r\n  line-height: 20px;\r\n  flex-grow: 1;\r\n  min-width: 150px;\r\n}\r\n\r\n#lexical-wrapper .dropdown .item .icon {\r\n  display: flex;\r\n  width: 20px;\r\n  height: 20px;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n          user-select: none;\r\n  margin-right: 12px;\r\n  line-height: 16px;\r\n  background-size: contain;\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n}\r\n\r\n#lexical-wrapper .dropdown .divider {\r\n  width: auto;\r\n  background-color: #eee;\r\n  margin: 4px 8px;\r\n  height: 1px;\r\n}\r\n\r\n@media screen and (max-width: 1100px) {\r\n  #lexical-wrapper .dropdown-button-text {\r\n    display: none !important;\r\n  }\r\n\r\n  #lexical-wrapper .dialog-dropdown > .dropdown-button-text {\r\n    display: flex !important;\r\n  }\r\n\r\n  #lexical-wrapper .font-size .dropdown-button-text {\r\n    display: flex !important;\r\n  }\r\n\r\n  #lexical-wrapper .code-language .dropdown-button-text {\r\n    display: flex !important;\r\n  }\r\n}\r\n\r\n#lexical-wrapper .icon.paragraph {\r\n  background-image: url(/public/lexical/icons/text-paragraph.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.h1 {\r\n  background-image: url(/public/lexical/icons/type-h1.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.h2 {\r\n  background-image: url(/public/lexical/icons/type-h2.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.h3 {\r\n  background-image: url(/public/lexical/icons/type-h3.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.h4 {\r\n  background-image: url(/public/lexical/icons/type-h4.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.h5 {\r\n  background-image: url(/public/lexical/icons/type-h5.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.h6 {\r\n  background-image: url(/public/lexical/icons/type-h6.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.bullet-list,\r\n#lexical-wrapper .icon.bullet {\r\n  background-image: url(/public/lexical/icons/list-ul.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.check-list,\r\n#lexical-wrapper .icon.check {\r\n  background-image: url(/public/lexical/icons/square-check.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.numbered-list,\r\n#lexical-wrapper .icon.number {\r\n  background-image: url(/public/lexical/icons/list-ol.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.quote {\r\n  background-image: url(/public/lexical/icons/chat-square-quote.svg);\r\n}\r\n\r\n#lexical-wrapper .icon.code {\r\n  background-image: url(/public/lexical/icons/code.svg);\r\n}\r\n\r\n#lexical-wrapper .switches {\r\n  z-index: 6;\r\n  position: fixed;\r\n  left: 10px;\r\n  bottom: 70px;\r\n  animation: slide-in 0.4s ease;\r\n}\r\n\r\n@keyframes slide-in {\r\n  0% {\r\n    opacity: 0;\r\n    transform: translateX(-200px);\r\n  }\r\n\r\n  100% {\r\n    opacity: 1;\r\n    transform: translateX(0);\r\n  }\r\n}\r\n\r\n#lexical-wrapper .switch {\r\n  display: block;\r\n  color: #444;\r\n  margin: 5px 0;\r\n  background-color: rgba(238, 238, 238, 0.7);\r\n  padding: 5px 10px;\r\n  border-radius: 10px;\r\n}\r\n\r\n#lexical-wrapper #rich-text-switch {\r\n  right: 0;\r\n}\r\n\r\n#lexical-wrapper #character-count-switch {\r\n  right: 130px;\r\n}\r\n\r\n#lexical-wrapper .switch label {\r\n  margin-right: 5px;\r\n  line-height: 24px;\r\n  width: 100px;\r\n  font-size: 14px;\r\n  display: inline-block;\r\n  vertical-align: middle;\r\n}\r\n\r\n#lexical-wrapper .switch button {\r\n  background-color: #ced0d4;\r\n  height: 24px;\r\n  box-sizing: border-box;\r\n  border-radius: 12px;\r\n  width: 44px;\r\n  display: inline-block;\r\n  vertical-align: middle;\r\n  position: relative;\r\n  outline: none;\r\n  cursor: pointer;\r\n  transition: background-color 0.1s;\r\n  border: 2px solid transparent;\r\n}\r\n\r\n#lexical-wrapper .switch button:focus-visible {\r\n  border-color: blue;\r\n}\r\n\r\n#lexical-wrapper .switch button span {\r\n  top: 0px;\r\n  left: 0px;\r\n  display: block;\r\n  position: absolute;\r\n  width: 20px;\r\n  height: 20px;\r\n  border-radius: 12px;\r\n  background-color: white;\r\n  transition: transform 0.2s;\r\n}\r\n\r\n#lexical-wrapper .switch button[aria-checked=\"true\"] {\r\n  background-color: #1877f2;\r\n}\r\n\r\n#lexical-wrapper .switch button[aria-checked=\"true\"] span {\r\n  transform: translateX(20px);\r\n}\r\n\r\n#lexical-wrapper .editor-shell span.editor-image {\r\n  cursor: default;\r\n  display: inline-block;\r\n  position: relative;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n          user-select: none;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .editor-image img {\r\n  max-width: 100%;\r\n  cursor: default;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .editor-image img.focused {\r\n  outline: 2px solid #3c84f4;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n          user-select: none;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .editor-image img.focused.draggable {\r\n  cursor: grab;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .editor-image img.focused.draggable:active {\r\n  cursor: grabbing;\r\n}\r\n\r\n#lexical-wrapper\r\n  .editor-shell\r\n  .editor-image\r\n  .image-caption-container\r\n  .tree-view-output {\r\n  margin: 0;\r\n  border-radius: 0;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .editor-image .image-caption-container {\r\n  display: block;\r\n  position: absolute;\r\n  bottom: 4px;\r\n  left: 0;\r\n  right: 0;\r\n  padding: 0;\r\n  margin: 0;\r\n  border-top: 1px solid #fff;\r\n  background-color: rgba(255, 255, 255, 0.9);\r\n  min-width: 100px;\r\n  color: #000;\r\n  overflow: hidden;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .editor-image .image-caption-button {\r\n  display: block;\r\n  position: absolute;\r\n  bottom: 20px;\r\n  left: 0;\r\n  right: 0;\r\n  width: 30%;\r\n  padding: 10px;\r\n  margin: 0 auto;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 5px;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  min-width: 100px;\r\n  color: #fff;\r\n  cursor: pointer;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n          user-select: none;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .editor-image .image-caption-button:hover {\r\n  background-color: rgba(60, 132, 244, 0.5);\r\n}\r\n\r\n#lexical-wrapper .editor-shell .editor-image .image-edit-button {\r\n  border: 1px solid rgba(0, 0, 0, 0.3);\r\n  border-radius: 5px;\r\n  background-image: url(/public/lexical/icons/pencil-fill.svg);\r\n  background-size: 16px;\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n  width: 35px;\r\n  height: 35px;\r\n  vertical-align: -0.25em;\r\n  position: absolute;\r\n  right: 4px;\r\n  top: 4px;\r\n  cursor: pointer;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n          user-select: none;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .editor-image .image-edit-button:hover {\r\n  background-color: rgba(60, 132, 244, 0.1);\r\n}\r\n\r\n#lexical-wrapper .editor-shell .editor-image .image-resizer {\r\n  display: block;\r\n  width: 7px;\r\n  height: 7px;\r\n  position: absolute;\r\n  background-color: #3c84f4;\r\n  border: 1px solid #fff;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .editor-image .image-resizer.image-resizer-n {\r\n  top: -6px;\r\n  left: 48%;\r\n  cursor: n-resize;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .editor-image .image-resizer.image-resizer-ne {\r\n  top: -6px;\r\n  right: -6px;\r\n  cursor: ne-resize;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .editor-image .image-resizer.image-resizer-e {\r\n  bottom: 48%;\r\n  right: -6px;\r\n  cursor: e-resize;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .editor-image .image-resizer.image-resizer-se {\r\n  bottom: -2px;\r\n  right: -6px;\r\n  cursor: nwse-resize;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .editor-image .image-resizer.image-resizer-s {\r\n  bottom: -2px;\r\n  left: 48%;\r\n  cursor: s-resize;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .editor-image .image-resizer.image-resizer-sw {\r\n  bottom: -2px;\r\n  left: -6px;\r\n  cursor: sw-resize;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .editor-image .image-resizer.image-resizer-w {\r\n  bottom: 48%;\r\n  left: -6px;\r\n  cursor: w-resize;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .editor-image .image-resizer.image-resizer-nw {\r\n  top: -6px;\r\n  left: -6px;\r\n  cursor: nw-resize;\r\n}\r\n\r\n#lexical-wrapper .editor-shell span.inline-editor-image {\r\n  cursor: default;\r\n  display: inline-block;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .inline-editor-image img {\r\n  max-width: 100%;\r\n  cursor: default;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .inline-editor-image img.focused {\r\n  outline: 2px solid #3c84f4;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .inline-editor-image img.focused.draggable {\r\n  cursor: grab;\r\n}\r\n\r\n#lexical-wrapper\r\n  .editor-shell\r\n  .inline-editor-image\r\n  img.focused.draggable:active {\r\n  cursor: grabbing;\r\n}\r\n\r\n#lexical-wrapper\r\n  .editor-shell\r\n  .inline-editor-image\r\n  .image-caption-container\r\n  .tree-view-output {\r\n  margin: 0;\r\n  border-radius: 0;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .inline-editor-image.position-full {\r\n  margin: 1em 0 1em 0;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .inline-editor-image.position-left {\r\n  float: left;\r\n  width: 50%;\r\n  margin: 1em 1em 0 0;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .inline-editor-image.position-right {\r\n  float: right;\r\n  width: 50%;\r\n  margin: 1em 0 0 1em;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .inline-editor-image .image-edit-button {\r\n  display: block;\r\n  position: absolute;\r\n  top: 12px;\r\n  right: 12px;\r\n  padding: 6px 8px;\r\n  margin: 0 auto;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 5px;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  min-width: 60px;\r\n  color: #fff;\r\n  cursor: pointer;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n          user-select: none;\r\n}\r\n\r\n#lexical-wrapper .editor-shell .inline-editor-image .image-edit-button:hover {\r\n  background-color: rgba(60, 132, 244, 0.5);\r\n}\r\n\r\n#lexical-wrapper .editor-shell .inline-editor-image .image-caption-container {\r\n  display: block;\r\n  background-color: #f4f4f4;\r\n  min-width: 100%;\r\n  color: #000;\r\n  overflow: hidden;\r\n}\r\n\r\n#lexical-wrapper .emoji {\r\n  color: transparent;\r\n  caret-color: #050505;\r\n  background-size: 16px 16px;\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n  vertical-align: middle;\r\n  margin: 0 -1px;\r\n}\r\n\r\n#lexical-wrapper .emoji-inner {\r\n  padding: 0 0.15em;\r\n}\r\n\r\n#lexical-wrapper .emoji-inner::-moz-selection {\r\n  color: transparent;\r\n  background-color: rgba(150, 150, 150, 0.4);\r\n}\r\n\r\n#lexical-wrapper .emoji-inner::selection {\r\n  color: transparent;\r\n  background-color: rgba(150, 150, 150, 0.4);\r\n}\r\n\r\n#lexical-wrapper .emoji-inner {\r\n  color: transparent;\r\n  background-color: rgba(150, 150, 150, 0.4);\r\n}\r\n\r\n#lexical-wrapper .emoji.happysmile {\r\n  background-image: url(/public/lexical/emoji/1F642.png);\r\n}\r\n\r\n#lexical-wrapper .emoji.veryhappysmile {\r\n  background-image: url(/public/lexical/emoji/1F600.png);\r\n}\r\n\r\n#lexical-wrapper .emoji.unhappysmile {\r\n  background-image: url(/public/lexical/emoji/1F641.png);\r\n}\r\n\r\n#lexical-wrapper .emoji.heart {\r\n  background-image: url(/public/lexical/emoji/2764.png);\r\n}\r\n\r\n#lexical-wrapper .keyword {\r\n  color: #f1765e;\r\n  font-weight: bold;\r\n}\r\n\r\n#lexical-wrapper .actions {\r\n  position: absolute;\r\n  text-align: right;\r\n  margin: 10px;\r\n  bottom: 0;\r\n  right: 0;\r\n}\r\n\r\n#lexical-wrapper .actions.tree-view {\r\n  border-bottom-left-radius: 0;\r\n  border-bottom-right-radius: 0;\r\n}\r\n\r\n#lexical-wrapper .actions i {\r\n  background-size: contain;\r\n  display: inline-block;\r\n  height: 15px;\r\n  width: 15px;\r\n  vertical-align: -0.25em;\r\n}\r\n\r\n#lexical-wrapper .actions i.indent {\r\n  background-image: url(/public/lexical/icons/indent.svg);\r\n}\r\n\r\n#lexical-wrapper .actions i.outdent {\r\n  background-image: url(/public/lexical/icons/outdent.svg);\r\n}\r\n\r\n#lexical-wrapper .actions i.lock {\r\n  background-image: url(/public/lexical/icons/lock-fill.svg);\r\n}\r\n\r\n#lexical-wrapper .actions i.image {\r\n  background-image: url(/public/lexical/icons/file-image.svg);\r\n}\r\n\r\n#lexical-wrapper .actions i.table {\r\n  background-image: url(/public/lexical/icons/table.svg);\r\n}\r\n\r\n#lexical-wrapper .actions i.unlock {\r\n  background-image: url(/public/lexical/icons/lock.svg);\r\n}\r\n\r\n#lexical-wrapper .actions i.left-align {\r\n  background-image: url(/public/lexical/icons/text-left.svg);\r\n}\r\n\r\n#lexical-wrapper .actions i.center-align {\r\n  background-image: url(/public/lexical/icons/text-center.svg);\r\n}\r\n\r\n#lexical-wrapper .actions i.right-align {\r\n  background-image: url(/public/lexical/icons/text-right.svg);\r\n}\r\n\r\n#lexical-wrapper .actions i.justify-align {\r\n  background-image: url(/public/lexical/icons/justify.svg);\r\n}\r\n\r\n#lexical-wrapper .actions i.disconnect {\r\n  background-image: url(/public/lexical/icons/plug.svg);\r\n}\r\n\r\n#lexical-wrapper .actions i.connect {\r\n  background-image: url(/public/lexical/icons/plug-fill.svg);\r\n}\r\n\r\n#lexical-wrapper .table-cell-action-button-container {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  will-change: transform;\r\n}\r\n\r\n#lexical-wrapper .table-cell-action-button {\r\n  background-color: none;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border: 0;\r\n  position: relative;\r\n  border-radius: 15px;\r\n  color: #222;\r\n  display: inline-block;\r\n  cursor: pointer;\r\n}\r\n\r\n#lexical-wrapper i.chevron-down {\r\n  background-color: transparent;\r\n  background-size: contain;\r\n  display: inline-block;\r\n  height: 8px;\r\n  width: 8px;\r\n  background-image: url(/public/lexical/icons/chevron-down.svg);\r\n}\r\n\r\n#lexical-wrapper .action-button {\r\n  background-color: #eee;\r\n  border: 0;\r\n  padding: 8px 12px;\r\n  position: relative;\r\n  margin-left: 5px;\r\n  border-radius: 15px;\r\n  color: #222;\r\n  display: inline-block;\r\n  cursor: pointer;\r\n}\r\n\r\n#lexical-wrapper .action-button:hover {\r\n  background-color: #ddd;\r\n  color: #000;\r\n}\r\n\r\n#lexical-wrapper .action-button-mic.active {\r\n  animation: mic-pulsate-color 3s infinite;\r\n}\r\n\r\n#lexical-wrapper button.action-button:disabled {\r\n  opacity: 0.6;\r\n  background: #eee;\r\n  cursor: not-allowed;\r\n}\r\n\r\n@keyframes mic-pulsate-color {\r\n  0% {\r\n    background-color: #ffdcdc;\r\n  }\r\n\r\n  50% {\r\n    background-color: #ff8585;\r\n  }\r\n\r\n  100% {\r\n    background-color: #ffdcdc;\r\n  }\r\n}\r\n\r\n@keyframes ripple {\r\n  0% {\r\n    transform: scale(0);\r\n    opacity: 1;\r\n  }\r\n  100% {\r\n    transform: scale(2.5);\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n#lexical-wrapper .debug-timetravel-panel {\r\n  overflow: hidden;\r\n  padding: 0 0 10px 0;\r\n  margin: auto;\r\n  display: flex;\r\n}\r\n\r\n#lexical-wrapper .debug-timetravel-panel-slider {\r\n  padding: 0;\r\n  flex: 8;\r\n}\r\n\r\n#lexical-wrapper .debug-timetravel-panel-button {\r\n  padding: 0;\r\n  border: 0;\r\n  background: none;\r\n  flex: 1;\r\n  color: #fff;\r\n  font-size: 12px;\r\n}\r\n\r\n#lexical-wrapper .debug-timetravel-panel-button:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n#lexical-wrapper .debug-timetravel-button {\r\n  border: 0;\r\n  padding: 0;\r\n  font-size: 12px;\r\n  top: 10px;\r\n  right: 15px;\r\n  position: absolute;\r\n  background: none;\r\n  color: #fff;\r\n}\r\n\r\n#lexical-wrapper .debug-timetravel-button:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n#lexical-wrapper .debug-treetype-button {\r\n  border: 0;\r\n  padding: 0;\r\n  font-size: 12px;\r\n  top: 10px;\r\n  right: 85px;\r\n  position: absolute;\r\n  background: none;\r\n  color: #fff;\r\n}\r\n\r\n#lexical-wrapper .debug-treetype-button:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n#lexical-wrapper .connecting {\r\n  font-size: 15px;\r\n  color: #999;\r\n  overflow: hidden;\r\n  position: absolute;\r\n  text-overflow: ellipsis;\r\n  top: 10px;\r\n  left: 10px;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n          user-select: none;\r\n  white-space: nowrap;\r\n  display: inline-block;\r\n  pointer-events: none;\r\n}\r\n\r\n#lexical-wrapper .ltr {\r\n  text-align: left;\r\n}\r\n\r\n#lexical-wrapper .rtl {\r\n  text-align: right;\r\n}\r\n\r\n#lexical-wrapper .toolbar {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-bottom: 1px;\r\n  background: #fff;\r\n  padding: 4px;\r\n  border-top-left-radius: 10px;\r\n  border-top-right-radius: 10px;\r\n  vertical-align: middle;\r\n  overflow: auto;\r\n  height: -moz-max-content;\r\n  height: max-content;\r\n  width: 100%;\r\n  min-height: 36px;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 0;\r\n}\r\n\r\n#lexical-wrapper button.toolbar-item {\r\n  border: 0;\r\n  display: flex;\r\n  background: none;\r\n  border-radius: 10px;\r\n  padding: 8px;\r\n  cursor: pointer;\r\n  vertical-align: middle;\r\n  flex-shrink: 0;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n#lexical-wrapper button.toolbar-item:disabled {\r\n  cursor: not-allowed;\r\n}\r\n\r\n#lexical-wrapper button.toolbar-item.spaced {\r\n  margin-right: 2px;\r\n}\r\n\r\n#lexical-wrapper button.toolbar-item i.format {\r\n  background-size: contain;\r\n  display: inline-block;\r\n  height: 18px;\r\n  width: 18px;\r\n  vertical-align: -0.25em;\r\n  display: flex;\r\n  opacity: 0.6;\r\n}\r\n\r\n#lexical-wrapper button.toolbar-item:disabled .icon,\r\n#lexical-wrapper button.toolbar-item:disabled .text,\r\n#lexical-wrapper button.toolbar-item:disabled i.format,\r\n#lexical-wrapper button.toolbar-item:disabled .chevron-down {\r\n  opacity: 0.2;\r\n}\r\n\r\n#lexical-wrapper button.toolbar-item.active {\r\n  background-color: rgba(223, 232, 250, 0.3);\r\n}\r\n\r\n#lexical-wrapper button.toolbar-item.active i {\r\n  opacity: 1;\r\n}\r\n\r\n#lexical-wrapper .toolbar-item:hover:not([disabled]) {\r\n  background-color: #eee;\r\n}\r\n\r\n#lexical-wrapper .toolbar-item.font-family .text {\r\n  display: block;\r\n  max-width: 40px;\r\n}\r\n\r\n#lexical-wrapper .toolbar .code-language {\r\n  width: 150px;\r\n}\r\n\r\n#lexical-wrapper .toolbar .toolbar-item .text {\r\n  display: flex;\r\n  line-height: 20px;\r\n  vertical-align: middle;\r\n  font-size: 14px;\r\n  color: #777;\r\n  text-overflow: ellipsis;\r\n  overflow: hidden;\r\n  height: 20px;\r\n  text-align: left;\r\n  padding-right: 10px;\r\n}\r\n\r\n#lexical-wrapper .toolbar .toolbar-item .icon {\r\n  display: flex;\r\n  width: 20px;\r\n  height: 20px;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n          user-select: none;\r\n  margin-right: 8px;\r\n  line-height: 16px;\r\n  background-size: contain;\r\n}\r\n\r\n#lexical-wrapper .toolbar i.chevron-down,\r\n#lexical-wrapper .toolbar-item i.chevron-down {\r\n  margin-top: 3px;\r\n  width: 16px;\r\n  height: 16px;\r\n  display: flex;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n          user-select: none;\r\n}\r\n\r\n#lexical-wrapper .toolbar i.chevron-down.inside {\r\n  width: 16px;\r\n  height: 16px;\r\n  display: flex;\r\n  margin-left: -25px;\r\n  margin-top: 11px;\r\n  margin-right: 10px;\r\n  pointer-events: none;\r\n}\r\n\r\n#lexical-wrapper .toolbar .divider {\r\n  width: 1px;\r\n  background-color: #eee;\r\n  margin: 0 4px;\r\n}\r\n\r\n#lexical-wrapper .sticky-note-container {\r\n  position: absolute;\r\n  z-index: 9;\r\n  width: 120px;\r\n  display: inline-block;\r\n}\r\n\r\n#lexical-wrapper .sticky-note {\r\n  line-height: 1;\r\n  text-align: left;\r\n  width: 120px;\r\n  margin: 25px;\r\n  padding: 20px 10px;\r\n  position: relative;\r\n  border: 1px solid #e8e8e8;\r\n  font-family: \"Reenie Beanie\";\r\n  font-size: 24px;\r\n  border-bottom-right-radius: 60px 5px;\r\n  display: block;\r\n  cursor: move;\r\n}\r\n\r\n#lexical-wrapper .sticky-note:after {\r\n  content: \"\";\r\n  position: absolute;\r\n  z-index: -1;\r\n  right: 0px;\r\n  bottom: 20px;\r\n  width: 120px;\r\n  height: 25px;\r\n  background: rgba(0, 0, 0, 0.2);\r\n  box-shadow: 2px 15px 5px rgba(0, 0, 0, 0.4);\r\n  transform: matrix(-1, -0.1, 0, 1, 0, 0);\r\n}\r\n\r\n#lexical-wrapper .sticky-note.yellow {\r\n  border-top: 1px solid #fdfd86;\r\n  background: linear-gradient(\r\n    135deg,\r\n    #ff8 81%,\r\n    #ff8 82%,\r\n    #ff8 82%,\r\n    #ffffc6 100%\r\n  );\r\n}\r\n\r\n#lexical-wrapper .sticky-note.pink {\r\n  border-top: 1px solid #e7d1e4;\r\n  background: linear-gradient(\r\n    135deg,\r\n    #f7cbe8 81%,\r\n    #f7cbe8 82%,\r\n    #f7cbe8 82%,\r\n    #e7bfe1 100%\r\n  );\r\n}\r\n\r\n#lexical-wrapper .sticky-note-container.dragging {\r\n  transition: none !important;\r\n}\r\n\r\n#lexical-wrapper .sticky-note div {\r\n  cursor: text;\r\n}\r\n\r\n#lexical-wrapper .sticky-note .delete {\r\n  border: 0;\r\n  background: none;\r\n  position: absolute;\r\n  top: 8px;\r\n  right: 10px;\r\n  font-size: 10px;\r\n  cursor: pointer;\r\n  opacity: 0.5;\r\n}\r\n\r\n#lexical-wrapper .sticky-note .delete:hover {\r\n  font-weight: bold;\r\n  opacity: 1;\r\n}\r\n\r\n#lexical-wrapper .sticky-note .color {\r\n  border: 0;\r\n  background: none;\r\n  position: absolute;\r\n  top: 8px;\r\n  right: 25px;\r\n  cursor: pointer;\r\n  opacity: 0.5;\r\n}\r\n\r\n#lexical-wrapper .sticky-note .color:hover {\r\n  opacity: 1;\r\n}\r\n\r\n#lexical-wrapper .sticky-note .color i {\r\n  display: block;\r\n  width: 12px;\r\n  height: 12px;\r\n  background-size: contain;\r\n}\r\n\r\n#lexical-wrapper .github-corner:hover .octo-arm {\r\n  animation: octocat-wave 560ms ease-in-out;\r\n}\r\n\r\n@keyframes octocat-wave {\r\n  0%,\r\n  100% {\r\n    transform: rotate(0);\r\n  }\r\n\r\n  20%,\r\n  60% {\r\n    transform: rotate(-25deg);\r\n  }\r\n\r\n  40%,\r\n  80% {\r\n    transform: rotate(10deg);\r\n  }\r\n}\r\n\r\n@media (max-width: 500px) {\r\n  #lexical-wrapper .github-corner:hover .octo-arm {\r\n    animation: none;\r\n  }\r\n\r\n  #lexical-wrapper .github-corner .octo-arm {\r\n    animation: octocat-wave 560ms ease-in-out;\r\n  }\r\n}\r\n\r\n#lexical-wrapper .spacer {\r\n  letter-spacing: -2px;\r\n}\r\n\r\n#lexical-wrapper .editor-equation {\r\n  cursor: default;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n          user-select: none;\r\n}\r\n\r\n#lexical-wrapper .editor-equation.focused {\r\n  outline: 2px solid #3c84f4;\r\n}\r\n\r\n#lexical-wrapper button.item i {\r\n  opacity: 0.6;\r\n}\r\n\r\n#lexical-wrapper button.item.dropdown-item-active {\r\n  background-color: rgba(223, 232, 250, 0.3);\r\n}\r\n\r\n#lexical-wrapper button.item.dropdown-item-active i {\r\n  opacity: 1;\r\n}\r\n\r\n#lexical-wrapper .TableNode__contentEditable {\r\n  min-height: 20px;\r\n  border: 0px;\r\n  resize: none;\r\n  cursor: text;\r\n  display: block;\r\n  position: relative;\r\n  outline: 0px;\r\n  padding: 0;\r\n  -webkit-user-select: text;\r\n     -moz-user-select: text;\r\n          user-select: text;\r\n  font-size: 15px;\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n  z-index: 3;\r\n}\r\n\r\n#lexical-wrapper .LexicalEditorTheme__blockCursor {\r\n  display: block;\r\n  pointer-events: none;\r\n  position: absolute;\r\n}\r\n\r\n#lexical-wrapper .LexicalEditorTheme__blockCursor:after {\r\n  content: \"\";\r\n  display: block;\r\n  position: absolute;\r\n  top: -2px;\r\n  width: 20px;\r\n  border-top: 1px solid black;\r\n  animation: CursorBlink 1.1s steps(2, start) infinite;\r\n}\r\n\r\n@keyframes CursorBlink {\r\n  to {\r\n    visibility: hidden;\r\n  }\r\n}\r\n\r\n#lexical-wrapper .dialog-dropdown {\r\n  background-color: #eee !important;\r\n  margin-bottom: 10px;\r\n  width: 100%;\r\n}\r\n\r\n/* FE CUSTOM */\r\n\r\n#lexical-wrapper img,\r\n#lexical-wrapper iframe,\r\n#lexical-wrapper audio,\r\n#lexical-wrapper video {\r\n  max-width: 100%;\r\n  /* height: auto; */\r\n  margin: 20px auto;\r\n}\r\n#lexical-wrapper .LexicalEditorTheme__paragraph > img {\r\n  display: initial !important;\r\n}\r\n\r\n.LexicalEditorTheme__ul {\r\n  margin-top: 12px;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.LexicalEditorTheme__listItem {\r\n  margin-top: 8px;\r\n  margin-bottom: 8px;\r\n  line-height: 150%;\r\n}\r\n\r\n.LexicalEditorTheme__paragraph {\r\n  line-height: 28px;\r\n}\r\n\r\n/* Layout Container Styles */\r\n#lexical-wrapper .LexicalEditorTheme__layoutContainer,\r\n.LexicalEditorTheme__layoutContainer,\r\n#lexical-wrapper div[data-lexical-layout-container=\"true\"],\r\ndiv[data-lexical-layout-container=\"true\"] {\r\n  display: grid;\r\n  gap: 10px;\r\n  margin: 10px 0;\r\n}\r\n\r\n#lexical-wrapper .LexicalEditorTheme__layoutItem,\r\n.LexicalEditorTheme__layoutItem {\r\n  border: 1px dashed #ddd;\r\n  padding: 8px 16px;\r\n  min-height: 50px;\r\n}\r\n\r\n/* Remove incorrect grid properties from layout items */\r\n#lexical-wrapper .LexicalEditorTheme__layoutItem,\r\n.LexicalEditorTheme__layoutItem {\r\n  display: block !important;\r\n  grid-template-columns: unset !important;\r\n}\r\n"], "names": [], "mappings": "AAGA;;;;;;;;;;;;AAYA;;;;;;;;AAQA;;;;AAIA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;;;AAOA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;;;AAcA;;;;;;;;;;;;AAYA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;;AAgBA;;;;AAIA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;AAOA;;;;;;;;;AASA;;;;AAIA;;;;;AAKA;;;;;;;;;AASA;;;;AAIA;;;;;;;;;;;;;;;;AAgBA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;AAOA;;;;;;;;;;;;;;AAcA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;AAKA;;;;;;;;;;;;;;;;AAgBA;;;;;;;;;;;;;;AAcA;;;;;;AAMA;;;;;;;;;;;;;;AAcA;;;;;;;;;;;;;;AAcA;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;;;;;AAeA;;;;;;;;;AASA;;;;AAIA;;;;;AAMA;;;;;;;AAOA;;;;;AAKA;;;;;;;;;;AAUA;;;;AAIA;;;;;;;;;;AAaA;;;;;;;;;;;;;;;;;;;AAmBA;;;;AAKA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;AAOA;;;;;;;;;;;;;;AAcA;;;;;;;AAOA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;;;;AAYA;;;;;;;;;AASA;;;;AAIA;;;;AAIA;;;;;;;;;AASA;;;;;;;;;;;;;;;AAeA;;;;AAIA;;;;;;;;;;;;AAYA;;;;AAIA;;;;AAIA;;;;;;;;;AASA;;;;;AAKA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;;AASA;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;;;;;;;;;;AAoBA;;;;AAIA;;;;;;;;;;;;;;;;;;;AAmBA;;;;AAIA;;;;;;;;;AASA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;AAOA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAOA;;;;;AASA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;;;;;;;AAkBA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;;AAUA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;;;AAQA;;;;;AAKA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;AAOA;;;;;;;;;;;;;AAaA;;;;;;;;;AASA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;AAIA;;;;;;AAMA;;;;;;;;;;;;;;AAcA;;;;;;;;;;;;AAWA;;;;;;;AAOA;;;;;AAKA;;;;;;;;;AASA;;;;AAIA;;;;;;;;;;;AAWA;;;;AAIA;;;;;;;;;;;AAWA;;;;AAIA;;;;;;;;;;;;;;;;AAgBA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;;;;;AAmBA;;;;;;;;;;;;;AAaA;;;;AAIA;;;;AAIA;;;;;;;;;;AAUA;;;;AAOA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;;AAaA;;;;;;;;;;;;AAYA;;;;;;;;;;AAWA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;;;AAaA;;;;;AAWA;;;;;AAWA;;;;AAIA;;;;AAIA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;;;;;AAUA;;;;AAIA;;;;;;;AAOA;;;;AAIA;;;;;;;;;;;;;;AAiBA;EACE;;;;EAIA;;;;;AAKF;;;;AAIA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;;;;AAkBA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;AAQA;;;;;AAQA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;AAKA;;;;;;AASA;;;;;;AAQA"}}, {"offset": {"line": 1770, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}