{"version": 3, "sources": [], "sections": [{"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/i18n/index.js"], "sourcesContent": ["import i18n from \"i18next\"\r\nimport LanguageDetector from \"i18next-browser-languagedetector\"\r\nimport { initReactI18next } from \"react-i18next\"\r\n\r\ni18n\r\n  .use(LanguageDetector)\r\n  .use(initReactI18next)\r\n  .init({\r\n    debug: false,\r\n    lng: \"vi\",\r\n    fallbackLng: \"vi\",\r\n    interpolation: {\r\n      escapeValue: false,\r\n    },\r\n    defaultNS: \"layout\",\r\n    ns: [\r\n      \"layout\",\r\n      \"templates\",\r\n      \"product_detail\",\r\n      \"cart\",\r\n      \"checkout\",\r\n      \"account\",\r\n      \"order_confirm\",\r\n      \"wishlist\",\r\n      \"blog\",\r\n      \"collections\",\r\n    ],\r\n\r\n    resources: {\r\n      en: {\r\n        layout: require(\"./locales/en/layout.json\"),\r\n        templates: require(\"./locales/en/templates.json\"),\r\n        product_detail: require(\"./locales/en/product_detail.json\"),\r\n        cart: require(\"./locales/en/cart.json\"),\r\n        checkout: require(\"./locales/en/checkout.json\"),\r\n        account: require(\"./locales/en/account.json\"),\r\n        order_confirm: require(\"./locales/en/order_confirm.json\"),\r\n        wishlist: require(\"./locales/en/wishlist.json\"),\r\n        blog: require(\"./locales/en/blog.json\"),\r\n        collections: require(\"./locales/en/collections.json\"),\r\n      },\r\n      vi: {\r\n        layout: require(\"./locales/vi/layout.json\"),\r\n        templates: require(\"./locales/vi/templates.json\"),\r\n        product_detail: require(\"./locales/vi/product_detail.json\"),\r\n        cart: require(\"./locales/vi/cart.json\"),\r\n        checkout: require(\"./locales/vi/checkout.json\"),\r\n        account: require(\"./locales/vi/account.json\"),\r\n        order_confirm: require(\"./locales/vi/order_confirm.json\"),\r\n        wishlist: require(\"./locales/vi/wishlist.json\"),\r\n        blog: require(\"./locales/vi/blog.json\"),\r\n        collections: require(\"./locales/vi/collections.json\"),\r\n      },\r\n    },\r\n  })\r\n\r\nexport default i18n\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;;;;AAEA,oJAAA,CAAA,UAAI,CACD,GAAG,CAAC,0MAAA,CAAA,UAAgB,EACpB,GAAG,CAAC,qKAAA,CAAA,mBAAgB,EACpB,IAAI,CAAC;IACJ,OAAO;IACP,KAAK;IACL,aAAa;IACb,eAAe;QACb,aAAa;IACf;IACA,WAAW;IACX,IAAI;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,WAAW;QACT,IAAI;YACF,MAAM;YACN,SAAS;YACT,cAAc;YACd,IAAI;YACJ,QAAQ;YACR,OAAO;YACP,aAAa;YACb,QAAQ;YACR,IAAI;YACJ,WAAW;QACb;QACA,IAAI;YACF,MAAM;YACN,SAAS;YACT,cAAc;YACd,IAAI;YACJ,QAAQ;YAC<PERSON>,OAAO;YAC<PERSON>,aAAa;YACb,QAAQ;YACR,IAAI;YACJ,WAAW;QACb;IACF;AACF;uCAEa,oJAAA,CAAA,UAAI"}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/lib/context/provider-i18n.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport i18n from \"i18n\"\r\nimport { useEffect } from \"react\"\r\nimport { I18nextProvider } from \"react-i18next\"\r\n\r\ntype TProps = {\r\n  children: React.ReactNode\r\n  defaultLanguage: string\r\n}\r\n\r\nconst I18nProvider = ({ children, defaultLanguage }: TProps) => {\r\n  useEffect(() => {\r\n    i18n.changeLanguage(defaultLanguage)\r\n  }, [defaultLanguage])\r\n\r\n  return (\r\n    <I18nextProvider i18n={i18n} defaultNS=\"layout\">\r\n      {children}\r\n    </I18nextProvider>\r\n  )\r\n}\r\n\r\nexport default I18nProvider\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;AAWA,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAE,eAAe,EAAU;;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,uHAAA,CAAA,UAAI,CAAC,cAAc,CAAC;QACtB;iCAAG;QAAC;KAAgB;IAEpB,qBACE,6LAAC,oKAAA,CAAA,kBAAe;QAAC,MAAM,uHAAA,CAAA,UAAI;QAAE,WAAU;kBACpC;;;;;;AAGP;GAVM;KAAA;uCAYS"}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/contexts/cart-bubble-context.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport React, { createContext, use<PERSON><PERSON>back, useContext, useState } from \"react\"\r\n\r\ninterface BubbleAnimation {\r\n  id: string\r\n  startX: number\r\n  startY: number\r\n  endX: number\r\n  endY: number\r\n  productImage?: string\r\n  productTitle?: string\r\n}\r\n\r\ninterface CartBubbleContextType {\r\n  animations: BubbleAnimation[]\r\n  triggerBubble: (params: {\r\n    startX: number\r\n    startY: number\r\n    endX: number\r\n    endY: number\r\n    productImage?: string\r\n    productTitle?: string\r\n  }) => void\r\n  removeAnimation: (id: string) => void\r\n}\r\n\r\nconst CartBubbleContext = createContext<CartBubbleContextType | undefined>(\r\n  undefined\r\n)\r\n\r\nexport const useCartBubble = () => {\r\n  const context = useContext(CartBubbleContext)\r\n  if (!context) {\r\n    throw new Error(\"useCartBubble must be used within a CartBubbleProvider\")\r\n  }\r\n  return context\r\n}\r\n\r\nexport const CartBubbleProvider: React.FC<{ children: React.ReactNode }> = ({\r\n  children,\r\n}) => {\r\n  const [animations, setAnimations] = useState<BubbleAnimation[]>([])\r\n\r\n  const triggerBubble = useCallback(\r\n    (params: {\r\n      startX: number\r\n      startY: number\r\n      endX: number\r\n      endY: number\r\n      productImage?: string\r\n      productTitle?: string\r\n    }) => {\r\n      const id = `bubble-${Date.now()}-${Math.random()}`\r\n      const newAnimation: BubbleAnimation = {\r\n        id,\r\n        ...params,\r\n      }\r\n\r\n      setAnimations((prev) => [...prev, newAnimation])\r\n\r\n      setTimeout(() => {\r\n        setAnimations((prev) => prev.filter((anim) => anim.id !== id))\r\n      }, 2000)\r\n    },\r\n    []\r\n  )\r\n\r\n  const removeAnimation = useCallback((id: string) => {\r\n    setAnimations((prev) => prev.filter((anim) => anim.id !== id))\r\n  }, [])\r\n\r\n  return (\r\n    <CartBubbleContext.Provider\r\n      value={{\r\n        animations,\r\n        triggerBubble,\r\n        removeAnimation,\r\n      }}\r\n    >\r\n      {children}\r\n    </CartBubbleContext.Provider>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AA2BA,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EACpC;AAGK,MAAM,gBAAgB;;IAC3B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAQN,MAAM,qBAA8D,CAAC,EAC1E,QAAQ,EACT;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAElE,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAC9B,CAAC;YAQC,MAAM,KAAK,CAAC,OAAO,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,IAAI;YAClD,MAAM,eAAgC;gBACpC;gBACA,GAAG,MAAM;YACX;YAEA;iEAAc,CAAC,OAAS;2BAAI;wBAAM;qBAAa;;YAE/C;iEAAW;oBACT;yEAAc,CAAC,OAAS,KAAK,MAAM;iFAAC,CAAC,OAAS,KAAK,EAAE,KAAK;;;gBAC5D;gEAAG;QACL;wDACA,EAAE;IAGJ,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YACnC;mEAAc,CAAC,OAAS,KAAK,MAAM;2EAAC,CAAC,OAAS,KAAK,EAAE,KAAK;;;QAC5D;0DAAG,EAAE;IAEL,qBACE,6LAAC,kBAAkB,QAAQ;QACzB,OAAO;YACL;YACA;YACA;QACF;kBAEC;;;;;;AAGP;IA5Ca;KAAA"}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/cart-bubble/index.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useCartBubble } from \"contexts/cart-bubble-context\"\r\nimport { AnimatePresence, motion } from \"framer-motion\"\r\nimport React from \"react\"\r\n\r\nexport const CartBubble: React.FC = () => {\r\n  const { animations } = useCartBubble()\r\n\r\n  return (\r\n    <div className=\"pointer-events-none fixed inset-0 z-[9999]\">\r\n      <AnimatePresence>\r\n        {animations.map((animation) => (\r\n          <BubbleItem key={animation.id} animation={animation} />\r\n        ))}\r\n      </AnimatePresence>\r\n    </div>\r\n  )\r\n}\r\n\r\ninterface BubbleItemProps {\r\n  animation: {\r\n    id: string\r\n    startX: number\r\n    startY: number\r\n    endX: number\r\n    endY: number\r\n    productImage?: string\r\n    productTitle?: string\r\n  }\r\n}\r\n\r\nconst BubbleItem: React.FC<BubbleItemProps> = ({ animation }) => {\r\n  const { startX, startY, endX, endY, productImage } = animation\r\n\r\n  return (\r\n    <motion.div\r\n      className=\"absolute flex items-center justify-center\"\r\n      initial={{\r\n        x: startX - 20, // Offset để center\r\n        y: startY - 20,\r\n        scale: 1,\r\n        opacity: 1,\r\n      }}\r\n      animate={{\r\n        x: endX - 20,\r\n        y: endY - 20,\r\n        scale: 0.5,\r\n        opacity: 0,\r\n      }}\r\n      exit={{\r\n        opacity: 0,\r\n        scale: 0,\r\n      }}\r\n      transition={{\r\n        duration: 1.2,\r\n        ease: \"easeOut\",\r\n      }}\r\n    >\r\n      {/* Simple bubble */}\r\n      <div className=\"shadow-lg flex h-10 w-10 items-center justify-center rounded-full bg-blue-500 font-bold text-white\">\r\n        {productImage ? (\r\n          <img\r\n            src={productImage}\r\n            alt=\"Product\"\r\n            className=\"h-8 w-8 rounded-full object-cover\"\r\n          />\r\n        ) : (\r\n          \"+1\"\r\n        )}\r\n      </div>\r\n    </motion.div>\r\n  )\r\n}\r\n\r\nexport default CartBubble\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;;;AAHA;;;AAMO,MAAM,aAAuB;;IAClC,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,gBAAa,AAAD;IAEnC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;sBACb,WAAW,GAAG,CAAC,CAAC,0BACf,6LAAC;oBAA8B,WAAW;mBAAzB,UAAU,EAAE;;;;;;;;;;;;;;;AAKvC;GAZa;;QACY,gJAAA,CAAA,gBAAa;;;KADzB;AA0Bb,MAAM,aAAwC,CAAC,EAAE,SAAS,EAAE;IAC1D,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG;IAErD,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YACP,GAAG,SAAS;YACZ,GAAG,SAAS;YACZ,OAAO;YACP,SAAS;QACX;QACA,SAAS;YACP,GAAG,OAAO;YACV,GAAG,OAAO;YACV,OAAO;YACP,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,OAAO;QACT;QACA,YAAY;YACV,UAAU;YACV,MAAM;QACR;kBAGA,cAAA,6LAAC;YAAI,WAAU;sBACZ,6BACC,6LAAC;gBACC,KAAK;gBACL,KAAI;gBACJ,WAAU;;;;;uBAGZ;;;;;;;;;;;AAKV;MAzCM;uCA2CS"}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 406, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/utils/index.ts"], "sourcesContent": ["import { twMerge } from \"tailwind-merge\"\r\nimport { clsx } from \"clsx\"\r\nimport { ClassValue } from \"class-variance-authority/dist/types\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\n\r\nexport const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));\r\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAIO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,QAAQ,CAAC,KAAe,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS"}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"utils\"\r\n\r\nconst TooltipProvider = TooltipPrimitive.Provider\r\n\r\nconst Tooltip = TooltipPrimitive.Root\r\n\r\nconst TooltipTrigger = TooltipPrimitive.Trigger\r\n\r\nconst TooltipContent = React.forwardRef<\r\n  React.ElementRef<typeof TooltipPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <TooltipPrimitive.Portal>\r\n    <TooltipPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 overflow-hidden rounded-md bg-primary-main text-white px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </TooltipPrimitive.Portal>\r\n))\r\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,kBAAkB,uKAAiB,QAAQ;AAEjD,MAAM,UAAU,uKAAiB,IAAI;AAErC,MAAM,iBAAiB,uKAAiB,OAAO;AAE/C,MAAM,+BAAiB,8JAAM,UAAU,MAGrC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,uKAAiB,MAAM;kBACtB,cAAA,6LAAC,uKAAiB,OAAO;YACvB,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,qYACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,uKAAiB,OAAO,CAAC,WAAW"}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}