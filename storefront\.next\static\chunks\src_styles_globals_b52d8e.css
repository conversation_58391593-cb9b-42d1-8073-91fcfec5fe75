/* [project]/src/styles/globals.css [app-client] (css) */
*, :before, :after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #3b82f680;
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
  --tw-contain-size: ;
  --tw-contain-layout: ;
  --tw-contain-paint: ;
  --tw-contain-style: ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #3b82f680;
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
  --tw-contain-size: ;
  --tw-contain-layout: ;
  --tw-contain-paint: ;
  --tw-contain-style: ;
}

*, :before, :after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #ebeff4;
}

:before, :after {
  --tw-content: "";
}

html, :host {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  font-family: var(--font-sans), ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-feature-settings: normal;
  font-variation-settings: normal;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  line-height: inherit;
}

hr {
  height: 0;
  color: inherit;
  border-top-width: 1px;
}

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}

h1, h2, h3, h4, h5, h6 {
  font-size: inherit;
  font-weight: inherit;
}

a {
  color: inherit;
  text-decoration: inherit;
}

b, strong {
  font-weight: bolder;
}

code, kbd, samp, pre {
  font-family: Roboto Mono, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
  font-feature-settings: normal;
  font-variation-settings: normal;
  font-size: 1em;
}

small {
  font-size: 80%;
}

sub, sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -.25em;
}

sup {
  top: -.5em;
}

table {
  text-indent: 0;
  border-color: inherit;
  border-collapse: collapse;
}

button, input, optgroup, select, textarea {
  font-family: inherit;
  font-feature-settings: inherit;
  font-variation-settings: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  letter-spacing: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
}

button, select {
  text-transform: none;
}

button, input:where([type="button"]), input:where([type="reset"]), input:where([type="submit"]) {
  -webkit-appearance: button;
  background-color: #0000;
  background-image: none;
}

:-moz-focusring {
  outline: auto;
}

:-moz-ui-invalid {
  box-shadow: none;
}

progress {
  vertical-align: baseline;
}

::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
  height: auto;
}

[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

summary {
  display: list-item;
}

blockquote, dl, dd, h1, h2, h3, h4, h5, h6, hr, figure, p, pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol, ul, menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

dialog {
  padding: 0;
}

textarea {
  resize: vertical;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  color: #c1cdd7;
}

input::placeholder, textarea::placeholder {
  opacity: 1;
  color: #c1cdd7;
}

button, [role="button"] {
  cursor: pointer;
}

:disabled {
  cursor: default;
}

img, svg, video, canvas, audio, iframe, embed, object {
  display: block;
  vertical-align: middle;
}

img, video {
  max-width: 100%;
  height: auto;
}

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

* {
  border-color: var(--border-base);
}

:root {
  --tag-neutral-border: #e4e4e7;
  --tag-neutral-icon: #a1a1aa;
  --bg-switch-off-hover: #d4d4d8;
  --border-menu-bot: #fff;
  --border-menu-top: #e4e4e7;
  --bg-subtle-hover: #f4f4f5;
  --contrast-fg-primary: #ffffffe0;
  --bg-switch-off: #e4e4e7;
  --contrast-bg-base-pressed: #3f3f46;
  --bg-field-component-hover: #fafafa;
  --bg-base-pressed: #e4e4e7;
  --tag-neutral-text: #52525b;
  --tag-red-text: #9f1239;
  --contrast-bg-base: #18181b;
  --border-strong: #d4d4d8;
  --contrast-border-base: #ffffff26;
  --bg-field: #fafafa;
  --tag-blue-text: #1e40af;
  --button-inverted-pressed: #52525b;
  --border-interactive: #3b82f6;
  --bg-base-hover: #f4f4f5;
  --contrast-bg-subtle: #27272a;
  --bg-highlight: #eff6ff;
  --contrast-fg-secondary: #ffffff8f;
  --tag-red-bg: #ffe4e6;
  --button-transparent: #fff0;
  --button-danger-pressed: #9f1239;
  --fg-on-color: #fff;
  --button-inverted-hover: #3f3f46;
  --bg-field-component: #fff;
  --tag-orange-text: #9a3412;
  --tag-green-icon: #10b981;
  --border-base: #e4e4e7;
  --bg-base: #fff;
  --tag-orange-border: #fed7aa;
  --tag-red-border: #fecdd3;
  --tag-green-border: #a7f3d0;
  --tag-green-text: #065f46;
  --button-neutral: #fff;
  --tag-blue-border: #bfdbfe;
  --fg-interactive-hover: #2563eb;
  --tag-orange-icon: #f97316;
  --button-neutral-hover: #f4f4f5;
  --fg-interactive: #3b82f6;
  --bg-component-pressed: #e4e4e7;
  --tag-purple-bg: #ede9fe;
  --contrast-bg-base-hover: #27272a;
  --bg-component: #fafafa;
  --bg-subtle: #fafafa;
  --tag-purple-text: #5b21b6;
  --contrast-border-bot: #ffffff1a;
  --button-inverted: #27272a;
  --tag-red-icon: #f43f5e;
  --button-transparent-hover: #f4f4f5;
  --button-neutral-pressed: #e4e4e7;
  --tag-purple-icon: #a78bfa;
  --bg-field-hover: #f4f4f5;
  --fg-on-inverted: #fff;
  --bg-interactive: #3b82f6;
  --border-danger: #be123c;
  --button-transparent-pressed: #e4e4e7;
  --tag-purple-border: #ddd6fe;
  --bg-highlight-hover: #dbeafe;
  --border-error: #e11d48;
  --button-danger: #e11d48;
  --tag-blue-bg: #dbeafe;
  --border-transparent: #fff0;
  --button-danger-hover: #be123c;
  --bg-subtle-pressed: #e4e4e7;
  --fg-error: #e11d48;
  --bg-component-hover: #f4f4f5;
  --bg-disabled: #f4f4f5;
  --tag-blue-icon: #60a5fa;
  --fg-subtle: #52525b;
  --tag-orange-bg-hover: #fed7aa;
  --tag-green-bg-hover: #a7f3d0;
  --tag-red-bg-hover: #fecdd3;
  --tag-purple-bg-hover: #ddd6fe;
  --tag-neutral-bg-hover: #e4e4e7;
  --tag-blue-bg-hover: #bfdbfe;
  --tag-green-bg: #d1fae5;
  --tag-neutral-bg: #f4f4f5;
  --tag-orange-bg: #ffedd5;
  --fg-base: #18181b;
  --contrast-border-top: #18181b;
  --bg-overlay: #18181b66;
  --fg-disabled: #a1a1aa;
  --fg-muted: #71717a;
  --borders-interactive-with-active: 0px 0px 0px 1px #3b82f6, 0px 0px 0px 4px #3b82f633;
  --buttons-danger-focus: 0px .75px 0px 0px #fff3 inset, 0px 1px 2px 0px #be123c66, 0px 0px 0px 1px #be123c, 0px 0px 0px 2px #fff, 0px 0px 0px 4px #3b82f699;
  --details-contrast-on-bg-interactive: 0px 1px 2px 0px #1e3a8a99;
  --borders-interactive-with-focus: 0px 1px 2px 0px #1e3a8a80, 0px 0px 0px 1px #3b82f6, 0px 0px 0px 2px #fff, 0px 0px 0px 4px #3b82f699;
  --borders-error: 0px 0px 0px 1px #e11d48, 0px 0px 0px 3px #e11d4826;
  --borders-focus: 0px 0px 0px 1px #fff, 0px 0px 0px 3px #3b82f699;
  --borders-interactive-with-shadow: 0px 1px 2px 0px #1e3a8a80, 0px 0px 0px 1px #3b82f6;
  --buttons-danger: 0px .75px 0px 0px #fff3 inset, 0px 1px 2px 0px #be123c66, 0px 0px 0px 1px #be123c;
  --buttons-inverted-focus: 0px .75px 0px 0px #fff3 inset, 0px 1px 2px 0px #0006, 0px 0px 0px 1px #18181b, 0px 0px 0px 2px #fff, 0px 0px 0px 4px #3b82f699;
  --elevation-card-hover: 0px 0px 0px 1px #00000014, 0px 1px 2px -1px #00000014, 0px 2px 8px 0px #0000001a;
  --details-switch-handle: 0px 0px 2px 1px #fff inset, 0px 1px 0px 0px #fff inset, 0px 0px 0px .5px #00000005, 0px 5px 4px 0px #00000005, 0px 3px 3px 0px #0000000a, 0px 1px 2px 0px #0000001f, 0px 0px 1px 0px #00000014;
  --buttons-neutral: 0px 1px 2px 0px #0000001f, 0px 0px 0px 1px #00000014;
  --borders-base: 0px 1px 2px 0px #0000001f, 0px 0px 0px 1px #00000014;
  --elevation-card-rest: 0px 0px 0px 1px #00000014, 0px 1px 2px -1px #00000014, 0px 2px 4px 0px #0000000a;
  --buttons-neutral-focus: 0px 1px 2px 0px #0000001f, 0px 0px 0px 1px #00000014, 0px 0px 0px 2px #fff, 0px 0px 0px 4px #3b82f699;
  --details-switch-background-focus: 0px 0px 0px 1px #fff, 0px 0px 0px 3px #3b82f699, 0px 1px 1px 0px #0000000a inset, 0px 2px 4px 0px #0000000a inset, 0px 0px 0px .75px #0000000f inset, 0px 0px 8px 0px #00000005 inset, 0px 2px 4px 0px #0000000a;
  --details-switch-background: 0px 1px 1px 0px #0000000a inset, 0px 2px 4px 0px #0000000a inset, 0px 0px 0px .75px #0000000f inset, 0px 0px 8px 0px #00000005 inset, 0px 2px 4px 0px #0000000a;
  --elevation-flyout: 0px 0px 0px 1px #00000014, 0px 4px 8px 0px #00000014, 0px 8px 16px 0px #00000014;
  --elevation-tooltip: 0px 0px 0px 1px #00000014, 0px 2px 4px 0px #00000014, 0px 4px 8px 0px #00000014;
  --elevation-modal: 0px 0px 0px 1px #fff inset, 0px 0px 0px 1.5px #e4e4e799 inset, 0px 0px 0px 1px #00000014, 0px 8px 16px 0px #00000014, 0px 16px 32px 0px #00000014;
  --elevation-commandbar: 0px 0px 0px 1px #27272a inset, 0px 0px 0px 1.5px #ffffff4d inset, 0px 8px 16px 0px #00000014, 0px 16px 32px 0px #00000014;
  --elevation-code-block: 0px 0px 0px 1px #18181b inset, 0px 0px 0px 1.5px #fff3 inset;
  --buttons-inverted: 0px .75px 0px 0px #fff3 inset, 0px 1px 2px 0px #0006, 0px 0px 0px 1px #18181b;
  --background: 0 0% 100%;
  --foreground: 240 10% 3.9%;
  --muted: 240 4.8% 95.9%;
  --muted-foreground: 240 3.8% 46.1%;
  --popover: 0 0% 100%;
  --popover-foreground: 240 10% 3.9%;
  --border: 240 5.9% 90%;
  --input: 240 5.9% 90%;
  --card: 0 0% 100%;
  --card-foreground: 240 10% 3.9%;
  --primary: 22 34.8% 44.51%;
  --primary-foreground: 0 0% 98%;
  --secondary: 240 4.8% 95.9%;
  --secondary-foreground: 240 5.9% 10%;
  --accent: 240 4.8% 95.9%;
  --accent-foreground: 240 5.9% 10%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --ring: 240 10% 3.9%;
  --radius: .5rem;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
  --font-body: "Inter", sans-serif;
  --font-mulish: "Mulish", sans-serif;
  --font-sans: "Sans", sans-serif;
  --font-display: "Mulish", sans-serif;
  --font-lora: "Lora", serif;
  --font-poppins: "Poppins", sans-serif;
  --font-montserrat: "Montserrat", sans-serif;
  --font-didot: "GFS Didot", serif;
  --font-playfair: "Playfair Display", serif;
  --sidebar-background: 0 0% 98%;
  --sidebar-foreground: 240 5.3% 26.1%;
  --sidebar-primary: 240 5.9% 10%;
  --sidebar-primary-foreground: 0 0% 98%;
  --sidebar-accent: 240 4.8% 95.9%;
  --sidebar-accent-foreground: 240 5.9% 10%;
  --sidebar-border: 220 13% 91%;
  --sidebar-ring: 217.2 91.2% 59.8%;
  --swiper-theme-color: #59b71f !important;
  --swiper-pagination-color: #59b71f !important;
  --borders-interactive-with-active: #59b71f !important;
  --swiper-navigation-size: 12px !important;
}

body {
  font-family: var(--font-body), ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1rem;
  padding-left: 1rem;
}

@media (width >= 768px) {
  .container {
    max-width: 768px;
  }
}

@media (width >= 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (width >= 1440px) {
  .container {
    max-width: 1440px;
  }
}

.txt-compact-xsmall {
  font-size: .75rem;
  line-height: 1.25rem;
  font-weight: 400;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.txt-compact-xsmall-plus {
  font-size: .75rem;
  line-height: 1.25rem;
  font-weight: 500;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.txt-xlarge {
  font-size: 1.125rem;
  line-height: 1.6875rem;
  font-weight: 400;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.txt-compact-small-plus {
  font-size: .8125rem;
  line-height: 1.25rem;
  font-weight: 500;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.txt-compact-medium {
  font-size: .875rem;
  line-height: 1.25rem;
  font-weight: 400;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.txt-compact-large-plus {
  font-size: 1rem;
  line-height: 1.25rem;
  font-weight: 500;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.\!txt-medium {
  font-size: .875rem !important;
  line-height: 1.3125rem !important;
  font-weight: 400 !important;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji !important;
}

.txt-medium {
  font-size: .875rem;
  line-height: 1.3125rem;
  font-weight: 400;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.txt-compact-large {
  font-size: 1rem;
  line-height: 1.25rem;
  font-weight: 400;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.txt-compact-medium-plus {
  font-size: .875rem;
  line-height: 1.25rem;
  font-weight: 500;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.txt-compact-xlarge {
  font-size: 1.125rem;
  line-height: 1.25rem;
  font-weight: 400;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.\!txt-compact-small {
  font-size: .8125rem !important;
  line-height: 1.25rem !important;
  font-weight: 400 !important;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji !important;
}

.txt-compact-small {
  font-size: .8125rem;
  line-height: 1.25rem;
  font-weight: 400;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.txt-xsmall-plus {
  font-size: .75rem;
  line-height: 1.125rem;
  font-weight: 500;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.txt-small {
  font-size: .8125rem;
  line-height: 1.21875rem;
  font-weight: 400;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.txt-small-plus {
  font-size: .8125rem;
  line-height: 1.21875rem;
  font-weight: 500;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.txt-large {
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.txt-medium-plus {
  font-size: .875rem;
  line-height: 1.3125rem;
  font-weight: 500;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.txt-xsmall {
  font-size: .75rem;
  line-height: 1.125rem;
  font-weight: 400;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.txt-xlarge-plus {
  font-size: 1.125rem;
  line-height: 1.6875rem;
  font-weight: 500;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.code-body {
  font-size: .75rem;
  line-height: 1.125rem;
  font-weight: 400;
  font-family: Roboto Mono, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
}

.h2-core {
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 500;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.h3-core {
  font-size: .875rem;
  line-height: 1.25rem;
  font-weight: 500;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.h1-core {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 500;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.container {
  max-width: 100%;
}

@media (width >= 640px) {
  .container {
    max-width: 640px;
  }
}

@media (width >= 768px) {
  .container {
    max-width: 768px;
  }
}

@media (width >= 992px) and (width <= 1024px) {
  .container {
    max-width: 992px;
  }
}

@media (width >= 1024px) and (width <= 1280px) {
  .container {
    max-width: 1024px;
  }
}

@media (width >= 1280px) and (width <= 1440px) {
  .container {
    max-width: 1280px;
  }
}

@media (width >= 1441px) {
  .container {
    max-width: 1440px;
  }
}

.content-container {
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  max-width: 1440px;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.text-small-regular {
  font-size: .75rem;
  line-height: 1.2;
  font-weight: 400;
  line-height: 1.25rem;
}

.text-base-regular {
  font-size: .875rem;
  line-height: 1.2;
  font-weight: 400;
  line-height: 1.5rem;
}

.text-base-semi {
  font-size: .875rem;
  line-height: 1.2;
  font-weight: 600;
  line-height: 1.5rem;
}

.text-large-regular {
  font-size: 1rem;
  line-height: 1.2;
  font-weight: 400;
  line-height: 1.5rem;
}

.text-large-semi {
  font-size: 1rem;
  line-height: 1.2;
  font-weight: 600;
  line-height: 1.5rem;
}

.text-xl-semi {
  font-size: 2rem;
  line-height: 1.2;
  font-weight: 600;
  line-height: 36px;
}

.text-2xl-regular {
  font-size: 30px;
  font-weight: 400;
  line-height: 48px;
}

.text-2xl-semi {
  font-size: 30px;
  font-weight: 600;
  line-height: 48px;
}

.text-3xl-regular {
  font-size: 32px;
  font-weight: 400;
  line-height: 44px;
}

.text-3xl-semi {
  font-size: 32px;
  font-weight: 600;
  line-height: 44px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.pointer-events-none {
  pointer-events: none;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.inset-0 {
  inset: 0;
}

.inset-2 {
  inset: .5rem;
}

.inset-x-0 {
  left: 0;
  right: 0;
}

.inset-x-\[31\.75\%\] {
  left: 31.75%;
  right: 31.75%;
}

.inset-y-0 {
  top: 0;
  bottom: 0;
}

.inset-y-2 {
  top: .5rem;
  bottom: .5rem;
}

.inset-y-\[31\.75\%\] {
  top: 31.75%;
  bottom: 31.75%;
}

.-bottom-6 {
  bottom: -1.5rem;
}

.-bottom-\[calc\(100\%-36px\)\] {
  bottom: calc(-100% + 36px);
}

.-right-1 {
  right: -.25rem;
}

.-right-2 {
  right: -.5rem;
}

.-right-3 {
  right: -.75rem;
}

.-top-1\/2 {
  top: -50%;
}

.-top-2 {
  top: -.5rem;
}

.-top-3 {
  top: -.75rem;
}

.-top-7 {
  top: -1.75rem;
}

.bottom-0 {
  bottom: 0;
}

.bottom-1\/2 {
  bottom: 50%;
}

.bottom-24 {
  bottom: 6rem;
}

.bottom-4 {
  bottom: 1rem;
}

.bottom-6 {
  bottom: 1.5rem;
}

.bottom-8 {
  bottom: 2rem;
}

.bottom-\[3px\] {
  bottom: 3px;
}

.left-0 {
  left: 0;
}

.left-1 {
  left: .25rem;
}

.left-1\/2 {
  left: 50%;
}

.left-2 {
  left: .5rem;
}

.left-3 {
  left: .75rem;
}

.left-4 {
  left: 1rem;
}

.left-\[48\%\] {
  left: 48%;
}

.left-\[50\%\] {
  left: 50%;
}

.left-\[calc\(20px\+24px\+24px\)\] {
  left: 68px;
}

.right-0 {
  right: 0;
}

.right-1 {
  right: .25rem;
}

.right-1\/2 {
  right: 50%;
}

.right-14 {
  right: 3.5rem;
}

.right-2 {
  right: .5rem;
}

.right-3 {
  right: .75rem;
}

.right-4 {
  right: 1rem;
}

.right-5 {
  right: 1.25rem;
}

.right-6 {
  right: 1.5rem;
}

.top-0 {
  top: 0;
}

.top-1\/2 {
  top: 50%;
}

.top-12 {
  top: 3rem;
}

.top-16 {
  top: 4rem;
}

.top-2 {
  top: .5rem;
}

.top-3 {
  top: .75rem;
}

.top-4 {
  top: 1rem;
}

.top-\[1px\] {
  top: 1px;
}

.top-\[48\%\] {
  top: 48%;
}

.top-\[50\%\] {
  top: 50%;
}

.top-\[60\%\] {
  top: 60%;
}

.top-full {
  top: 100%;
}

.isolate {
  isolation: isolate;
}

.z-0 {
  z-index: 0;
}

.z-10 {
  z-index: 10;
}

.z-30 {
  z-index: 30;
}

.z-50 {
  z-index: 50;
}

.z-\[1000\] {
  z-index: 1000;
}

.z-\[1\] {
  z-index: 1;
}

.z-\[75\] {
  z-index: 75;
}

.z-\[900\] {
  z-index: 900;
}

.z-\[9998\] {
  z-index: 9998;
}

.z-\[9999\] {
  z-index: 9999;
}

.col-span-1 {
  grid-column: span 1 / span 1;
}

.col-span-12 {
  grid-column: span 12 / span 12;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.col-span-3 {
  grid-column: span 3 / span 3;
}

.col-span-4 {
  grid-column: span 4 / span 4;
}

.col-span-9 {
  grid-column: span 9 / span 9;
}

.col-span-full {
  grid-column: 1 / -1;
}

.-col-start-1 {
  grid-column-start: -1;
}

.-col-start-10 {
  grid-column-start: -10;
}

.-col-start-11 {
  grid-column-start: -11;
}

.-col-start-12 {
  grid-column-start: -12;
}

.-col-start-13 {
  grid-column-start: -13;
}

.-col-start-2 {
  grid-column-start: -2;
}

.-col-start-3 {
  grid-column-start: -3;
}

.-col-start-4 {
  grid-column-start: -4;
}

.-col-start-5 {
  grid-column-start: -5;
}

.-col-start-6 {
  grid-column-start: -6;
}

.-col-start-7 {
  grid-column-start: -7;
}

.-col-start-8 {
  grid-column-start: -8;
}

.-col-start-9 {
  grid-column-start: -9;
}

.col-start-1 {
  grid-column-start: 1;
}

.col-start-10 {
  grid-column-start: 10;
}

.col-start-11 {
  grid-column-start: 11;
}

.col-start-12 {
  grid-column-start: 12;
}

.col-start-13 {
  grid-column-start: 13;
}

.col-start-2 {
  grid-column-start: 2;
}

.col-start-3 {
  grid-column-start: 3;
}

.col-start-4 {
  grid-column-start: 4;
}

.col-start-5 {
  grid-column-start: 5;
}

.col-start-6 {
  grid-column-start: 6;
}

.col-start-7 {
  grid-column-start: 7;
}

.col-start-8 {
  grid-column-start: 8;
}

.col-start-9 {
  grid-column-start: 9;
}

.-col-end-1 {
  grid-column-end: -1;
}

.-col-end-10 {
  grid-column-end: -10;
}

.-col-end-11 {
  grid-column-end: -11;
}

.-col-end-12 {
  grid-column-end: -12;
}

.-col-end-13 {
  grid-column-end: -13;
}

.-col-end-2 {
  grid-column-end: -2;
}

.-col-end-3 {
  grid-column-end: -3;
}

.-col-end-4 {
  grid-column-end: -4;
}

.-col-end-5 {
  grid-column-end: -5;
}

.-col-end-6 {
  grid-column-end: -6;
}

.-col-end-7 {
  grid-column-end: -7;
}

.-col-end-8 {
  grid-column-end: -8;
}

.-col-end-9 {
  grid-column-end: -9;
}

.col-end-1 {
  grid-column-end: 1;
}

.col-end-10 {
  grid-column-end: 10;
}

.col-end-11 {
  grid-column-end: 11;
}

.col-end-12 {
  grid-column-end: 12;
}

.col-end-13 {
  grid-column-end: 13;
}

.col-end-2 {
  grid-column-end: 2;
}

.col-end-3 {
  grid-column-end: 3;
}

.col-end-4 {
  grid-column-end: 4;
}

.col-end-5 {
  grid-column-end: 5;
}

.col-end-6 {
  grid-column-end: 6;
}

.col-end-7 {
  grid-column-end: 7;
}

.col-end-8 {
  grid-column-end: 8;
}

.col-end-9 {
  grid-column-end: 9;
}

.row-span-2 {
  grid-row: span 2 / span 2;
}

.\!m-0 {
  margin: 0 !important;
}

.m-2 {
  margin: .5rem;
}

.m-4 {
  margin: 1rem;
}

.-mx-1 {
  margin-left: -.25rem;
  margin-right: -.25rem;
}

.mx-1 {
  margin-left: .25rem;
  margin-right: .25rem;
}

.mx-2 {
  margin-left: .5rem;
  margin-right: .5rem;
}

.mx-3 {
  margin-left: .75rem;
  margin-right: .75rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-1 {
  margin-top: .25rem;
  margin-bottom: .25rem;
}

.my-10 {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}

.my-12 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.my-2 {
  margin-top: .5rem;
  margin-bottom: .5rem;
}

.my-3 {
  margin-top: .75rem;
  margin-bottom: .75rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.my-\[5vh\] {
  margin-top: 5vh;
  margin-bottom: 5vh;
}

.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.\!-mt-0 {
  margin-top: 0 !important;
}

.\!mt-0 {
  margin-top: 0 !important;
}

.mb-0 {
  margin-bottom: 0;
}

.mb-1 {
  margin-bottom: .25rem;
}

.mb-10 {
  margin-bottom: 2.5rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-14 {
  margin-bottom: 3.5rem;
}

.mb-16 {
  margin-bottom: 4rem;
}

.mb-2 {
  margin-bottom: .5rem;
}

.mb-20 {
  margin-bottom: 5rem;
}

.mb-3 {
  margin-bottom: .75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.ml-1 {
  margin-left: .25rem;
}

.ml-2 {
  margin-left: .5rem;
}

.ml-3 {
  margin-left: .75rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-auto {
  margin-left: auto;
}

.mr-2 {
  margin-right: .5rem;
}

.mt-0 {
  margin-top: 0;
}

.mt-1 {
  margin-top: .25rem;
}

.mt-1\.5 {
  margin-top: .375rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-12 {
  margin-top: 3rem;
}

.mt-2 {
  margin-top: .5rem;
}

.mt-20 {
  margin-top: 5rem;
}

.mt-24 {
  margin-top: 6rem;
}

.mt-3 {
  margin-top: .75rem;
}

.mt-32 {
  margin-top: 8rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-5 {
  margin-top: 1.25rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mt-\[10vh\] {
  margin-top: 10vh;
}

.mt-\[30vh\] {
  margin-top: 30vh;
}

.mt-auto {
  margin-top: auto;
}

.box-border {
  box-sizing: border-box;
}

.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.line-clamp-4 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.\!flex {
  display: flex !important;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

.aspect-\[1\/1\] {
  aspect-ratio: 1;
}

.aspect-\[11\/14\] {
  aspect-ratio: 11 / 14;
}

.aspect-\[16\/10\] {
  aspect-ratio: 16 / 10;
}

.aspect-\[29\/34\] {
  aspect-ratio: 29 / 34;
}

.aspect-\[3\/4\] {
  aspect-ratio: 3 / 4;
}

.aspect-\[9\/16\] {
  aspect-ratio: 9 / 16;
}

.aspect-square {
  aspect-ratio: 1;
}

.size-1 {
  width: .25rem;
  height: .25rem;
}

.size-11 {
  width: 2.75rem;
  height: 2.75rem;
}

.size-4 {
  width: 1rem;
  height: 1rem;
}

.size-5 {
  width: 1.25rem;
  height: 1.25rem;
}

.size-6 {
  width: 1.5rem;
  height: 1.5rem;
}

.size-7 {
  width: 1.75rem;
  height: 1.75rem;
}

.size-8 {
  width: 2rem;
  height: 2rem;
}

.size-9 {
  width: 2.25rem;
  height: 2.25rem;
}

.size-\[15px\] {
  width: 15px;
  height: 15px;
}

.size-\[3px\] {
  width: 3px;
  height: 3px;
}

.size-full {
  width: 100%;
  height: 100%;
}

.\!h-10 {
  height: 2.5rem !important;
}

.\!h-12 {
  height: 3rem !important;
}

.\!h-5 {
  height: 1.25rem !important;
}

.\!h-8 {
  height: 2rem !important;
}

.\!h-9 {
  height: 2.25rem !important;
}

.\!h-\[90vh\] {
  height: 90vh !important;
}

.\!h-auto {
  height: auto !important;
}

.\!h-fit {
  height: -moz-fit-content !important;
  height: fit-content !important;
}

.h-0\.5 {
  height: .125rem;
}

.h-1 {
  height: .25rem;
}

.h-1\.5 {
  height: .375rem;
}

.h-1\/2 {
  height: 50%;
}

.h-1\/3 {
  height: 33.3333%;
}

.h-10 {
  height: 2.5rem;
}

.h-11 {
  height: 2.75rem;
}

.h-12 {
  height: 3rem;
}

.h-14 {
  height: 3.5rem;
}

.h-16 {
  height: 4rem;
}

.h-18 {
  height: 4.5rem;
}

.h-2 {
  height: .5rem;
}

.h-2\.5 {
  height: .625rem;
}

.h-20 {
  height: 5rem;
}

.h-24 {
  height: 6rem;
}

.h-25 {
  height: 6.25rem;
}

.h-28 {
  height: 7rem;
}

.h-3 {
  height: .75rem;
}

.h-3\.5 {
  height: .875rem;
}

.h-30 {
  height: 7.5rem;
}

.h-32 {
  height: 8rem;
}

.h-4 {
  height: 1rem;
}

.h-44 {
  height: 11rem;
}

.h-48 {
  height: 12rem;
}

.h-5 {
  height: 1.25rem;
}

.h-56 {
  height: 14rem;
}

.h-6 {
  height: 1.5rem;
}

.h-7 {
  height: 1.75rem;
}

.h-72 {
  height: 18rem;
}

.h-8 {
  height: 2rem;
}

.h-80 {
  height: 20rem;
}

.h-9 {
  height: 2.25rem;
}

.h-\[1\.5px\] {
  height: 1.5px;
}

.h-\[12px\] {
  height: 12px;
}

.h-\[14px\] {
  height: 14px;
}

.h-\[15px\] {
  height: 15px;
}

.h-\[16px\] {
  height: 16px;
}

.h-\[18px\] {
  height: 18px;
}

.h-\[1px\] {
  height: 1px;
}

.h-\[2\.2em\] {
  height: 2.2em;
}

.h-\[200px\] {
  height: 200px;
}

.h-\[2px\] {
  height: 2px;
}

.h-\[300px\] {
  height: 300px;
}

.h-\[320px\] {
  height: 320px;
}

.h-\[360px\] {
  height: 360px;
}

.h-\[400px\] {
  height: 400px;
}

.h-\[40px\] {
  height: 40px;
}

.h-\[40vh\] {
  height: 40vh;
}

.h-\[52px\] {
  height: 52px;
}

.h-\[56px\] {
  height: 56px;
}

.h-\[90vh\] {
  height: 90vh;
}

.h-\[calc\(100vh-10rem\)\] {
  height: calc(100vh - 10rem);
}

.h-\[calc\(100vh-1rem\)\] {
  height: calc(100vh - 1rem);
}

.h-\[var\(--radix-navigation-menu-viewport-height\)\] {
  height: var(--radix-navigation-menu-viewport-height);
}

.h-\[var\(--radix-select-trigger-height\)\] {
  height: var(--radix-select-trigger-height);
}

.h-auto {
  height: auto;
}

.h-fit {
  height: -moz-fit-content;
  height: fit-content;
}

.h-full {
  height: 100%;
}

.h-px {
  height: 1px;
}

.h-screen {
  height: 100vh;
}

.h-64 {
  height: 16rem;
}

.h-\[560px\] {
  height: 560px;
}

.max-h-0 {
  max-height: 0;
}

.max-h-48 {
  max-height: 12rem;
}

.max-h-60 {
  max-height: 15rem;
}

.max-h-96 {
  max-height: 24rem;
}

.max-h-\[1000px\] {
  max-height: 1000px;
}

.max-h-\[200px\] {
  max-height: 200px;
}

.max-h-\[300px\] {
  max-height: 300px;
}

.max-h-\[400px\] {
  max-height: 400px;
}

.max-h-\[442px\] {
  max-height: 442px;
}

.max-h-\[500px\] {
  max-height: 500px;
}

.max-h-\[534px\] {
  max-height: 534px;
}

.max-h-\[70px\] {
  max-height: 70px;
}

.max-h-\[75vh\] {
  max-height: 75vh;
}

.max-h-\[90vh\] {
  max-height: 90vh;
}

.max-h-\[var\(--radix-popper-available-height\)\] {
  max-height: var(--radix-popper-available-height);
}

.max-h-full {
  max-height: 100%;
}

.\!min-h-5 {
  min-height: 1.25rem !important;
}

.min-h-0 {
  min-height: 0;
}

.min-h-10 {
  min-height: 2.5rem;
}

.min-h-40 {
  min-height: 10rem;
}

.min-h-\[120px\] {
  min-height: 120px;
}

.min-h-\[180px\] {
  min-height: 180px;
}

.min-h-\[200px\] {
  min-height: 200px;
}

.min-h-\[250px\] {
  min-height: 250px;
}

.min-h-\[25px\] {
  min-height: 25px;
}

.min-h-\[32px\] {
  min-height: 32px;
}

.min-h-\[400px\] {
  min-height: 400px;
}

.min-h-\[50px\] {
  min-height: 50px;
}

.min-h-\[50vh\] {
  min-height: 50vh;
}

.min-h-\[55vh\] {
  min-height: 55vh;
}

.min-h-\[60dvh\] {
  min-height: 60dvh;
}

.min-h-\[60px\] {
  min-height: 60px;
}

.min-h-\[80px\] {
  min-height: 80px;
}

.min-h-\[calc\(100vh-100px\)\] {
  min-height: calc(100vh - 100px);
}

.min-h-\[calc\(100vh-64px\)\] {
  min-height: calc(100vh - 64px);
}

.min-h-full {
  min-height: 100%;
}

.min-h-screen {
  min-height: 100vh;
}

.\!w-5 {
  width: 1.25rem !important;
}

.w-1 {
  width: .25rem;
}

.w-1\.5 {
  width: .375rem;
}

.w-1\/2 {
  width: 50%;
}

.w-1\/3 {
  width: 33.3333%;
}

.w-1\/4 {
  width: 25%;
}

.w-1\/6 {
  width: 16.6667%;
}

.w-10 {
  width: 2.5rem;
}

.w-11 {
  width: 2.75rem;
}

.w-12 {
  width: 3rem;
}

.w-14 {
  width: 3.5rem;
}

.w-16 {
  width: 4rem;
}

.w-18 {
  width: 4.5rem;
}

.w-2 {
  width: .5rem;
}

.w-2\.5 {
  width: .625rem;
}

.w-2\/3 {
  width: 66.6667%;
}

.w-2\/5 {
  width: 40%;
}

.w-2\/6 {
  width: 33.3333%;
}

.w-20 {
  width: 5rem;
}

.w-24 {
  width: 6rem;
}

.w-25 {
  width: 6.25rem;
}

.w-28 {
  width: 7rem;
}

.w-3 {
  width: .75rem;
}

.w-3\.5 {
  width: .875rem;
}

.w-3\/4 {
  width: 75%;
}

.w-3\/5 {
  width: 60%;
}

.w-3\/6 {
  width: 50%;
}

.w-32 {
  width: 8rem;
}

.w-4 {
  width: 1rem;
}

.w-40 {
  width: 10rem;
}

.w-44 {
  width: 11rem;
}

.w-48 {
  width: 12rem;
}

.w-5 {
  width: 1.25rem;
}

.w-6 {
  width: 1.5rem;
}

.w-60 {
  width: 15rem;
}

.w-64 {
  width: 16rem;
}

.w-7 {
  width: 1.75rem;
}

.w-72 {
  width: 18rem;
}

.w-8 {
  width: 2rem;
}

.w-9 {
  width: 2.25rem;
}

.w-96 {
  width: 24rem;
}

.w-\[1\.5px\] {
  width: 1.5px;
}

.w-\[100px\] {
  width: 100px;
}

.w-\[120px\] {
  width: 120px;
}

.w-\[128px\] {
  width: 128px;
}

.w-\[12px\] {
  width: 12px;
}

.w-\[138px\] {
  width: 138px;
}

.w-\[140px\] {
  width: 140px;
}

.w-\[14px\] {
  width: 14px;
}

.w-\[15px\] {
  width: 15px;
}

.w-\[180px\] {
  width: 180px;
}

.w-\[28px\] {
  width: 28px;
}

.w-\[290px\] {
  width: 290px;
}

.w-\[2px\] {
  width: 2px;
}

.w-\[32px\] {
  width: 32px;
}

.w-\[40\%\] {
  width: 40%;
}

.w-\[408px\] {
  width: 408px;
}

.w-\[440px\] {
  width: 440px;
}

.w-\[50\%\] {
  width: 50%;
}

.w-\[60\%\] {
  width: 60%;
}

.w-\[66px\] {
  width: 66px;
}

.w-\[90vw\] {
  width: 90vw;
}

.w-\[95vw\] {
  width: 95vw;
}

.w-\[calc\(100\%-2rem\)\] {
  width: calc(100% - 2rem);
}

.w-\[calc\(20px\+24px\+24px\)\] {
  width: 68px;
}

.w-\[calc\(28px\+24px\+4px\)\] {
  width: 56px;
}

.w-auto {
  width: auto;
}

.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}

.w-full {
  width: 100%;
}

.w-max {
  width: -moz-max-content;
  width: max-content;
}

.w-px {
  width: 1px;
}

.w-screen {
  width: 100vw;
}

.\!min-w-5 {
  min-width: 1.25rem !important;
}

.min-w-0 {
  min-width: 0;
}

.min-w-20 {
  min-width: 5rem;
}

.min-w-\[20px\] {
  min-width: 20px;
}

.min-w-\[210px\] {
  min-width: 210px;
}

.min-w-\[220px\] {
  min-width: 220px;
}

.min-w-\[300px\] {
  min-width: 300px;
}

.min-w-\[320px\] {
  min-width: 320px;
}

.min-w-\[32px\] {
  min-width: 32px;
}

.min-w-\[360px\] {
  min-width: 360px;
}

.min-w-\[40px\] {
  min-width: 40px;
}

.min-w-\[8rem\] {
  min-width: 8rem;
}

.min-w-\[calc\(20px\+24px\+24px\)\] {
  min-width: 68px;
}

.min-w-\[calc\(28px\+24px\+4px\)\] {
  min-width: 56px;
}

.min-w-\[var\(--radix-select-trigger-width\)\] {
  min-width: var(--radix-select-trigger-width);
}

.\!max-w-\[700px\] {
  max-width: 700px !important;
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-42 {
  max-width: 10.5rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-6xl {
  max-width: 72rem;
}

.max-w-\[1200px\] {
  max-width: 1200px;
}

.max-w-\[1280px\] {
  max-width: 1280px;
}

.max-w-\[200px\] {
  max-width: 200px;
}

.max-w-\[280px\] {
  max-width: 280px;
}

.max-w-\[400px\] {
  max-width: 400px;
}

.max-w-\[440px\] {
  max-width: 440px;
}

.max-w-\[767px\] {
  max-width: 767px;
}

.max-w-\[90\%\] {
  max-width: 90%;
}

.max-w-\[900px\] {
  max-width: 900px;
}

.max-w-\[calc\(20px\+24px\+24px\)\] {
  max-width: 68px;
}

.max-w-\[calc\(28px\+24px\+4px\)\] {
  max-width: 56px;
}

.max-w-full {
  max-width: 100%;
}

.max-w-lg {
  max-width: 32rem;
}

.max-w-max {
  max-width: -moz-max-content;
  max-width: max-content;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-screen-lg {
  max-width: 1024px;
}

.max-w-screen-md {
  max-width: 768px;
}

.max-w-sm {
  max-width: 24rem;
}

.max-w-xl {
  max-width: 36rem;
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.shrink-0 {
  flex-shrink: 0;
}

.flex-grow {
  flex-grow: 1;
}

.grow {
  flex-grow: 1;
}

.basis-0 {
  flex-basis: 0;
}

.caption-bottom {
  caption-side: bottom;
}

.border-collapse {
  border-collapse: collapse;
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1 {
  --tw-translate-y: -.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[-50\%\] {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-1\/2 {
  --tw-translate-y: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-4 {
  --tw-translate-y: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[-50\%\] {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-90 {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\!transform-none {
  transform: none !important;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(.8, 0, 1, 1);
  }

  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, .2, 1);
  }
}

.animate-bounce {
  animation: 1s infinite bounce;
}

@keyframes marquee {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(calc(-100% - var(--gap)));
  }
}

.animate-marquee {
  animation: marquee var(--duration) linear infinite;
}

@keyframes marquee-vertical {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(calc(-100% - var(--gap)));
  }
}

.animate-marquee-vertical {
  animation: marquee-vertical var(--duration) linear infinite;
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

.animate-pulse {
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite pulse;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: 1s linear infinite spin;
}

.cursor-default {
  cursor: default;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-text {
  cursor: text;
}

.cursor-zoom-in {
  cursor: zoom-in;
}

.touch-none {
  touch-action: none;
}

.touch-manipulation {
  touch-action: manipulation;
}

.select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.resize {
  resize: both;
}

.scroll-m-20 {
  scroll-margin: 5rem;
}

.list-none {
  list-style-type: none;
}

.appearance-none {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}

.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}

.grid-cols-8 {
  grid-template-columns: repeat(8, minmax(0, 1fr));
}

.grid-cols-9 {
  grid-template-columns: repeat(9, minmax(0, 1fr));
}

.grid-cols-\[122px_1fr\] {
  grid-template-columns: 122px 1fr;
}

.grid-cols-\[144px_1fr\] {
  grid-template-columns: 144px 1fr;
}

.grid-cols-\[15px_1fr\] {
  grid-template-columns: 15px 1fr;
}

.grid-cols-\[1fr_80px\] {
  grid-template-columns: 1fr 80px;
}

.grid-cols-\[20px_1fr\] {
  grid-template-columns: 20px 1fr;
}

.grid-cols-\[20px_1fr_20px\] {
  grid-template-columns: 20px 1fr 20px;
}

.grid-cols-\[28px_1fr\] {
  grid-template-columns: 28px 1fr;
}

.grid-cols-\[28px_1fr_28px\] {
  grid-template-columns: 28px 1fr 28px;
}

.grid-cols-\[32px_1fr\] {
  grid-template-columns: 32px 1fr;
}

.grid-cols-\[32px_1fr_32px\] {
  grid-template-columns: 32px 1fr 32px;
}

.grid-cols-\[40\%_1fr\] {
  grid-template-columns: 40% 1fr;
}

.grid-cols-\[4px_1fr\] {
  grid-template-columns: 4px 1fr;
}

.grid-cols-\[auto\,1fr\] {
  grid-template-columns: auto 1fr;
}

.grid-rows-2 {
  grid-template-rows: repeat(2, minmax(0, 1fr));
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.items-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.gap-0 {
  gap: 0;
}

.gap-0\.5 {
  gap: .125rem;
}

.gap-1 {
  gap: .25rem;
}

.gap-1\.5 {
  gap: .375rem;
}

.gap-10 {
  gap: 2.5rem;
}

.gap-12 {
  gap: 3rem;
}

.gap-14 {
  gap: 3.5rem;
}

.gap-2 {
  gap: .5rem;
}

.gap-3 {
  gap: .75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-5 {
  gap: 1.25rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.gap-x-0\.5 {
  -moz-column-gap: .125rem;
  column-gap: .125rem;
}

.gap-x-1 {
  -moz-column-gap: .25rem;
  column-gap: .25rem;
}

.gap-x-1\.5 {
  -moz-column-gap: .375rem;
  column-gap: .375rem;
}

.gap-x-10 {
  -moz-column-gap: 2.5rem;
  column-gap: 2.5rem;
}

.gap-x-16 {
  -moz-column-gap: 4rem;
  column-gap: 4rem;
}

.gap-x-2 {
  -moz-column-gap: .5rem;
  column-gap: .5rem;
}

.gap-x-3 {
  -moz-column-gap: .75rem;
  column-gap: .75rem;
}

.gap-x-4 {
  -moz-column-gap: 1rem;
  column-gap: 1rem;
}

.gap-x-40 {
  -moz-column-gap: 10rem;
  column-gap: 10rem;
}

.gap-x-5 {
  -moz-column-gap: 1.25rem;
  column-gap: 1.25rem;
}

.gap-x-6 {
  -moz-column-gap: 1.5rem;
  column-gap: 1.5rem;
}

.gap-x-8 {
  -moz-column-gap: 2rem;
  column-gap: 2rem;
}

.gap-y-0\.5 {
  row-gap: .125rem;
}

.gap-y-1 {
  row-gap: .25rem;
}

.gap-y-10 {
  row-gap: 2.5rem;
}

.gap-y-16 {
  row-gap: 4rem;
}

.gap-y-2 {
  row-gap: .5rem;
}

.gap-y-3 {
  row-gap: .75rem;
}

.gap-y-4 {
  row-gap: 1rem;
}

.gap-y-6 {
  row-gap: 1.5rem;
}

.gap-y-8 {
  row-gap: 2rem;
}

.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(.25rem * var(--tw-space-x-reverse));
  margin-left: calc(.25rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-10 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2.5rem * var(--tw-space-x-reverse));
  margin-left: calc(2.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(.5rem * var(--tw-space-x-reverse));
  margin-left: calc(.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(.75rem * var(--tw-space-x-reverse));
  margin-left: calc(.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.25rem * var(--tw-space-y-reverse));
}

.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.375rem * var(--tw-space-y-reverse));
}

.space-y-10 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2.5rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.5rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.75rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

.divide-x > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-x-reverse: 0;
  border-right-width: calc(1px * var(--tw-divide-x-reverse));
  border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}

.self-start {
  align-self: flex-start;
}

.self-end {
  align-self: flex-end;
}

.self-stretch {
  align-self: stretch;
}

.justify-self-end {
  justify-self: end;
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-visible {
  overflow: visible;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.overflow-y-hidden {
  overflow-y: hidden;
}

.overflow-x-scroll {
  overflow-x: scroll;
}

.overflow-y-scroll {
  overflow-y: scroll;
}

.overscroll-none {
  overscroll-behavior: none;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis {
  text-overflow: ellipsis;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.whitespace-pre-line {
  white-space: pre-line;
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}

.text-nowrap {
  text-wrap: nowrap;
}

.text-pretty {
  text-wrap: pretty;
}

.break-words {
  overflow-wrap: break-word;
}

.rounded-\[10px\] {
  border-radius: 10px;
}

.rounded-\[16px\] {
  border-radius: 16px;
}

.rounded-\[33px\] {
  border-radius: 33px;
}

.rounded-\[3px\] {
  border-radius: 3px;
}

.rounded-\[49px\] {
  border-radius: 49px;
}

.rounded-\[4px\] {
  border-radius: 4px;
}

.rounded-\[55px\] {
  border-radius: 55px;
}

.rounded-\[66px\] {
  border-radius: 66px;
}

.rounded-\[inherit\] {
  border-radius: inherit;
}

.rounded-circle {
  border-radius: 9999px;
}

.rounded-full {
  border-radius: 100%;
}

.rounded-large {
  border-radius: 16px;
}

.rounded-lg {
  border-radius: var(--radius);
}

.rounded-md {
  border-radius: calc(var(--radius)  - 2px);
}

.rounded-none {
  border-radius: 0;
}

.rounded-rounded {
  border-radius: 8px;
}

.rounded-sm {
  border-radius: calc(var(--radius)  - 4px);
}

.rounded-xl {
  border-radius: 50px;
}

.rounded-xs {
  border-radius: 4px;
}

.\!rounded-r-none {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.rounded-b-lg {
  border-bottom-right-radius: var(--radius);
  border-bottom-left-radius: var(--radius);
}

.rounded-l-lg {
  border-top-left-radius: var(--radius);
  border-bottom-left-radius: var(--radius);
}

.rounded-l-none {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.rounded-r-none {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.rounded-r-sm {
  border-top-right-radius: calc(var(--radius)  - 4px);
  border-bottom-right-radius: calc(var(--radius)  - 4px);
}

.rounded-t-\[10px\] {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.rounded-t-md {
  border-top-left-radius: calc(var(--radius)  - 2px);
  border-top-right-radius: calc(var(--radius)  - 2px);
}

.rounded-tl-sm {
  border-top-left-radius: calc(var(--radius)  - 4px);
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0;
}

.border-2 {
  border-width: 2px;
}

.border-\[5px\] {
  border-width: 5px;
}

.border-y {
  border-top-width: 1px;
  border-bottom-width: 1px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-0 {
  border-bottom-width: 0;
}

.border-l {
  border-left-width: 1px;
}

.border-l-2 {
  border-left-width: 2px;
}

.border-r {
  border-right-width: 1px;
}

.border-t {
  border-top-width: 1px;
}

.border-t-0 {
  border-top-width: 0;
}

.border-solid {
  border-style: solid;
}

.border-dashed {
  border-style: dashed;
}

.\!border-none {
  border-style: none !important;
}

.border-none {
  border-style: none;
}

.border-\[\#000\] {
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}

.border-\[\#EAECF0\] {
  --tw-border-opacity: 1;
  border-color: rgb(234 236 240 / var(--tw-border-opacity, 1));
}

.border-black\/30 {
  border-color: #0000004d;
}

.border-current {
  border-color: currentColor;
}

.border-error-light {
  --tw-border-opacity: 1;
  border-color: rgb(254 205 202 / var(--tw-border-opacity, 1));
}

.border-error-main {
  --tw-border-opacity: 1;
  border-color: rgb(217 45 32 / var(--tw-border-opacity, 1));
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(235 239 244 / var(--tw-border-opacity, 1));
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(216 224 233 / var(--tw-border-opacity, 1));
}

.border-gray-400 {
  --tw-border-opacity: 1;
  border-color: rgb(193 205 215 / var(--tw-border-opacity, 1));
}

.border-gray-450 {
  --tw-border-opacity: 1;
  border-color: rgb(174 180 190 / var(--tw-border-opacity, 1));
}

.border-gray-600 {
  --tw-border-opacity: 1;
  border-color: rgb(125 135 156 / var(--tw-border-opacity, 1));
}

.border-gray-900 {
  --tw-border-opacity: 1;
  border-color: rgb(43 52 69 / var(--tw-border-opacity, 1));
}

.border-green-200 {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}

.border-input {
  border-color: hsl(var(--input));
}

.border-orange-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));
}

.border-primary-light {
  --tw-border-opacity: 1;
  border-color: rgb(119 184 79 / var(--tw-border-opacity, 1));
}

.border-primary-main {
  --tw-border-opacity: 1;
  border-color: rgb(89 183 31 / var(--tw-border-opacity, 1));
}

.border-primary-main\/50 {
  border-color: #59b71f80;
}

.border-transparent {
  border-color: #0000;
}

.border-ui-border-base {
  border-color: var(--border-base);
}

.border-ui-border-interactive {
  border-color: var(--border-interactive);
}

.border-ui-contrast-border-base {
  border-color: var(--contrast-border-base);
}

.border-ui-contrast-border-bot {
  border-color: var(--contrast-border-bot);
}

.border-ui-tag-blue-border {
  border-color: var(--tag-blue-border);
}

.border-ui-tag-green-border {
  border-color: var(--tag-green-border);
}

.border-ui-tag-neutral-border {
  border-color: var(--tag-neutral-border);
}

.border-ui-tag-orange-border {
  border-color: var(--tag-orange-border);
}

.border-ui-tag-purple-border {
  border-color: var(--tag-purple-border);
}

.border-ui-tag-red-border {
  border-color: var(--tag-red-border);
}

.border-b-transparent {
  border-bottom-color: #0000;
}

.border-b-ui-border-menu-bot {
  border-bottom-color: var(--border-menu-bot);
}

.border-l-transparent {
  border-left-color: #0000;
}

.border-r-ui-border-base {
  border-right-color: var(--border-base);
}

.border-t-transparent {
  border-top-color: #0000;
}

.border-t-ui-border-menu-top {
  border-top-color: var(--border-menu-top);
}

.\!bg-ui-bg-disabled {
  background-color: var(--bg-disabled) !important;
}

.\!bg-ui-bg-interactive {
  background-color: var(--bg-interactive) !important;
}

.bg-\[\#CBAB7C\] {
  --tw-bg-opacity: 1;
  background-color: rgb(203 171 124 / var(--tw-bg-opacity, 1));
}

.bg-\[\#F0F4F8\] {
  --tw-bg-opacity: 1;
  background-color: rgb(240 244 248 / var(--tw-bg-opacity, 1));
}

.bg-\[\#eef1cf\] {
  --tw-bg-opacity: 1;
  background-color: rgb(238 241 207 / var(--tw-bg-opacity, 1));
}

.bg-\[\#f8f8f8\] {
  --tw-bg-opacity: 1;
  background-color: rgb(248 248 248 / var(--tw-bg-opacity, 1));
}

.bg-\[\#fafafa\] {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
}

.bg-\[rgba\(3\,7\,18\,0\.5\)\] {
  background-color: #03071280;
}

.bg-accent {
  background-color: hsl(var(--accent));
}

.bg-background {
  background-color: hsl(var(--background));
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}

.bg-black\/70 {
  background-color: #000000b3;
}

.bg-black\/80 {
  background-color: #000c;
}

.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.bg-border {
  background-color: hsl(var(--border));
}

.bg-card {
  background-color: hsl(var(--card));
}

.bg-destructive {
  background-color: hsl(var(--destructive));
}

.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 246 248 / var(--tw-bg-opacity, 1));
}

.bg-gray-100\/50 {
  background-color: #f4f6f880;
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(235 239 244 / var(--tw-bg-opacity, 1));
}

.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(216 224 233 / var(--tw-bg-opacity, 1));
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.bg-gray-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(75 86 107 / var(--tw-bg-opacity, 1));
}

.bg-gray-750 {
  --tw-bg-opacity: 1;
  background-color: rgb(67 77 86 / var(--tw-bg-opacity, 1));
}

.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(33 43 54 / var(--tw-bg-opacity, 1));
}

.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.bg-muted {
  background-color: hsl(var(--muted));
}

.bg-muted\/50 {
  background-color: hsl(var(--muted) / .5);
}

.bg-neutral-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
}

.bg-orange-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 186 116 / var(--tw-bg-opacity, 1));
}

.bg-orange-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}

.bg-popover {
  background-color: hsl(var(--popover));
}

.bg-primary {
  background-color: hsl(var(--primary));
}

.bg-primary-extraLight {
  --tw-bg-opacity: 1;
  background-color: rgb(226 242 226 / var(--tw-bg-opacity, 1));
}

.bg-primary-light {
  --tw-bg-opacity: 1;
  background-color: rgb(119 184 79 / var(--tw-bg-opacity, 1));
}

.bg-primary-light\/10 {
  background-color: #77b84f1a;
}

.bg-primary-light\/20 {
  background-color: #77b84f33;
}

.bg-primary-light\/30 {
  background-color: #77b84f4d;
}

.bg-primary-lighter {
  --tw-bg-opacity: 1;
  background-color: rgb(238 251 230 / var(--tw-bg-opacity, 1));
}

.bg-primary-main {
  --tw-bg-opacity: 1;
  background-color: rgb(89 183 31 / var(--tw-bg-opacity, 1));
}

.bg-primary-main\/10 {
  background-color: #59b71f1a;
}

.bg-primary\/20 {
  background-color: hsl(var(--primary) / .2);
}

.bg-red {
  --tw-bg-opacity: 1;
  background-color: rgb(210 63 87 / var(--tw-bg-opacity, 1));
}

.bg-secondary {
  background-color: hsl(var(--secondary));
}

.bg-transparent {
  background-color: #0000;
}

.bg-ui-bg-base {
  background-color: var(--bg-base);
}

.bg-ui-bg-component {
  background-color: var(--bg-component);
}

.bg-ui-bg-component-hover {
  background-color: var(--bg-component-hover);
}

.bg-ui-bg-disabled {
  background-color: var(--bg-disabled);
}

.bg-ui-bg-field {
  background-color: var(--bg-field);
}

.bg-ui-bg-interactive {
  background-color: var(--bg-interactive);
}

.bg-ui-bg-overlay {
  background-color: var(--bg-overlay);
}

.bg-ui-bg-subtle {
  background-color: var(--bg-subtle);
}

.bg-ui-bg-subtle-hover {
  background-color: var(--bg-subtle-hover);
}

.bg-ui-bg-switch-off {
  background-color: var(--bg-switch-off);
}

.bg-ui-border-base {
  background-color: var(--border-base);
}

.bg-ui-border-menu-bot {
  background-color: var(--border-menu-bot);
}

.bg-ui-border-menu-top {
  background-color: var(--border-menu-top);
}

.bg-ui-button-danger {
  background-color: var(--button-danger);
}

.bg-ui-button-inverted {
  background-color: var(--button-inverted);
}

.bg-ui-button-neutral {
  background-color: var(--button-neutral);
}

.bg-ui-button-neutral-hover {
  background-color: var(--button-neutral-hover);
}

.bg-ui-button-transparent {
  background-color: var(--button-transparent);
}

.bg-ui-contrast-bg-base {
  background-color: var(--contrast-bg-base);
}

.bg-ui-contrast-bg-subtle {
  background-color: var(--contrast-bg-subtle);
}

.bg-ui-contrast-border-base {
  background-color: var(--contrast-border-base);
}

.bg-ui-contrast-border-top {
  background-color: var(--contrast-border-top);
}

.bg-ui-contrast-fg-primary {
  background-color: var(--contrast-fg-primary);
}

.bg-ui-fg-on-color {
  background-color: var(--fg-on-color);
}

.bg-ui-tag-blue-bg {
  background-color: var(--tag-blue-bg);
}

.bg-ui-tag-green-bg {
  background-color: var(--tag-green-bg);
}

.bg-ui-tag-green-icon {
  background-color: var(--tag-green-icon);
}

.bg-ui-tag-neutral-bg {
  background-color: var(--tag-neutral-bg);
}

.bg-ui-tag-neutral-icon {
  background-color: var(--tag-neutral-icon);
}

.bg-ui-tag-orange-bg {
  background-color: var(--tag-orange-bg);
}

.bg-ui-tag-orange-icon {
  background-color: var(--tag-orange-icon);
}

.bg-ui-tag-purple-bg {
  background-color: var(--tag-purple-bg);
}

.bg-ui-tag-red-bg {
  background-color: var(--tag-red-bg);
}

.bg-ui-tag-red-icon {
  background-color: var(--tag-red-icon);
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-white\/80 {
  background-color: #fffc;
}

.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 248 229 / var(--tw-bg-opacity, 1));
}

.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}

.bg-zinc-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 244 245 / var(--tw-bg-opacity, 1));
}

.bg-zinc-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(228 228 231 / var(--tw-bg-opacity, 1));
}

.bg-zinc-200\/80 {
  background-color: #e4e4e7cc;
}

.bg-zinc-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(212 212 216 / var(--tw-bg-opacity, 1));
}

.bg-zinc-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(161 161 170 / var(--tw-bg-opacity, 1));
}

.bg-black\/20 {
  background-color: #0003;
}

.bg-opacity-30 {
  --tw-bg-opacity: .3;
}

.bg-opacity-50 {
  --tw-bg-opacity: .5;
}

.bg-opacity-75 {
  --tw-bg-opacity: .75;
}

.bg-\[linear-gradient\(0deg\,var\(--border-strong\)_1px\,transparent_1px\)\] {
  background-image: linear-gradient(0deg, var(--border-strong) 1px, transparent 1px);
}

.bg-\[linear-gradient\(90deg\,var\(--border-strong\)_1px\,transparent_1px\)\] {
  background-image: linear-gradient(90deg, var(--border-strong) 1px, transparent 1px);
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.bg-gradient-to-l {
  background-image: linear-gradient(to left, var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}

.from-\[\#f7f8f8\] {
  --tw-gradient-from: #f7f8f8 var(--tw-gradient-from-position);
  --tw-gradient-to: #f7f8f800 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-black {
  --tw-gradient-from: #000 var(--tw-gradient-from-position);
  --tw-gradient-to: #0000 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary-main\/10 {
  --tw-gradient-from: #59b71f1a var(--tw-gradient-from-position);
  --tw-gradient-to: #59b71f00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-primary-main\/20 {
  --tw-gradient-to: #59b71f33 var(--tw-gradient-to-position);
}

.to-transparent {
  --tw-gradient-to: #0000 var(--tw-gradient-to-position);
}

.bg-\[length\:1px_4px\] {
  background-size: 1px 4px;
}

.bg-\[length\:4px_1px\] {
  background-size: 4px 1px;
}

.fill-\[\#9ca3af\] {
  fill: #9ca3af;
}

.fill-current {
  fill: currentColor;
}

.fill-ui-fg-muted {
  fill: var(--fg-muted);
}

.fill-ui-fg-subtle {
  fill: var(--fg-subtle);
}

.object-contain {
  -o-object-fit: contain;
  object-fit: contain;
}

.object-cover {
  -o-object-fit: cover;
  object-fit: cover;
}

.object-center {
  -o-object-position: center;
  object-position: center;
}

.\!p-0 {
  padding: 0 !important;
}

.\!p-4 {
  padding: 1rem !important;
}

.p-0 {
  padding: 0;
}

.p-0\.5 {
  padding: .125rem;
}

.p-1 {
  padding: .25rem;
}

.p-1\.5 {
  padding: .375rem;
}

.p-10 {
  padding: 2.5rem;
}

.p-2 {
  padding: .5rem;
}

.p-2\.5 {
  padding: .625rem;
}

.p-20 {
  padding: 5rem;
}

.p-3 {
  padding: .75rem;
}

.p-3\.5 {
  padding: .875rem;
}

.p-4 {
  padding: 1rem;
}

.p-5 {
  padding: 1.25rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.p-\[1px\] {
  padding: 1px;
}

.p-\[6px\] {
  padding: 6px;
}

.p-px {
  padding: 1px;
}

.\!py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.px-0 {
  padding-left: 0;
  padding-right: 0;
}

.px-0\.5 {
  padding-left: .125rem;
  padding-right: .125rem;
}

.px-1 {
  padding-left: .25rem;
  padding-right: .25rem;
}

.px-1\.5 {
  padding-left: .375rem;
  padding-right: .375rem;
}

.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.px-2 {
  padding-left: .5rem;
  padding-right: .5rem;
}

.px-2\.5 {
  padding-left: .625rem;
  padding-right: .625rem;
}

.px-3 {
  padding-left: .75rem;
  padding-right: .75rem;
}

.px-3\.5 {
  padding-left: .875rem;
  padding-right: .875rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.px-\[5px\] {
  padding-left: 5px;
  padding-right: 5px;
}

.px-\[6px\] {
  padding-left: 6px;
  padding-right: 6px;
}

.py-0 {
  padding-top: 0;
  padding-bottom: 0;
}

.py-1 {
  padding-top: .25rem;
  padding-bottom: .25rem;
}

.py-1\.5 {
  padding-top: .375rem;
  padding-bottom: .375rem;
}

.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.py-2 {
  padding-top: .5rem;
  padding-bottom: .5rem;
}

.py-2\.5 {
  padding-top: .625rem;
  padding-bottom: .625rem;
}

.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.py-3 {
  padding-top: .75rem;
  padding-bottom: .75rem;
}

.py-3\.5 {
  padding-top: .875rem;
  padding-bottom: .875rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-\[10px\] {
  padding-top: 10px;
  padding-bottom: 10px;
}

.py-\[3px\] {
  padding-top: 3px;
  padding-bottom: 3px;
}

.py-\[5px\] {
  padding-top: 5px;
  padding-bottom: 5px;
}

.py-\[7px\] {
  padding-top: 7px;
  padding-bottom: 7px;
}

.py-\[9px\] {
  padding-top: 9px;
  padding-bottom: 9px;
}

.py-px {
  padding-top: 1px;
  padding-bottom: 1px;
}

.\!pb-0 {
  padding-bottom: 0 !important;
}

.\!pb-6 {
  padding-bottom: 1.5rem !important;
}

.\!pl-0 {
  padding-left: 0 !important;
}

.\!pr-0 {
  padding-right: 0 !important;
}

.\!pt-0 {
  padding-top: 0 !important;
}

.pb-0 {
  padding-bottom: 0;
}

.pb-1 {
  padding-bottom: .25rem;
}

.pb-10 {
  padding-bottom: 2.5rem;
}

.pb-2 {
  padding-bottom: .5rem;
}

.pb-3 {
  padding-bottom: .75rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-8 {
  padding-bottom: 2rem;
}

.pb-\[5px\] {
  padding-bottom: 5px;
}

.pb-\[9px\] {
  padding-bottom: 9px;
}

.pl-0 {
  padding-left: 0;
}

.pl-10 {
  padding-left: 2.5rem;
}

.pl-12 {
  padding-left: 3rem;
}

.pl-2 {
  padding-left: .5rem;
}

.pl-2\.5 {
  padding-left: .625rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pl-6 {
  padding-left: 1.5rem;
}

.pl-7 {
  padding-left: 1.75rem;
}

.pl-8 {
  padding-left: 2rem;
}

.pl-\[31px\] {
  padding-left: 31px;
}

.pl-\[88px\] {
  padding-left: 88px;
}

.pr-1 {
  padding-right: .25rem;
}

.pr-10 {
  padding-right: 2.5rem;
}

.pr-12 {
  padding-right: 3rem;
}

.pr-2 {
  padding-right: .5rem;
}

.pr-2\.5 {
  padding-right: .625rem;
}

.pr-3 {
  padding-right: .75rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pr-6 {
  padding-right: 1.5rem;
}

.pr-7 {
  padding-right: 1.75rem;
}

.pr-8 {
  padding-right: 2rem;
}

.pr-\[calc\(15px\+2px\+8px\)\] {
  padding-right: 25px;
}

.pt-0 {
  padding-top: 0;
}

.pt-1 {
  padding-top: .25rem;
}

.pt-10 {
  padding-top: 2.5rem;
}

.pt-2 {
  padding-top: .5rem;
}

.pt-2\.5 {
  padding-top: .625rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-5 {
  padding-top: 1.25rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pt-8 {
  padding-top: 2rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-justify {
  text-align: justify;
}

.text-start {
  text-align: start;
}

.text-end {
  text-align: end;
}

.align-middle {
  vertical-align: middle;
}

.align-bottom {
  vertical-align: bottom;
}

.font-mono {
  font-family: Roboto Mono, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
}

.font-sans {
  font-family: var(--font-sans), ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

.\!text-sm {
  font-size: .875rem !important;
  line-height: 1.2 !important;
}

.text-2xl {
  font-size: 2rem;
  line-height: 1.2;
}

.text-2xs {
  font-size: .625rem;
  line-height: 1.2;
}

.text-3xl {
  font-size: 2.25rem;
  line-height: 1.2;
}

.text-3xs {
  font-size: .5rem;
  line-height: 1.2;
}

.text-4xl {
  font-size: 2.5rem;
  line-height: 1.2;
}

.text-5xl {
  font-size: 3rem;
  line-height: 1.2;
}

.text-6xl {
  font-size: 3.5rem;
  line-height: 1.2;
}

.text-\[0\.8rem\] {
  font-size: .8rem;
}

.text-\[10px\] {
  font-size: 10px;
}

.text-\[13px\] {
  font-size: 13px;
}

.text-base {
  font-size: 1rem;
  line-height: 1.2;
}

.text-base18 {
  font-size: 1.125rem;
  line-height: 1.2;
}

.text-lg {
  font-size: 1.5rem;
  line-height: 1.2;
}

.text-md {
  font-size: 1.25rem;
  line-height: 1.2;
}

.text-sm {
  font-size: .875rem;
  line-height: 1.2;
}

.text-xl {
  font-size: 1.75rem;
  line-height: 1.2;
}

.text-xs {
  font-size: .75rem;
  line-height: 1.2;
}

.font-bold {
  font-weight: 700;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.capitalize {
  text-transform: capitalize;
}

.italic {
  font-style: italic;
}

.tabular-nums {
  --tw-numeric-spacing: tabular-nums;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}

.\!leading-5 {
  line-height: 1.25rem !important;
}

.\!leading-6 {
  line-height: 1.5rem !important;
}

.\!leading-7 {
  line-height: 1.75rem !important;
}

.\!leading-\[22px\] {
  line-height: 22px !important;
}

.\!leading-normal {
  line-height: 1.5 !important;
}

.leading-10 {
  line-height: 2.5rem;
}

.leading-5 {
  line-height: 1.25rem;
}

.leading-6 {
  line-height: 1.5rem;
}

.leading-none {
  line-height: 1;
}

.leading-snug {
  line-height: 1.375;
}

.tracking-tight {
  letter-spacing: -.025em;
}

.tracking-wider {
  letter-spacing: .05em;
}

.tracking-widest {
  letter-spacing: .1em;
}

.\!text-ui-contrast-fg-secondary {
  color: var(--contrast-fg-secondary) !important;
}

.\!text-ui-fg-on-color {
  color: var(--fg-on-color) !important;
}

.text-\[\#064A60\] {
  --tw-text-opacity: 1;
  color: rgb(6 74 96 / var(--tw-text-opacity, 1));
}

.text-\[\#224219\] {
  --tw-text-opacity: 1;
  color: rgb(34 66 25 / var(--tw-text-opacity, 1));
}

.text-\[\#344054\] {
  --tw-text-opacity: 1;
  color: rgb(52 64 84 / var(--tw-text-opacity, 1));
}

.text-\[\#475467\] {
  --tw-text-opacity: 1;
  color: rgb(71 84 103 / var(--tw-text-opacity, 1));
}

.text-\[\#5F6E7C\] {
  --tw-text-opacity: 1;
  color: rgb(95 110 124 / var(--tw-text-opacity, 1));
}

.text-\[\#667085\] {
  --tw-text-opacity: 1;
  color: rgb(102 112 133 / var(--tw-text-opacity, 1));
}

.text-accent {
  color: hsl(var(--accent));
}

.text-accent-foreground {
  color: hsl(var(--accent-foreground));
}

.text-background {
  color: hsl(var(--background));
}

.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.text-card-foreground {
  color: hsl(var(--card-foreground));
}

.text-current {
  color: currentColor;
}

.text-destructive {
  color: hsl(var(--destructive));
}

.text-destructive-foreground {
  color: hsl(var(--destructive-foreground));
}

.text-emerald-500 {
  --tw-text-opacity: 1;
  color: rgb(16 185 129 / var(--tw-text-opacity, 1));
}

.text-error-dark {
  --tw-text-opacity: 1;
  color: rgb(180 35 24 / var(--tw-text-opacity, 1));
}

.text-error-main {
  --tw-text-opacity: 1;
  color: rgb(217 45 32 / var(--tw-text-opacity, 1));
}

.text-foreground {
  color: hsl(var(--foreground));
}

.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(235 239 244 / var(--tw-text-opacity, 1));
}

.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(216 224 233 / var(--tw-text-opacity, 1));
}

.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(193 205 215 / var(--tw-text-opacity, 1));
}

.text-gray-450 {
  --tw-text-opacity: 1;
  color: rgb(174 180 190 / var(--tw-text-opacity, 1));
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(99 115 129 / var(--tw-text-opacity, 1));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(125 135 156 / var(--tw-text-opacity, 1));
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(75 86 107 / var(--tw-text-opacity, 1));
}

.text-gray-750 {
  --tw-text-opacity: 1;
  color: rgb(67 77 86 / var(--tw-text-opacity, 1));
}

.text-gray-760 {
  --tw-text-opacity: 1;
  color: rgb(75 75 75 / var(--tw-text-opacity, 1));
}

.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(33 43 54 / var(--tw-text-opacity, 1));
}

.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(43 52 69 / var(--tw-text-opacity, 1));
}

.text-gray-950 {
  --tw-text-opacity: 1;
  color: rgb(55 63 80 / var(--tw-text-opacity, 1));
}

.text-gray-base {
  --tw-text-opacity: 1;
  color: rgb(102 112 133 / var(--tw-text-opacity, 1));
}

.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.text-green-700 {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}

.text-indigo-600 {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}

.text-muted {
  color: hsl(var(--muted));
}

.text-muted-foreground {
  color: hsl(var(--muted-foreground));
}

.text-neutral-500 {
  --tw-text-opacity: 1;
  color: rgb(115 115 115 / var(--tw-text-opacity, 1));
}

.text-neutral-600 {
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
}

.text-neutral-950 {
  --tw-text-opacity: 1;
  color: rgb(10 10 10 / var(--tw-text-opacity, 1));
}

.text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}

.text-orange-800 {
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}

.text-orange-950 {
  --tw-text-opacity: 1;
  color: rgb(67 20 7 / var(--tw-text-opacity, 1));
}

.text-popover-foreground {
  color: hsl(var(--popover-foreground));
}

.text-primary-dark {
  --tw-text-opacity: 1;
  color: rgb(4 114 33 / var(--tw-text-opacity, 1));
}

.text-primary-foreground {
  color: hsl(var(--primary-foreground));
}

.text-primary-main {
  --tw-text-opacity: 1;
  color: rgb(89 183 31 / var(--tw-text-opacity, 1));
}

.text-red {
  --tw-text-opacity: 1;
  color: rgb(210 63 87 / var(--tw-text-opacity, 1));
}

.text-rose-500 {
  --tw-text-opacity: 1;
  color: rgb(244 63 94 / var(--tw-text-opacity, 1));
}

.text-secondary {
  color: hsl(var(--secondary));
}

.text-secondary-foreground {
  color: hsl(var(--secondary-foreground));
}

.text-success-main {
  --tw-text-opacity: 1;
  color: rgb(7 148 85 / var(--tw-text-opacity, 1));
}

.text-ui-contrast-fg-primary {
  color: var(--contrast-fg-primary);
}

.text-ui-contrast-fg-secondary {
  color: var(--contrast-fg-secondary);
}

.text-ui-fg-base {
  color: var(--fg-base);
}

.text-ui-fg-disabled {
  color: var(--fg-disabled);
}

.text-ui-fg-error {
  color: var(--fg-error);
}

.text-ui-fg-interactive {
  color: var(--fg-interactive);
}

.text-ui-fg-muted {
  color: var(--fg-muted);
}

.text-ui-fg-on-color {
  color: var(--fg-on-color);
}

.text-ui-fg-on-inverted {
  color: var(--fg-on-inverted);
}

.text-ui-fg-subtle {
  color: var(--fg-subtle);
}

.text-ui-tag-blue-icon {
  color: var(--tag-blue-icon);
}

.text-ui-tag-blue-text {
  color: var(--tag-blue-text);
}

.text-ui-tag-green-icon {
  color: var(--tag-green-icon);
}

.text-ui-tag-green-text {
  color: var(--tag-green-text);
}

.text-ui-tag-neutral-icon {
  color: var(--tag-neutral-icon);
}

.text-ui-tag-neutral-text {
  color: var(--tag-neutral-text);
}

.text-ui-tag-orange-icon {
  color: var(--tag-orange-icon);
}

.text-ui-tag-orange-text {
  color: var(--tag-orange-text);
}

.text-ui-tag-purple-text {
  color: var(--tag-purple-text);
}

.text-ui-tag-red-icon {
  color: var(--tag-red-icon);
}

.text-ui-tag-red-text {
  color: var(--tag-red-text);
}

.text-warning-main {
  --tw-text-opacity: 1;
  color: rgb(232 131 0 / var(--tw-text-opacity, 1));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.text-yellow-300 {
  --tw-text-opacity: 1;
  color: rgb(253 224 71 / var(--tw-text-opacity, 1));
}

.text-zinc-400 {
  --tw-text-opacity: 1;
  color: rgb(161 161 170 / var(--tw-text-opacity, 1));
}

.text-zinc-500 {
  --tw-text-opacity: 1;
  color: rgb(113 113 122 / var(--tw-text-opacity, 1));
}

.text-zinc-600 {
  --tw-text-opacity: 1;
  color: rgb(82 82 91 / var(--tw-text-opacity, 1));
}

.text-zinc-900 {
  --tw-text-opacity: 1;
  color: rgb(24 24 27 / var(--tw-text-opacity, 1));
}

.underline {
  text-decoration-line: underline;
}

.line-through {
  text-decoration-line: line-through;
}

.no-underline {
  text-decoration-line: none;
}

.underline-offset-4 {
  text-underline-offset: 4px;
}

.underline-offset-\[4px\] {
  text-underline-offset: 4px;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.\!placeholder-ui-fg-disabled::-moz-placeholder {
  color: var(--fg-disabled) !important;
}

.\!placeholder-ui-fg-disabled::placeholder {
  color: var(--fg-disabled) !important;
}

.placeholder-ui-fg-muted::-moz-placeholder {
  color: var(--fg-muted);
}

.placeholder-ui-fg-muted::placeholder {
  color: var(--fg-muted);
}

.caret-ui-fg-base {
  caret-color: var(--fg-base);
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.opacity-25 {
  opacity: .25;
}

.opacity-50 {
  opacity: .5;
}

.opacity-60 {
  opacity: .6;
}

.opacity-70 {
  opacity: .7;
}

.opacity-75 {
  opacity: .75;
}

.opacity-90 {
  opacity: .9;
}

.\!shadow-borders-error {
  --tw-shadow: var(--borders-error) !important;
  --tw-shadow-colored: var(--borders-error) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.\!shadow-buttons-neutral {
  --tw-shadow: var(--buttons-neutral) !important;
  --tw-shadow-colored: var(--buttons-neutral) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.shadow-\[0_1px_1px_0\] {
  --tw-shadow: 0 1px 1px 0;
  --tw-shadow-colored: 0 1px 1px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0_8px_24px_rgba\(149\,157\,165\,0\.1\)\] {
  --tw-shadow: 0 8px 24px #959da51a;
  --tw-shadow-colored: 0 8px 24px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-around-circle {
  --tw-shadow: 0 4px 14px #3434341a;
  --tw-shadow-colored: 0 4px 14px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-borders-base {
  --tw-shadow: var(--borders-base);
  --tw-shadow-colored: var(--borders-base);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-borders-error {
  --tw-shadow: var(--borders-error);
  --tw-shadow-colored: var(--borders-error);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-borders-interactive-with-active {
  --tw-shadow: var(--borders-interactive-with-active);
  --tw-shadow-colored: var(--borders-interactive-with-active);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-buttons-danger {
  --tw-shadow: var(--buttons-danger);
  --tw-shadow-colored: var(--buttons-danger);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-buttons-inverted {
  --tw-shadow: var(--buttons-inverted);
  --tw-shadow-colored: var(--buttons-inverted);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-buttons-neutral {
  --tw-shadow: var(--buttons-neutral);
  --tw-shadow-colored: var(--buttons-neutral);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-details-contrast-on-bg-interactive {
  --tw-shadow: var(--details-contrast-on-bg-interactive);
  --tw-shadow-colored: var(--details-contrast-on-bg-interactive);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-details-switch-handle {
  --tw-shadow: var(--details-switch-handle);
  --tw-shadow-colored: var(--details-switch-handle);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-elevation-card-rest {
  --tw-shadow: var(--elevation-card-rest);
  --tw-shadow-colored: var(--elevation-card-rest);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-elevation-code-block {
  --tw-shadow: var(--elevation-code-block);
  --tw-shadow-colored: var(--elevation-code-block);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-elevation-flyout {
  --tw-shadow: var(--elevation-flyout);
  --tw-shadow-colored: var(--elevation-flyout);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-elevation-modal {
  --tw-shadow: var(--elevation-modal);
  --tw-shadow-colored: var(--elevation-modal);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-elevation-tooltip {
  --tw-shadow: var(--elevation-tooltip);
  --tw-shadow-colored: var(--elevation-tooltip);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-ui-border-base {
  --tw-shadow-color: var(--border-base);
  --tw-shadow: var(--tw-shadow-colored);
}

.\!outline-none {
  outline: 2px solid #0000 !important;
  outline-offset: 2px !important;
}

.outline-none {
  outline: 2px solid #0000;
  outline-offset: 2px;
}

.outline {
  outline-style: solid;
}

.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-muted {
  --tw-ring-color: hsl(var(--muted));
}

.ring-offset-background {
  --tw-ring-offset-color: hsl(var(--background));
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.drop-shadow-md {
  --tw-drop-shadow: drop-shadow(0 4px 3px #00000012) drop-shadow(0 2px 2px #0000000f);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.drop-shadow-lg {
  --tw-drop-shadow: drop-shadow(0 10px 8px #0000000a) drop-shadow(0 4px 3px #0000001a);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.grayscale {
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.invert {
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.\!filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) !important;
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur-2xl {
  --tw-backdrop-blur: blur(40px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-md {
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.transition-\[height\,max-height\,opacity\] {
  transition-property: height, max-height, opacity;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.transition-\[max-height\,opacity\] {
  transition-property: max-height, opacity;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.transition-fg {
  transition-property: color, background-color, border-color, box-shadow, opacity;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.delay-200 {
  transition-delay: .2s;
}

.duration-150 {
  transition-duration: .15s;
}

.duration-200 {
  transition-duration: .2s;
}

.duration-300 {
  transition-duration: .3s;
}

.duration-500 {
  transition-duration: .5s;
}

.ease-in {
  transition-timing-function: cubic-bezier(.4, 0, 1, 1);
}

.ease-in-out {
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.ease-linear {
  transition-timing-function: linear;
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, .2, 1);
}

@keyframes enter {
  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}

@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}

.animate-in {
  animation-name: enter;
  animation-duration: .15s;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.fade-in-0 {
  --tw-enter-opacity: 0;
}

.zoom-in-95 {
  --tw-enter-scale: .95;
}

.duration-150 {
  animation-duration: .15s;
}

.duration-200 {
  animation-duration: .2s;
}

.duration-300 {
  animation-duration: .3s;
}

.duration-500 {
  animation-duration: .5s;
}

.delay-200 {
  animation-delay: .2s;
}

.ease-in {
  animation-timing-function: cubic-bezier(.4, 0, 1, 1);
}

.ease-in-out {
  animation-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.ease-linear {
  animation-timing-function: linear;
}

.ease-out {
  animation-timing-function: cubic-bezier(0, 0, .2, 1);
}

@keyframes enter {
  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}

@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}

.animate-in {
  animation-name: enter;
  animation-duration: .15s;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.fade-in-0 {
  --tw-enter-opacity: 0;
}

.zoom-in-95 {
  --tw-enter-scale: .95;
}

.duration-150 {
  animation-duration: .15s;
}

.duration-200 {
  animation-duration: .2s;
}

.duration-300 {
  animation-duration: .3s;
}

.duration-500 {
  animation-duration: .5s;
}

.delay-200 {
  animation-delay: .2s;
}

.ease-in {
  animation-timing-function: cubic-bezier(.4, 0, 1, 1);
}

.ease-in-out {
  animation-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.ease-linear {
  animation-timing-function: linear;
}

.ease-out {
  animation-timing-function: cubic-bezier(0, 0, .2, 1);
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar::-webkit-scrollbar-track {
  background-color: #0000;
}

.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

input:not(:-moz-placeholder) ~ label {
  font-size: .625rem;
  line-height: 1.2;
  font-weight: 400;
  line-height: 1rem;
  --tw-translate-y: -.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

input:focus ~ label, input:not(:placeholder-shown) ~ label {
  font-size: .625rem;
  line-height: 1.2;
  font-weight: 400;
  line-height: 1rem;
  --tw-translate-y: -.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

input:focus ~ label {
  left: 0;
}

input:-webkit-autofill, input:-webkit-autofill:hover, input:-webkit-autofill:focus, textarea:-webkit-autofill, textarea:-webkit-autofill:hover, textarea:-webkit-autofill:focus, select:-webkit-autofill, select:-webkit-autofill:hover, select:-webkit-autofill:focus {
  border: 1px solid #212121;
  -webkit-text-fill-color: #212121;
  -webkit-box-shadow: inset 0 0 0 1000px #fff;
  -webkit-transition: background-color 5000s ease-in-out;
  transition: background-color 5000s ease-in-out;
}

input[type="search"]::-webkit-search-decoration, input[type="search"]::-webkit-search-cancel-button, input[type="search"]::-webkit-search-results-button, input[type="search"]::-webkit-search-results-decoration {
  -webkit-appearance: none;
}

.\[--duration\:40s\] {
  --duration: 40s;
}

.\[--gap\:1rem\] {
  --gap: 1rem;
}

.\[-moz-appearance\:textfield\] {
  -moz-appearance: textfield;
}

.\[animation-direction\:reverse\] {
  animation-direction: reverse;
}

.\[gap\:var\(--gap\)\] {
  gap: var(--gap);
}

body {
  font-family: var(--font-body), ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

.swiper {
  width: 100%;
  height: 100%;
}

.swiper-slide {
  text-align: center;
  font-size: 18px;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}

.swiper-v {
  background: #eee;
}

.swiper-button-prev, .swiper-button-next {
  background: #fffc;
  box-shadow: 0 4px 30px #0000001a;
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  border: 1px solid #ffffff4d;
  width: 40px !important;
  height: 40px !important;
}

.mobile-footer-accordion {
  touch-action: manipulation;
}

.mobile-footer-accordion button {
  min-height: 44px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: #0000001a;
}

.mobile-footer-accordion button:active {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

@media (width <= 767px) {
  html {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  body {
    -webkit-overflow-scrolling: touch;
  }

  button, a, [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }
}

@keyframes phone-ring {
  0%, 100% {
    transform: rotate(0)scale(1);
  }

  10%, 30%, 50%, 70%, 90% {
    transform: rotate(-8deg)scale(1.15);
  }

  20%, 40%, 60%, 80% {
    transform: rotate(8deg)scale(1.15);
  }
}

@keyframes phone-ring-sequence {
  0%, 50% {
    transform: rotate(0)scale(1);
  }

  10%, 20%, 30%, 40% {
    transform: rotate(-8deg)scale(1.15);
  }

  15%, 25%, 35%, 45% {
    transform: rotate(8deg)scale(1.15);
  }

  50%, 100% {
    transform: rotate(0)scale(1);
  }
}

.phone-ring {
  animation: 3s ease-in-out infinite phone-ring-sequence;
}

.phone-ring:hover {
  animation: none;
  transform: scale(1.1);
}

.data-\[state\=checked\]\:txt-compact-small-plus[data-state="checked"] {
  font-size: .8125rem;
  line-height: 1.25rem;
  font-weight: 500;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.\[\&\>\*\]\:txt-compact-small-plus > * {
  font-size: .8125rem;
  line-height: 1.25rem;
  font-weight: 500;
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.\[\&\>code\]\:code-body > code {
  font-size: .75rem;
  line-height: 1.125rem;
  font-weight: 400;
  font-family: Roboto Mono, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
}

.file\:border-0::file-selector-button {
  border-width: 0;
}

.file\:bg-transparent::file-selector-button {
  background-color: #0000;
}

.file\:text-sm::file-selector-button {
  font-size: .875rem;
  line-height: 1.2;
}

.file\:font-medium::file-selector-button {
  font-weight: 500;
}

.file\:text-foreground::file-selector-button {
  color: hsl(var(--foreground));
}

.placeholder\:text-gray-700::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(75 86 107 / var(--tw-text-opacity, 1));
}

.placeholder\:text-gray-700::placeholder {
  --tw-text-opacity: 1;
  color: rgb(75 86 107 / var(--tw-text-opacity, 1));
}

.placeholder\:text-muted-foreground::-moz-placeholder {
  color: hsl(var(--muted-foreground));
}

.placeholder\:text-muted-foreground::placeholder {
  color: hsl(var(--muted-foreground));
}

.placeholder\:text-ui-fg-on-color::-moz-placeholder {
  color: var(--fg-on-color);
}

.placeholder\:text-ui-fg-on-color::placeholder {
  color: var(--fg-on-color);
}

.placeholder\:text-zinc-500::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(113 113 122 / var(--tw-text-opacity, 1));
}

.placeholder\:text-zinc-500::placeholder {
  --tw-text-opacity: 1;
  color: rgb(113 113 122 / var(--tw-text-opacity, 1));
}

.placeholder\:transition-colors::-moz-placeholder {
  -moz-transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.placeholder\:transition-colors::placeholder {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.before\:absolute:before {
  content: var(--tw-content);
  position: absolute;
}

.before\:inset-0:before {
  content: var(--tw-content);
  inset: 0;
}

.before\:rounded-full:before {
  content: var(--tw-content);
  border-radius: 100%;
}

.before\:shadow-details-switch-background:before {
  content: var(--tw-content);
  --tw-shadow: var(--details-switch-background);
  --tw-shadow-colored: var(--details-switch-background);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.before\:content-\[\'\'\]:before {
  --tw-content: "";
  content: var(--tw-content);
}

.after\:pointer-events-none:after {
  content: var(--tw-content);
  pointer-events: none;
}

.after\:absolute:after {
  content: var(--tw-content);
  position: absolute;
}

.after\:inset-0:after {
  content: var(--tw-content);
  inset: 0;
}

.after\:inset-y-0:after {
  content: var(--tw-content);
  top: 0;
  bottom: 0;
}

.after\:right-0:after {
  content: var(--tw-content);
  right: 0;
}

.after\:hidden:after {
  content: var(--tw-content);
  display: none;
}

.after\:h-full:after {
  content: var(--tw-content);
  height: 100%;
}

.after\:w-px:after {
  content: var(--tw-content);
  width: 1px;
}

.after\:rounded-full:after {
  content: var(--tw-content);
  border-radius: 100%;
}

.after\:bg-transparent:after {
  content: var(--tw-content);
  background-color: #0000;
}

.after\:bg-ui-border-base:after {
  content: var(--tw-content);
  background-color: var(--border-base);
}

.after\:shadow-elevation-flyout:after {
  content: var(--tw-content);
  --tw-shadow: var(--elevation-flyout);
  --tw-shadow-colored: var(--elevation-flyout);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.after\:transition-fg:after {
  content: var(--tw-content);
  transition-property: color, background-color, border-color, box-shadow, opacity;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.after\:content-\[\'\'\]:after {
  --tw-content: "";
  content: var(--tw-content);
}

.first\:mt-0:first-child {
  margin-top: 0;
}

.last\:mb-0:last-child {
  margin-bottom: 0;
}

.last\:border-b:last-child {
  border-bottom-width: 1px;
}

.last-of-type\:-mr-1:last-of-type {
  margin-right: -.25rem;
}

.last-of-type\:border-b-0:last-of-type {
  border-bottom-width: 0;
}

.last-of-type\:pr-4:last-of-type {
  padding-right: 1rem;
}

.invalid\:border-ui-border-error:invalid {
  border-color: var(--border-error);
}

.invalid\:\!shadow-borders-error:invalid {
  --tw-shadow: var(--borders-error) !important;
  --tw-shadow-colored: var(--borders-error) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.invalid\:shadow-borders-error:invalid {
  --tw-shadow: var(--borders-error);
  --tw-shadow-colored: var(--borders-error);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus-within\:relative:focus-within {
  position: relative;
}

.focus-within\:z-20:focus-within {
  z-index: 20;
}

.focus-within\:shadow-borders-interactive-with-active:focus-within {
  --tw-shadow: var(--borders-interactive-with-active);
  --tw-shadow-colored: var(--borders-interactive-with-active);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:scale-110:hover {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[1\.02\]:hover {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:cursor-pointer:hover {
  cursor: pointer;
}

.hover\:border:hover {
  border-width: 1px;
}

.hover\:border-gray-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(193 205 215 / var(--tw-border-opacity, 1));
}

.hover\:border-primary-main:hover {
  --tw-border-opacity: 1;
  border-color: rgb(89 183 31 / var(--tw-border-opacity, 1));
}

.hover\:border-primary-main\/30:hover {
  border-color: #59b71f4d;
}

.hover\:\!bg-transparent:hover {
  background-color: #0000 !important;
}

.hover\:\!bg-ui-bg-base:hover {
  background-color: var(--bg-base) !important;
}

.hover\:bg-accent:hover {
  background-color: hsl(var(--accent));
}

.hover\:bg-destructive\/80:hover {
  background-color: hsl(var(--destructive) / .8);
}

.hover\:bg-destructive\/90:hover {
  background-color: hsl(var(--destructive) / .9);
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(244 246 248 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(235 239 244 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(33 43 54 / var(--tw-bg-opacity, 1));
}

.hover\:bg-muted:hover {
  background-color: hsl(var(--muted));
}

.hover\:bg-muted\/30:hover {
  background-color: hsl(var(--muted) / .3);
}

.hover\:bg-muted\/50:hover {
  background-color: hsl(var(--muted) / .5);
}

.hover\:bg-primary:hover {
  background-color: hsl(var(--primary));
}

.hover\:bg-primary-dark:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(4 114 33 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary-light:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(119 184 79 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary-lighter:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(238 251 230 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary-main:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(89 183 31 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary-main\/10:hover {
  background-color: #59b71f1a;
}

.hover\:bg-primary-main\/80:hover {
  background-color: #59b71fcc;
}

.hover\:bg-primary-main\/90:hover {
  background-color: #59b71fe6;
}

.hover\:bg-secondary\/80:hover {
  background-color: hsl(var(--secondary) / .8);
}

.hover\:bg-transparent:hover {
  background-color: #0000;
}

.hover\:bg-ui-bg-base-hover:hover {
  background-color: var(--bg-base-hover);
}

.hover\:bg-ui-bg-field-hover:hover {
  background-color: var(--bg-field-hover);
}

.hover\:bg-ui-bg-subtle-hover:hover {
  background-color: var(--bg-subtle-hover);
}

.hover\:bg-ui-bg-switch-off-hover:hover {
  background-color: var(--bg-switch-off-hover);
}

.hover\:bg-ui-button-danger-hover:hover {
  background-color: var(--button-danger-hover);
}

.hover\:bg-ui-button-inverted-hover:hover {
  background-color: var(--button-inverted-hover);
}

.hover\:bg-ui-button-neutral-hover:hover {
  background-color: var(--button-neutral-hover);
}

.hover\:bg-ui-button-transparent-hover:hover {
  background-color: var(--button-transparent-hover);
}

.hover\:bg-ui-contrast-bg-base-hover:hover {
  background-color: var(--contrast-bg-base-hover);
}

.hover\:font-bold:hover {
  font-weight: 700;
}

.hover\:font-semibold:hover {
  font-weight: 600;
}

.hover\:text-accent-foreground:hover {
  color: hsl(var(--accent-foreground));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(125 135 156 / var(--tw-text-opacity, 1));
}

.hover\:text-primary-foreground:hover {
  color: hsl(var(--primary-foreground));
}

.hover\:text-primary-main:hover {
  --tw-text-opacity: 1;
  color: rgb(89 183 31 / var(--tw-text-opacity, 1));
}

.hover\:text-ui-fg-base:hover {
  color: var(--fg-base);
}

.hover\:text-ui-fg-disabled:hover {
  color: var(--fg-disabled);
}

.hover\:text-ui-fg-subtle:hover {
  color: var(--fg-subtle);
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:no-underline:hover {
  text-decoration-line: none;
}

.hover\:underline-offset-\[12px\]:hover {
  text-underline-offset: 12px;
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.hover\:opacity-80:hover {
  opacity: .8;
}

.hover\:opacity-90:hover {
  opacity: .9;
}

.hover\:shadow-borders-interactive-with-active:hover {
  --tw-shadow: var(--borders-interactive-with-active);
  --tw-shadow-colored: var(--borders-interactive-with-active);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-elevation-card-hover:hover {
  --tw-shadow: var(--elevation-card-hover);
  --tw-shadow-colored: var(--elevation-card-hover);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:z-\[1\]:focus {
  z-index: 1;
}

.focus\:border-gray-300:focus {
  --tw-border-opacity: 1;
  border-color: rgb(216 224 233 / var(--tw-border-opacity, 1));
}

.focus\:border-gray-700:focus {
  --tw-border-opacity: 1;
  border-color: rgb(75 86 107 / var(--tw-border-opacity, 1));
}

.focus\:border-primary-main:focus {
  --tw-border-opacity: 1;
  border-color: rgb(89 183 31 / var(--tw-border-opacity, 1));
}

.focus\:bg-accent:focus {
  background-color: hsl(var(--accent));
}

.focus\:bg-primary:focus {
  background-color: hsl(var(--primary));
}

.focus\:bg-transparent:focus {
  background-color: #0000;
}

.focus\:bg-ui-bg-component-hover:focus {
  background-color: var(--bg-component-hover);
}

.focus\:text-accent-foreground:focus {
  color: hsl(var(--accent-foreground));
}

.focus\:text-primary-foreground:focus {
  color: hsl(var(--primary-foreground));
}

.focus\:text-ui-fg-base:focus {
  color: var(--fg-base);
}

.focus\:outline-none:focus {
  outline: 2px solid #0000;
  outline-offset: 2px;
}

.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-primary-light:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(119 184 79 / var(--tw-ring-opacity, 1));
}

.focus\:ring-primary-main:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(89 183 31 / var(--tw-ring-opacity, 1));
}

.focus\:ring-primary-main\/80:focus {
  --tw-ring-color: #59b71fcc;
}

.focus\:ring-ring:focus {
  --tw-ring-color: hsl(var(--ring));
}

.focus\:ring-white:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));
}

.focus\:ring-opacity-75:focus {
  --tw-ring-opacity: .75;
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.focus\:ring-offset-gray-300:focus {
  --tw-ring-offset-color: #d8e0e9;
}

.focus-visible\:border-none:focus-visible {
  border-style: none;
}

.focus-visible\:border-ui-border-interactive:focus-visible {
  border-color: var(--border-interactive);
}

.focus-visible\:bg-ui-bg-base:focus-visible {
  background-color: var(--bg-base);
}

.focus-visible\:bg-ui-bg-component-hover:focus-visible {
  background-color: var(--bg-component-hover);
}

.focus-visible\:bg-ui-bg-interactive:focus-visible {
  background-color: var(--bg-interactive);
}

.focus-visible\:text-ui-fg-base:focus-visible {
  color: var(--fg-base);
}

.focus-visible\:text-ui-fg-on-color:focus-visible {
  color: var(--fg-on-color);
}

.focus-visible\:\!shadow-borders-focus:focus-visible {
  --tw-shadow: var(--borders-focus) !important;
  --tw-shadow-colored: var(--borders-focus) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.focus-visible\:\!shadow-buttons-inverted-focus:focus-visible {
  --tw-shadow: var(--buttons-inverted-focus) !important;
  --tw-shadow-colored: var(--buttons-inverted-focus) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.focus-visible\:shadow-borders-focus:focus-visible {
  --tw-shadow: var(--borders-focus);
  --tw-shadow-colored: var(--borders-focus);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus-visible\:shadow-borders-interactive-with-active:focus-visible {
  --tw-shadow: var(--borders-interactive-with-active);
  --tw-shadow-colored: var(--borders-interactive-with-active);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus-visible\:shadow-borders-interactive-with-focus:focus-visible {
  --tw-shadow: var(--borders-interactive-with-focus);
  --tw-shadow-colored: var(--borders-interactive-with-focus);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus-visible\:shadow-buttons-danger-focus:focus-visible {
  --tw-shadow: var(--buttons-danger-focus);
  --tw-shadow-colored: var(--buttons-danger-focus);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus-visible\:shadow-buttons-neutral-focus:focus-visible {
  --tw-shadow: var(--buttons-neutral-focus);
  --tw-shadow-colored: var(--buttons-neutral-focus);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus-visible\:shadow-details-switch-background-focus:focus-visible {
  --tw-shadow: var(--details-switch-background-focus);
  --tw-shadow-colored: var(--details-switch-background-focus);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus-visible\:outline-none:focus-visible {
  outline: 2px solid #0000;
  outline-offset: 2px;
}

.focus-visible\:ring-0:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-1:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-primary-main:focus-visible {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(89 183 31 / var(--tw-ring-opacity, 1));
}

.focus-visible\:ring-ring:focus-visible {
  --tw-ring-color: hsl(var(--ring));
}

.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}

.focus-visible\:hover\:bg-ui-contrast-bg-base-hover:hover:focus-visible {
  background-color: var(--contrast-bg-base-hover);
}

.active\:bg-gray-200:active {
  --tw-bg-opacity: 1;
  background-color: rgb(235 239 244 / var(--tw-bg-opacity, 1));
}

.active\:bg-gray-50:active {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.active\:bg-transparent:active {
  background-color: #0000;
}

.active\:bg-ui-bg-base-pressed:active {
  background-color: var(--bg-base-pressed);
}

.active\:bg-ui-bg-component-hover:active {
  background-color: var(--bg-component-hover);
}

.active\:bg-ui-bg-component-pressed:active {
  background-color: var(--bg-component-pressed);
}

.active\:bg-ui-button-danger-pressed:active {
  background-color: var(--button-danger-pressed);
}

.active\:bg-ui-button-inverted-pressed:active {
  background-color: var(--button-inverted-pressed);
}

.active\:bg-ui-button-neutral-pressed:active {
  background-color: var(--button-neutral-pressed);
}

.active\:bg-ui-button-transparent-pressed:active {
  background-color: var(--button-transparent-pressed);
}

.active\:bg-ui-contrast-bg-base-pressed:active {
  background-color: var(--contrast-bg-base-pressed);
}

.active\:text-primary-main:active {
  --tw-text-opacity: 1;
  color: rgb(89 183 31 / var(--tw-text-opacity, 1));
}

.active\:text-ui-fg-base:active {
  color: var(--fg-base);
}

.focus-visible\:active\:bg-ui-contrast-bg-base-pressed:active:focus-visible {
  background-color: var(--contrast-bg-base-pressed);
}

.hover\:enabled\:bg-ui-bg-base-hover:enabled:hover {
  background-color: var(--bg-base-hover);
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:border-ui-border-base:disabled {
  border-color: var(--border-base);
}

.disabled\:\!bg-transparent:disabled {
  background-color: #0000 !important;
}

.disabled\:\!bg-ui-bg-disabled:disabled {
  background-color: var(--bg-disabled) !important;
}

.disabled\:bg-transparent:disabled {
  background-color: #0000;
}

.disabled\:bg-ui-bg-disabled:disabled {
  background-color: var(--bg-disabled);
}

.disabled\:\!text-ui-fg-disabled:disabled {
  color: var(--fg-disabled) !important;
}

.disabled\:text-orange-500:disabled {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}

.disabled\:text-ui-fg-disabled:disabled {
  color: var(--fg-disabled);
}

.disabled\:text-ui-fg-muted:disabled {
  color: var(--fg-muted);
}

.disabled\:placeholder-ui-fg-disabled:disabled::-moz-placeholder {
  color: var(--fg-disabled);
}

.disabled\:placeholder-ui-fg-disabled:disabled::placeholder {
  color: var(--fg-disabled);
}

.disabled\:opacity-50:disabled {
  opacity: .5;
}

.disabled\:opacity-70:disabled {
  opacity: .7;
}

.disabled\:shadow-buttons-neutral:disabled {
  --tw-shadow: var(--buttons-neutral);
  --tw-shadow-colored: var(--buttons-neutral);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.disabled\:after\:hidden:disabled:after {
  content: var(--tw-content);
  display: none;
}

.group:hover .group-hover\:rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

.group:hover .group-hover\:animate-pulse {
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite pulse;
}

.group\/row:hover .group-hover\/row\:bg-ui-bg-base-hover {
  background-color: var(--bg-base-hover);
}

.group:hover .group-hover\:bg-primary-main\/60 {
  background-color: #59b71f99;
}

.group:hover .group-hover\:text-primary-main {
  --tw-text-opacity: 1;
  color: rgb(89 183 31 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.group:hover .group-hover\:opacity-50 {
  opacity: .5;
}

.group:hover .group-hover\:shadow-elevation-card-hover {
  --tw-shadow: var(--elevation-card-hover);
  --tw-shadow-colored: var(--elevation-card-hover);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group:hover .group-hover\:\[animation-play-state\:paused\] {
  animation-play-state: paused;
}

.group:focus .group-focus\:\!shadow-borders-interactive-with-focus {
  --tw-shadow: var(--borders-interactive-with-focus) !important;
  --tw-shadow-colored: var(--borders-interactive-with-focus) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.group:focus-visible .group-focus-visible\:\!shadow-borders-interactive-with-focus {
  --tw-shadow: var(--borders-interactive-with-focus) !important;
  --tw-shadow-colored: var(--borders-interactive-with-focus) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.group:disabled .group-disabled\:cursor-not-allowed {
  cursor: not-allowed;
}

.group:disabled .group-disabled\:\!bg-ui-bg-disabled {
  background-color: var(--bg-disabled) !important;
}

.group:disabled .group-disabled\:bg-ui-fg-disabled {
  background-color: var(--fg-disabled);
}

.group\/trigger:disabled .group-disabled\/trigger\:text-ui-fg-disabled {
  color: var(--fg-disabled);
}

.group:disabled .group-disabled\:text-ui-fg-disabled {
  color: var(--fg-disabled);
}

.group:disabled .group-disabled\:opacity-50 {
  opacity: .5;
}

.group:disabled .group-disabled\:\!shadow-borders-base {
  --tw-shadow: var(--borders-base) !important;
  --tw-shadow-colored: var(--borders-base) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.peer:disabled ~ .peer-disabled\:cursor-not-allowed {
  cursor: not-allowed;
}

.peer:disabled ~ .peer-disabled\:opacity-70 {
  opacity: .7;
}

.aria-selected\:bg-accent[aria-selected="true"] {
  background-color: hsl(var(--accent));
}

.aria-selected\:bg-accent\/50[aria-selected="true"] {
  background-color: hsl(var(--accent) / .5);
}

.aria-selected\:text-accent-foreground[aria-selected="true"] {
  color: hsl(var(--accent-foreground));
}

.aria-selected\:text-muted-foreground[aria-selected="true"] {
  color: hsl(var(--muted-foreground));
}

.aria-selected\:opacity-100[aria-selected="true"] {
  opacity: 1;
}

.aria-\[invalid\=true\]\:border-ui-border-error[aria-invalid="true"] {
  border-color: var(--border-error);
}

.aria-\[invalid\=true\]\:\!shadow-borders-error[aria-invalid="true"] {
  --tw-shadow: var(--borders-error) !important;
  --tw-shadow-colored: var(--borders-error) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.aria-\[invalid\=true\]\:shadow-borders-error[aria-invalid="true"] {
  --tw-shadow: var(--borders-error);
  --tw-shadow-colored: var(--borders-error);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.data-\[disabled\=true\]\:pointer-events-none[data-disabled="true"] {
  pointer-events: none;
}

.data-\[disabled\]\:pointer-events-none[data-disabled] {
  pointer-events: none;
}

.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
  --tw-translate-y: .25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=left\]\:-translate-x-1[data-side="left"] {
  --tw-translate-x: -.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=right\]\:translate-x-1[data-side="right"] {
  --tw-translate-x: .25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=top\]\:-translate-y-1[data-side="top"] {
  --tw-translate-y: -.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[state\=checked\]\:translate-x-3\.5[data-state="checked"] {
  --tw-translate-x: .875rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[state\=checked\]\:translate-x-4[data-state="checked"] {
  --tw-translate-x: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[state\=unchecked\]\:translate-x-0\.5[data-state="unchecked"] {
  --tw-translate-x: .125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }

  to {
    height: 0;
  }
}

.data-\[state\=closed\]\:animate-accordion-up[data-state="closed"] {
  animation: .2s ease-out accordion-up;
}

@keyframes accordion-down {
  from {
    height: 0;
  }

  to {
    height: var(--radix-accordion-content-height);
  }
}

.data-\[state\=open\]\:animate-accordion-down[data-state="open"] {
  animation: .2s ease-out accordion-down;
}

.data-\[state\=active\]\:border-0[data-state="active"] {
  border-width: 0;
}

.data-\[selected\=true\]\:bg-accent[data-selected="true"] {
  background-color: hsl(var(--accent));
}

.data-\[state\=active\]\:bg-background[data-state="active"] {
  background-color: hsl(var(--background));
}

.data-\[state\=active\]\:bg-primary-light[data-state="active"] {
  --tw-bg-opacity: 1;
  background-color: rgb(119 184 79 / var(--tw-bg-opacity, 1));
}

.data-\[state\=active\]\:bg-transparent[data-state="active"] {
  background-color: #0000;
}

.data-\[state\=active\]\:bg-ui-bg-base[data-state="active"] {
  background-color: var(--bg-base);
}

.data-\[state\=checked\]\:bg-primary-main[data-state="checked"] {
  --tw-bg-opacity: 1;
  background-color: rgb(89 183 31 / var(--tw-bg-opacity, 1));
}

.data-\[state\=checked\]\:bg-ui-bg-interactive[data-state="checked"] {
  background-color: var(--bg-interactive);
}

.data-\[state\=open\]\:\!bg-ui-bg-component-hover[data-state="open"] {
  background-color: var(--bg-component-hover) !important;
}

.data-\[state\=open\]\:bg-accent[data-state="open"] {
  background-color: hsl(var(--accent));
}

.data-\[state\=open\]\:bg-accent\/50[data-state="open"] {
  background-color: hsl(var(--accent) / .5);
}

.data-\[state\=open\]\:bg-secondary[data-state="open"] {
  background-color: hsl(var(--secondary));
}

.data-\[state\=selected\]\:bg-muted[data-state="selected"] {
  background-color: hsl(var(--muted));
}

.data-\[state\=active\]\:font-bold[data-state="active"] {
  font-weight: 700;
}

.data-\[disabled\]\:text-ui-fg-disabled[data-disabled] {
  color: var(--fg-disabled);
}

.data-\[placeholder\]\:text-ui-fg-muted[data-placeholder] {
  color: var(--fg-muted);
}

.data-\[selected\=true\]\:text-accent-foreground[data-selected="true"] {
  color: hsl(var(--accent-foreground));
}

.data-\[state\=active\]\:text-ui-fg-base[data-state="active"] {
  color: var(--fg-base);
}

.data-\[state\=active\]\:text-white[data-state="active"] {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.data-\[state\=checked\]\:text-white[data-state="checked"] {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.data-\[state\=open\]\:text-accent-foreground[data-state="open"] {
  color: hsl(var(--accent-foreground));
}

.data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
  color: hsl(var(--muted-foreground));
}

.data-\[disabled\=true\]\:opacity-50[data-disabled="true"] {
  opacity: .5;
}

.data-\[disabled\]\:opacity-50[data-disabled] {
  opacity: .5;
}

.data-\[state\=active\]\:shadow-elevation-card-rest[data-state="active"] {
  --tw-shadow: var(--elevation-card-rest);
  --tw-shadow-colored: var(--elevation-card-rest);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.data-\[state\=checked\]\:shadow-borders-interactive-with-shadow[data-state="checked"] {
  --tw-shadow: var(--borders-interactive-with-shadow);
  --tw-shadow-colored: var(--borders-interactive-with-shadow);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.data-\[state\=open\]\:\!shadow-borders-interactive-with-active[data-state="open"] {
  --tw-shadow: var(--borders-interactive-with-active) !important;
  --tw-shadow-colored: var(--borders-interactive-with-active) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.data-\[state\=closed\]\:duration-300[data-state="closed"] {
  transition-duration: .3s;
}

.data-\[state\=open\]\:duration-500[data-state="open"] {
  transition-duration: .5s;
}

.data-\[motion\^\=from-\]\:animate-in[data-motion^="from-"] {
  animation-name: enter;
  animation-duration: .15s;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.data-\[state\=open\]\:animate-in[data-state="open"] {
  animation-name: enter;
  animation-duration: .15s;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.data-\[state\=visible\]\:animate-in[data-state="visible"] {
  animation-name: enter;
  animation-duration: .15s;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.data-\[motion\^\=to-\]\:animate-out[data-motion^="to-"] {
  animation-name: exit;
  animation-duration: .15s;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[state\=closed\]\:animate-out[data-state="closed"] {
  animation-name: exit;
  animation-duration: .15s;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[state\=hidden\]\:animate-out[data-state="hidden"] {
  animation-name: exit;
  animation-duration: .15s;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[motion\^\=from-\]\:fade-in[data-motion^="from-"] {
  --tw-enter-opacity: 0;
}

.data-\[motion\^\=to-\]\:fade-out[data-motion^="to-"] {
  --tw-exit-opacity: 0;
}

.data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
  --tw-exit-opacity: 0;
}

.data-\[state\=hidden\]\:fade-out[data-state="hidden"] {
  --tw-exit-opacity: 0;
}

.data-\[state\=open\]\:fade-in-0[data-state="open"] {
  --tw-enter-opacity: 0;
}

.data-\[state\=visible\]\:fade-in[data-state="visible"] {
  --tw-enter-opacity: 0;
}

.data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
  --tw-exit-scale: .95;
}

.data-\[state\=open\]\:zoom-in-90[data-state="open"] {
  --tw-enter-scale: .9;
}

.data-\[state\=open\]\:zoom-in-95[data-state="open"] {
  --tw-enter-scale: .95;
}

.data-\[motion\=from-end\]\:slide-in-from-right-52[data-motion="from-end"] {
  --tw-enter-translate-x: 13rem;
}

.data-\[motion\=from-start\]\:slide-in-from-left-52[data-motion="from-start"] {
  --tw-enter-translate-x: -13rem;
}

.data-\[motion\=to-end\]\:slide-out-to-right-52[data-motion="to-end"] {
  --tw-exit-translate-x: 13rem;
}

.data-\[motion\=to-start\]\:slide-out-to-left-52[data-motion="to-start"] {
  --tw-exit-translate-x: -13rem;
}

.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
  --tw-enter-translate-y: -.5rem;
}

.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
  --tw-enter-translate-x: .5rem;
}

.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
  --tw-enter-translate-x: -.5rem;
}

.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
  --tw-enter-translate-y: .5rem;
}

.data-\[state\=closed\]\:slide-in-from-bottom-2[data-state="closed"] {
  --tw-enter-translate-y: .5rem;
}

.data-\[state\=closed\]\:slide-out-to-bottom[data-state="closed"] {
  --tw-exit-translate-y: 100%;
}

.data-\[state\=closed\]\:slide-out-to-left[data-state="closed"] {
  --tw-exit-translate-x: -100%;
}

.data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state="closed"] {
  --tw-exit-translate-x: -50%;
}

.data-\[state\=closed\]\:slide-out-to-right[data-state="closed"] {
  --tw-exit-translate-x: 100%;
}

.data-\[state\=closed\]\:slide-out-to-right-1\/2[data-state="closed"] {
  --tw-exit-translate-x: 50%;
}

.data-\[state\=closed\]\:slide-out-to-top[data-state="closed"] {
  --tw-exit-translate-y: -100%;
}

.data-\[state\=open\]\:slide-in-from-bottom[data-state="open"] {
  --tw-enter-translate-y: 100%;
}

.data-\[state\=open\]\:slide-in-from-bottom-0[data-state="open"] {
  --tw-enter-translate-y: 0px;
}

.data-\[state\=open\]\:slide-in-from-left[data-state="open"] {
  --tw-enter-translate-x: -100%;
}

.data-\[state\=open\]\:slide-in-from-left-1\/2[data-state="open"] {
  --tw-enter-translate-x: -50%;
}

.data-\[state\=open\]\:slide-in-from-right[data-state="open"] {
  --tw-enter-translate-x: 100%;
}

.data-\[state\=open\]\:slide-in-from-right-1\/2[data-state="open"] {
  --tw-enter-translate-x: 50%;
}

.data-\[state\=open\]\:slide-in-from-top[data-state="open"] {
  --tw-enter-translate-y: -100%;
}

.data-\[state\=closed\]\:duration-300[data-state="closed"] {
  animation-duration: .3s;
}

.data-\[state\=open\]\:duration-500[data-state="open"] {
  animation-duration: .5s;
}

.data-\[motion\^\=from-\]\:animate-in[data-motion^="from-"] {
  animation-name: enter;
  animation-duration: .15s;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.data-\[state\=open\]\:animate-in[data-state="open"] {
  animation-name: enter;
  animation-duration: .15s;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.data-\[state\=visible\]\:animate-in[data-state="visible"] {
  animation-name: enter;
  animation-duration: .15s;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.data-\[motion\^\=to-\]\:animate-out[data-motion^="to-"] {
  animation-name: exit;
  animation-duration: .15s;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[state\=closed\]\:animate-out[data-state="closed"] {
  animation-name: exit;
  animation-duration: .15s;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[state\=hidden\]\:animate-out[data-state="hidden"] {
  animation-name: exit;
  animation-duration: .15s;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[motion\^\=from-\]\:fade-in[data-motion^="from-"] {
  --tw-enter-opacity: 0;
}

.data-\[motion\^\=to-\]\:fade-out[data-motion^="to-"] {
  --tw-exit-opacity: 0;
}

.data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
  --tw-exit-opacity: 0;
}

.data-\[state\=hidden\]\:fade-out[data-state="hidden"] {
  --tw-exit-opacity: 0;
}

.data-\[state\=open\]\:fade-in-0[data-state="open"] {
  --tw-enter-opacity: 0;
}

.data-\[state\=visible\]\:fade-in[data-state="visible"] {
  --tw-enter-opacity: 0;
}

.data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
  --tw-exit-scale: .95;
}

.data-\[state\=open\]\:zoom-in-90[data-state="open"] {
  --tw-enter-scale: .9;
}

.data-\[state\=open\]\:zoom-in-95[data-state="open"] {
  --tw-enter-scale: .95;
}

.data-\[motion\=from-end\]\:slide-in-from-right-52[data-motion="from-end"] {
  --tw-enter-translate-x: 13rem;
}

.data-\[motion\=from-start\]\:slide-in-from-left-52[data-motion="from-start"] {
  --tw-enter-translate-x: -13rem;
}

.data-\[motion\=to-end\]\:slide-out-to-right-52[data-motion="to-end"] {
  --tw-exit-translate-x: 13rem;
}

.data-\[motion\=to-start\]\:slide-out-to-left-52[data-motion="to-start"] {
  --tw-exit-translate-x: -13rem;
}

.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
  --tw-enter-translate-y: -.5rem;
}

.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
  --tw-enter-translate-x: .5rem;
}

.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
  --tw-enter-translate-x: -.5rem;
}

.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
  --tw-enter-translate-y: .5rem;
}

.data-\[state\=closed\]\:slide-in-from-bottom-2[data-state="closed"] {
  --tw-enter-translate-y: .5rem;
}

.data-\[state\=closed\]\:slide-out-to-bottom[data-state="closed"] {
  --tw-exit-translate-y: 100%;
}

.data-\[state\=closed\]\:slide-out-to-left[data-state="closed"] {
  --tw-exit-translate-x: -100%;
}

.data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state="closed"] {
  --tw-exit-translate-x: -50%;
}

.data-\[state\=closed\]\:slide-out-to-right[data-state="closed"] {
  --tw-exit-translate-x: 100%;
}

.data-\[state\=closed\]\:slide-out-to-right-1\/2[data-state="closed"] {
  --tw-exit-translate-x: 50%;
}

.data-\[state\=closed\]\:slide-out-to-top[data-state="closed"] {
  --tw-exit-translate-y: -100%;
}

.data-\[state\=open\]\:slide-in-from-bottom[data-state="open"] {
  --tw-enter-translate-y: 100%;
}

.data-\[state\=open\]\:slide-in-from-bottom-0[data-state="open"] {
  --tw-enter-translate-y: 0px;
}

.data-\[state\=open\]\:slide-in-from-left[data-state="open"] {
  --tw-enter-translate-x: -100%;
}

.data-\[state\=open\]\:slide-in-from-left-1\/2[data-state="open"] {
  --tw-enter-translate-x: -50%;
}

.data-\[state\=open\]\:slide-in-from-right[data-state="open"] {
  --tw-enter-translate-x: 100%;
}

.data-\[state\=open\]\:slide-in-from-right-1\/2[data-state="open"] {
  --tw-enter-translate-x: 50%;
}

.data-\[state\=open\]\:slide-in-from-top[data-state="open"] {
  --tw-enter-translate-y: -100%;
}

.data-\[state\=closed\]\:duration-300[data-state="closed"] {
  animation-duration: .3s;
}

.data-\[state\=open\]\:duration-500[data-state="open"] {
  animation-duration: .5s;
}

.data-\[state\=open\]\:hover\:bg-accent:hover[data-state="open"] {
  background-color: hsl(var(--accent));
}

.data-\[state\=open\]\:focus\:bg-accent:focus[data-state="open"] {
  background-color: hsl(var(--accent));
}

.group[data-state="open"] .group-data-\[state\=open\]\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-state="open"] .group-data-\[state\=open\]\:rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-state="checked"] .group-data-\[state\=checked\]\:bg-ui-bg-interactive {
  background-color: var(--bg-interactive);
}

.group[data-state="indeterminate"] .group-data-\[state\=indeterminate\]\:bg-ui-bg-interactive {
  background-color: var(--bg-interactive);
}

.group\/trigger[data-state="active"] .group-data-\[state\=active\]\/trigger\:text-ui-fg-interactive {
  color: var(--fg-interactive);
}

.group[data-state="open"] .group-data-\[state\=open\]\:text-ui-fg-interactive {
  color: var(--fg-interactive);
}

.group[data-state="checked"] .group-data-\[state\=checked\]\:shadow-borders-interactive-with-shadow {
  --tw-shadow: var(--borders-interactive-with-shadow);
  --tw-shadow-colored: var(--borders-interactive-with-shadow);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-state="indeterminate"] .group-data-\[state\=indeterminate\]\:shadow-borders-interactive-with-shadow {
  --tw-shadow: var(--borders-interactive-with-shadow);
  --tw-shadow-colored: var(--borders-interactive-with-shadow);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group:hover:enabled[data-state="unchecked"] .group-hover\:group-enabled\:group-data-\[state\=unchecked\]\:bg-ui-bg-base-hover {
  background-color: var(--bg-base-hover);
}

.radix-state-closed\:pointer-events-none[data-state="closed"] {
  pointer-events: none;
}

.group[data-state="open"] .group-radix-state-open\:left-1\/2 {
  left: 50%;
}

.group[data-state="open"] .group-radix-state-open\:right-1\/2 {
  right: 50%;
}

.group[data-state="open"] .group-radix-state-open\:rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@media (prefers-reduced-motion: reduce) {
  .motion-reduce\:transition-none {
    transition-property: none;
  }
}

.dark\:from-background:is(.dark *) {
  --tw-gradient-from: hsl(var(--background)) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

@media not all and (width >= 768px) {
  .max-md\:hidden {
    display: none;
  }

  .max-md\:\!rounded-xs {
    border-radius: 4px !important;
  }

  .max-md\:pt-5 {
    padding-top: 1.25rem;
  }

  .max-md\:text-start {
    text-align: start;
  }
}

@media not all and (width >= 640px) {
  .max-sm\:inset-x-2 {
    left: .5rem;
    right: .5rem;
  }

  .max-sm\:w-\[calc\(100\%-16px\)\] {
    width: calc(100% - 16px);
  }
}

@media (width >= 400px) {
  .xs\:-col-start-1 {
    grid-column-start: -1;
  }

  .xs\:-col-start-10 {
    grid-column-start: -10;
  }

  .xs\:-col-start-11 {
    grid-column-start: -11;
  }

  .xs\:-col-start-12 {
    grid-column-start: -12;
  }

  .xs\:-col-start-13 {
    grid-column-start: -13;
  }

  .xs\:-col-start-2 {
    grid-column-start: -2;
  }

  .xs\:-col-start-3 {
    grid-column-start: -3;
  }

  .xs\:-col-start-4 {
    grid-column-start: -4;
  }

  .xs\:-col-start-5 {
    grid-column-start: -5;
  }

  .xs\:-col-start-6 {
    grid-column-start: -6;
  }

  .xs\:-col-start-7 {
    grid-column-start: -7;
  }

  .xs\:-col-start-8 {
    grid-column-start: -8;
  }

  .xs\:-col-start-9 {
    grid-column-start: -9;
  }

  .xs\:col-start-1 {
    grid-column-start: 1;
  }

  .xs\:col-start-10 {
    grid-column-start: 10;
  }

  .xs\:col-start-11 {
    grid-column-start: 11;
  }

  .xs\:col-start-12 {
    grid-column-start: 12;
  }

  .xs\:col-start-13 {
    grid-column-start: 13;
  }

  .xs\:col-start-2 {
    grid-column-start: 2;
  }

  .xs\:col-start-3 {
    grid-column-start: 3;
  }

  .xs\:col-start-4 {
    grid-column-start: 4;
  }

  .xs\:col-start-5 {
    grid-column-start: 5;
  }

  .xs\:col-start-6 {
    grid-column-start: 6;
  }

  .xs\:col-start-7 {
    grid-column-start: 7;
  }

  .xs\:col-start-8 {
    grid-column-start: 8;
  }

  .xs\:col-start-9 {
    grid-column-start: 9;
  }

  .xs\:-col-end-1 {
    grid-column-end: -1;
  }

  .xs\:-col-end-10 {
    grid-column-end: -10;
  }

  .xs\:-col-end-11 {
    grid-column-end: -11;
  }

  .xs\:-col-end-12 {
    grid-column-end: -12;
  }

  .xs\:-col-end-13 {
    grid-column-end: -13;
  }

  .xs\:-col-end-2 {
    grid-column-end: -2;
  }

  .xs\:-col-end-3 {
    grid-column-end: -3;
  }

  .xs\:-col-end-4 {
    grid-column-end: -4;
  }

  .xs\:-col-end-5 {
    grid-column-end: -5;
  }

  .xs\:-col-end-6 {
    grid-column-end: -6;
  }

  .xs\:-col-end-7 {
    grid-column-end: -7;
  }

  .xs\:-col-end-8 {
    grid-column-end: -8;
  }

  .xs\:-col-end-9 {
    grid-column-end: -9;
  }

  .xs\:col-end-1 {
    grid-column-end: 1;
  }

  .xs\:col-end-10 {
    grid-column-end: 10;
  }

  .xs\:col-end-11 {
    grid-column-end: 11;
  }

  .xs\:col-end-12 {
    grid-column-end: 12;
  }

  .xs\:col-end-13 {
    grid-column-end: 13;
  }

  .xs\:col-end-2 {
    grid-column-end: 2;
  }

  .xs\:col-end-3 {
    grid-column-end: 3;
  }

  .xs\:col-end-4 {
    grid-column-end: 4;
  }

  .xs\:col-end-5 {
    grid-column-end: 5;
  }

  .xs\:col-end-6 {
    grid-column-end: 6;
  }

  .xs\:col-end-7 {
    grid-column-end: 7;
  }

  .xs\:col-end-8 {
    grid-column-end: 8;
  }

  .xs\:col-end-9 {
    grid-column-end: 9;
  }
}

@media (width >= 640px) {
  .sm\:-top-1 {
    top: -.25rem;
  }

  .sm\:right-2 {
    right: .5rem;
  }

  .sm\:right-6 {
    right: 1.5rem;
  }

  .sm\:top-6 {
    top: 1.5rem;
  }

  .sm\:-col-start-1 {
    grid-column-start: -1;
  }

  .sm\:-col-start-10 {
    grid-column-start: -10;
  }

  .sm\:-col-start-11 {
    grid-column-start: -11;
  }

  .sm\:-col-start-12 {
    grid-column-start: -12;
  }

  .sm\:-col-start-13 {
    grid-column-start: -13;
  }

  .sm\:-col-start-2 {
    grid-column-start: -2;
  }

  .sm\:-col-start-3 {
    grid-column-start: -3;
  }

  .sm\:-col-start-4 {
    grid-column-start: -4;
  }

  .sm\:-col-start-5 {
    grid-column-start: -5;
  }

  .sm\:-col-start-6 {
    grid-column-start: -6;
  }

  .sm\:-col-start-7 {
    grid-column-start: -7;
  }

  .sm\:-col-start-8 {
    grid-column-start: -8;
  }

  .sm\:-col-start-9 {
    grid-column-start: -9;
  }

  .sm\:col-start-1 {
    grid-column-start: 1;
  }

  .sm\:col-start-10 {
    grid-column-start: 10;
  }

  .sm\:col-start-11 {
    grid-column-start: 11;
  }

  .sm\:col-start-12 {
    grid-column-start: 12;
  }

  .sm\:col-start-13 {
    grid-column-start: 13;
  }

  .sm\:col-start-2 {
    grid-column-start: 2;
  }

  .sm\:col-start-3 {
    grid-column-start: 3;
  }

  .sm\:col-start-4 {
    grid-column-start: 4;
  }

  .sm\:col-start-5 {
    grid-column-start: 5;
  }

  .sm\:col-start-6 {
    grid-column-start: 6;
  }

  .sm\:col-start-7 {
    grid-column-start: 7;
  }

  .sm\:col-start-8 {
    grid-column-start: 8;
  }

  .sm\:col-start-9 {
    grid-column-start: 9;
  }

  .sm\:-col-end-1 {
    grid-column-end: -1;
  }

  .sm\:-col-end-10 {
    grid-column-end: -10;
  }

  .sm\:-col-end-11 {
    grid-column-end: -11;
  }

  .sm\:-col-end-12 {
    grid-column-end: -12;
  }

  .sm\:-col-end-13 {
    grid-column-end: -13;
  }

  .sm\:-col-end-2 {
    grid-column-end: -2;
  }

  .sm\:-col-end-3 {
    grid-column-end: -3;
  }

  .sm\:-col-end-4 {
    grid-column-end: -4;
  }

  .sm\:-col-end-5 {
    grid-column-end: -5;
  }

  .sm\:-col-end-6 {
    grid-column-end: -6;
  }

  .sm\:-col-end-7 {
    grid-column-end: -7;
  }

  .sm\:-col-end-8 {
    grid-column-end: -8;
  }

  .sm\:-col-end-9 {
    grid-column-end: -9;
  }

  .sm\:col-end-1 {
    grid-column-end: 1;
  }

  .sm\:col-end-10 {
    grid-column-end: 10;
  }

  .sm\:col-end-11 {
    grid-column-end: 11;
  }

  .sm\:col-end-12 {
    grid-column-end: 12;
  }

  .sm\:col-end-13 {
    grid-column-end: 13;
  }

  .sm\:col-end-2 {
    grid-column-end: 2;
  }

  .sm\:col-end-3 {
    grid-column-end: 3;
  }

  .sm\:col-end-4 {
    grid-column-end: 4;
  }

  .sm\:col-end-5 {
    grid-column-end: 5;
  }

  .sm\:col-end-6 {
    grid-column-end: 6;
  }

  .sm\:col-end-7 {
    grid-column-end: 7;
  }

  .sm\:col-end-8 {
    grid-column-end: 8;
  }

  .sm\:col-end-9 {
    grid-column-end: 9;
  }

  .sm\:mx-2 {
    margin-left: .5rem;
    margin-right: .5rem;
  }

  .sm\:my-10 {
    margin-top: 2.5rem;
    margin-bottom: 2.5rem;
  }

  .sm\:my-20 {
    margin-top: 5rem;
    margin-bottom: 5rem;
  }

  .sm\:my-12 {
    margin-top: 3rem;
    margin-bottom: 3rem;
  }

  .sm\:mb-8 {
    margin-bottom: 2rem;
  }

  .sm\:mt-0 {
    margin-top: 0;
  }

  .sm\:mt-12 {
    margin-top: 3rem;
  }

  .sm\:mt-20 {
    margin-top: 5rem;
  }

  .sm\:mt-6 {
    margin-top: 1.5rem;
  }

  .sm\:block {
    display: block;
  }

  .sm\:inline-block {
    display: inline-block;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:h-18 {
    height: 4.5rem;
  }

  .sm\:h-full {
    height: 100%;
  }

  .sm\:w-1\/3 {
    width: 33.3333%;
  }

  .sm\:w-48 {
    width: 12rem;
  }

  .sm\:w-\[50vw\] {
    width: 50vw;
  }

  .sm\:w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }

  .sm\:w-full {
    width: 100%;
  }

  .sm\:min-w-min {
    min-width: -moz-min-content;
    min-width: min-content;
  }

  .sm\:max-w-\[560px\] {
    max-width: 560px;
  }

  .sm\:max-w-sm {
    max-width: 24rem;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:flex-col {
    flex-direction: column;
  }

  .sm\:items-end {
    align-items: flex-end;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-end {
    justify-content: flex-end;
  }

  .sm\:justify-center {
    justify-content: center;
  }

  .sm\:justify-between {
    justify-content: space-between;
  }

  .sm\:gap-2\.5 {
    gap: .625rem;
  }

  .sm\:gap-6 {
    gap: 1.5rem;
  }

  .sm\:gap-8 {
    gap: 2rem;
  }

  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(.5rem * var(--tw-space-x-reverse));
    margin-left: calc(.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(.75rem * var(--tw-space-x-reverse));
    margin-left: calc(.75rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .sm\:space-y-10 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(2.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(2.5rem * var(--tw-space-y-reverse));
  }

  .sm\:overflow-hidden {
    overflow: hidden;
  }

  .sm\:p-0 {
    padding: 0;
  }

  .sm\:p-10 {
    padding: 2.5rem;
  }

  .sm\:\!py-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .sm\:px-0 {
    padding-left: 0;
    padding-right: 0;
  }

  .sm\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .sm\:px-2 {
    padding-left: .5rem;
    padding-right: .5rem;
  }

  .sm\:px-20 {
    padding-left: 5rem;
    padding-right: 5rem;
  }

  .sm\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .sm\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .sm\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .sm\:pr-0 {
    padding-right: 0;
  }

  .sm\:pr-4 {
    padding-right: 1rem;
  }

  .sm\:pt-10 {
    padding-top: 2.5rem;
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:text-justify {
    text-align: justify;
  }

  .sm\:text-base {
    font-size: 1rem;
    line-height: 1.2;
  }

  .sm\:text-sm {
    font-size: .875rem;
    line-height: 1.2;
  }
}

@media (width >= 768px) {
  .md\:absolute {
    position: absolute;
  }

  .md\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:col-span-5 {
    grid-column: span 5 / span 5;
  }

  .md\:col-span-7 {
    grid-column: span 7 / span 7;
  }

  .md\:-col-start-1 {
    grid-column-start: -1;
  }

  .md\:-col-start-10 {
    grid-column-start: -10;
  }

  .md\:-col-start-11 {
    grid-column-start: -11;
  }

  .md\:-col-start-12 {
    grid-column-start: -12;
  }

  .md\:-col-start-13 {
    grid-column-start: -13;
  }

  .md\:-col-start-2 {
    grid-column-start: -2;
  }

  .md\:-col-start-3 {
    grid-column-start: -3;
  }

  .md\:-col-start-4 {
    grid-column-start: -4;
  }

  .md\:-col-start-5 {
    grid-column-start: -5;
  }

  .md\:-col-start-6 {
    grid-column-start: -6;
  }

  .md\:-col-start-7 {
    grid-column-start: -7;
  }

  .md\:-col-start-8 {
    grid-column-start: -8;
  }

  .md\:-col-start-9 {
    grid-column-start: -9;
  }

  .md\:col-start-1 {
    grid-column-start: 1;
  }

  .md\:col-start-10 {
    grid-column-start: 10;
  }

  .md\:col-start-11 {
    grid-column-start: 11;
  }

  .md\:col-start-12 {
    grid-column-start: 12;
  }

  .md\:col-start-13 {
    grid-column-start: 13;
  }

  .md\:col-start-2 {
    grid-column-start: 2;
  }

  .md\:col-start-3 {
    grid-column-start: 3;
  }

  .md\:col-start-4 {
    grid-column-start: 4;
  }

  .md\:col-start-5 {
    grid-column-start: 5;
  }

  .md\:col-start-6 {
    grid-column-start: 6;
  }

  .md\:col-start-7 {
    grid-column-start: 7;
  }

  .md\:col-start-8 {
    grid-column-start: 8;
  }

  .md\:col-start-9 {
    grid-column-start: 9;
  }

  .md\:-col-end-1 {
    grid-column-end: -1;
  }

  .md\:-col-end-10 {
    grid-column-end: -10;
  }

  .md\:-col-end-11 {
    grid-column-end: -11;
  }

  .md\:-col-end-12 {
    grid-column-end: -12;
  }

  .md\:-col-end-13 {
    grid-column-end: -13;
  }

  .md\:-col-end-2 {
    grid-column-end: -2;
  }

  .md\:-col-end-3 {
    grid-column-end: -3;
  }

  .md\:-col-end-4 {
    grid-column-end: -4;
  }

  .md\:-col-end-5 {
    grid-column-end: -5;
  }

  .md\:-col-end-6 {
    grid-column-end: -6;
  }

  .md\:-col-end-7 {
    grid-column-end: -7;
  }

  .md\:-col-end-8 {
    grid-column-end: -8;
  }

  .md\:-col-end-9 {
    grid-column-end: -9;
  }

  .md\:col-end-1 {
    grid-column-end: 1;
  }

  .md\:col-end-10 {
    grid-column-end: 10;
  }

  .md\:col-end-11 {
    grid-column-end: 11;
  }

  .md\:col-end-12 {
    grid-column-end: 12;
  }

  .md\:col-end-13 {
    grid-column-end: 13;
  }

  .md\:col-end-2 {
    grid-column-end: 2;
  }

  .md\:col-end-3 {
    grid-column-end: 3;
  }

  .md\:col-end-4 {
    grid-column-end: 4;
  }

  .md\:col-end-5 {
    grid-column-end: 5;
  }

  .md\:col-end-6 {
    grid-column-end: 6;
  }

  .md\:col-end-7 {
    grid-column-end: 7;
  }

  .md\:col-end-8 {
    grid-column-end: 8;
  }

  .md\:col-end-9 {
    grid-column-end: 9;
  }

  .md\:my-4 {
    margin-top: 1rem;
    margin-bottom: 1rem;
  }

  .md\:my-\[10vh\] {
    margin-top: 10vh;
    margin-bottom: 10vh;
  }

  .md\:mb-4 {
    margin-bottom: 1rem;
  }

  .md\:mt-0 {
    margin-top: 0;
  }

  .md\:mt-10 {
    margin-top: 2.5rem;
  }

  .md\:mt-15 {
    margin-top: 3.75rem;
  }

  .md\:mt-2 {
    margin-top: .5rem;
  }

  .md\:mt-32 {
    margin-top: 8rem;
  }

  .md\:line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .md\:block {
    display: block;
  }

  .md\:grid {
    display: grid;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-10 {
    height: 2.5rem;
  }

  .md\:h-12 {
    height: 3rem;
  }

  .md\:h-14 {
    height: 3.5rem;
  }

  .md\:h-18 {
    height: 4.5rem;
  }

  .md\:h-20 {
    height: 5rem;
  }

  .md\:h-3 {
    height: .75rem;
  }

  .md\:h-40 {
    height: 10rem;
  }

  .md\:h-80 {
    height: 20rem;
  }

  .md\:w-1\/2 {
    width: 50%;
  }

  .md\:w-1\/3 {
    width: 33.3333%;
  }

  .md\:w-10 {
    width: 2.5rem;
  }

  .md\:w-14 {
    width: 3.5rem;
  }

  .md\:w-18 {
    width: 4.5rem;
  }

  .md\:w-2\/3 {
    width: 66.6667%;
  }

  .md\:w-20 {
    width: 5rem;
  }

  .md\:w-3 {
    width: .75rem;
  }

  .md\:w-\[var\(--radix-navigation-menu-viewport-width\)\] {
    width: var(--radix-navigation-menu-viewport-width);
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }

  .md\:w-full {
    width: 100%;
  }

  .md\:min-w-\[420px\] {
    min-width: 420px;
  }

  .md\:max-w-\[40vw\] {
    max-width: 40vw;
  }

  .md\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:flex-wrap {
    flex-wrap: wrap;
  }

  .md\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:justify-end {
    justify-content: flex-end;
  }

  .md\:justify-between {
    justify-content: space-between;
  }

  .md\:gap-10 {
    gap: 2.5rem;
  }

  .md\:gap-20 {
    gap: 5rem;
  }

  .md\:gap-3 {
    gap: .75rem;
  }

  .md\:gap-32 {
    gap: 8rem;
  }

  .md\:gap-4 {
    gap: 1rem;
  }

  .md\:gap-6 {
    gap: 1.5rem;
  }

  .md\:gap-x-10 {
    -moz-column-gap: 2.5rem;
    column-gap: 2.5rem;
  }

  .md\:gap-x-12 {
    -moz-column-gap: 3rem;
    column-gap: 3rem;
  }

  .md\:gap-y-4 {
    row-gap: 1rem;
  }

  .md\:bg-zinc-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
  }

  .md\:p-0 {
    padding: 0;
  }

  .md\:p-10 {
    padding: 2.5rem;
  }

  .md\:p-4 {
    padding: 1rem;
  }

  .md\:p-6 {
    padding: 1.5rem;
  }

  .md\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .md\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .md\:px-16 {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .md\:px-2 {
    padding-left: .5rem;
    padding-right: .5rem;
  }

  .md\:px-3 {
    padding-left: .75rem;
    padding-right: .75rem;
  }

  .md\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .md\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .md\:py-2 {
    padding-top: .5rem;
    padding-bottom: .5rem;
  }

  .md\:pt-20 {
    padding-top: 5rem;
  }

  .md\:pt-\[70px\] {
    padding-top: 70px;
  }

  .md\:text-center {
    text-align: center;
  }

  .md\:text-2xl {
    font-size: 2rem;
    line-height: 1.2;
  }

  .md\:text-2xs {
    font-size: .625rem;
    line-height: 1.2;
  }

  .md\:text-4xl {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .md\:text-base {
    font-size: 1rem;
    line-height: 1.2;
  }

  .md\:text-lg {
    font-size: 1.5rem;
    line-height: 1.2;
  }

  .md\:text-md {
    font-size: 1.25rem;
    line-height: 1.2;
  }

  .md\:text-sm {
    font-size: .875rem;
    line-height: 1.2;
  }

  .md\:text-5xl {
    font-size: 3rem;
    line-height: 1.2;
  }

  .group:hover .md\:group-hover\:h-auto {
    height: auto;
  }

  .group:hover .md\:group-hover\:opacity-100 {
    opacity: 1;
  }
}

@media (width >= 1024px) {
  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .lg\:-col-start-1 {
    grid-column-start: -1;
  }

  .lg\:-col-start-10 {
    grid-column-start: -10;
  }

  .lg\:-col-start-11 {
    grid-column-start: -11;
  }

  .lg\:-col-start-12 {
    grid-column-start: -12;
  }

  .lg\:-col-start-13 {
    grid-column-start: -13;
  }

  .lg\:-col-start-2 {
    grid-column-start: -2;
  }

  .lg\:-col-start-3 {
    grid-column-start: -3;
  }

  .lg\:-col-start-4 {
    grid-column-start: -4;
  }

  .lg\:-col-start-5 {
    grid-column-start: -5;
  }

  .lg\:-col-start-6 {
    grid-column-start: -6;
  }

  .lg\:-col-start-7 {
    grid-column-start: -7;
  }

  .lg\:-col-start-8 {
    grid-column-start: -8;
  }

  .lg\:-col-start-9 {
    grid-column-start: -9;
  }

  .lg\:col-start-1 {
    grid-column-start: 1;
  }

  .lg\:col-start-10 {
    grid-column-start: 10;
  }

  .lg\:col-start-11 {
    grid-column-start: 11;
  }

  .lg\:col-start-12 {
    grid-column-start: 12;
  }

  .lg\:col-start-13 {
    grid-column-start: 13;
  }

  .lg\:col-start-2 {
    grid-column-start: 2;
  }

  .lg\:col-start-3 {
    grid-column-start: 3;
  }

  .lg\:col-start-4 {
    grid-column-start: 4;
  }

  .lg\:col-start-5 {
    grid-column-start: 5;
  }

  .lg\:col-start-6 {
    grid-column-start: 6;
  }

  .lg\:col-start-7 {
    grid-column-start: 7;
  }

  .lg\:col-start-8 {
    grid-column-start: 8;
  }

  .lg\:col-start-9 {
    grid-column-start: 9;
  }

  .lg\:-col-end-1 {
    grid-column-end: -1;
  }

  .lg\:-col-end-10 {
    grid-column-end: -10;
  }

  .lg\:-col-end-11 {
    grid-column-end: -11;
  }

  .lg\:-col-end-12 {
    grid-column-end: -12;
  }

  .lg\:-col-end-13 {
    grid-column-end: -13;
  }

  .lg\:-col-end-2 {
    grid-column-end: -2;
  }

  .lg\:-col-end-3 {
    grid-column-end: -3;
  }

  .lg\:-col-end-4 {
    grid-column-end: -4;
  }

  .lg\:-col-end-5 {
    grid-column-end: -5;
  }

  .lg\:-col-end-6 {
    grid-column-end: -6;
  }

  .lg\:-col-end-7 {
    grid-column-end: -7;
  }

  .lg\:-col-end-8 {
    grid-column-end: -8;
  }

  .lg\:-col-end-9 {
    grid-column-end: -9;
  }

  .lg\:col-end-1 {
    grid-column-end: 1;
  }

  .lg\:col-end-10 {
    grid-column-end: 10;
  }

  .lg\:col-end-11 {
    grid-column-end: 11;
  }

  .lg\:col-end-12 {
    grid-column-end: 12;
  }

  .lg\:col-end-13 {
    grid-column-end: 13;
  }

  .lg\:col-end-2 {
    grid-column-end: 2;
  }

  .lg\:col-end-3 {
    grid-column-end: 3;
  }

  .lg\:col-end-4 {
    grid-column-end: 4;
  }

  .lg\:col-end-5 {
    grid-column-end: 5;
  }

  .lg\:col-end-6 {
    grid-column-end: 6;
  }

  .lg\:col-end-7 {
    grid-column-end: 7;
  }

  .lg\:col-end-8 {
    grid-column-end: 8;
  }

  .lg\:col-end-9 {
    grid-column-end: 9;
  }

  .lg\:my-20 {
    margin-top: 5rem;
    margin-bottom: 5rem;
  }

  .lg\:mb-10 {
    margin-bottom: 2.5rem;
  }

  .lg\:ml-8 {
    margin-left: 2rem;
  }

  .lg\:mt-10 {
    margin-top: 2.5rem;
  }

  .lg\:mt-28 {
    margin-top: 7rem;
  }

  .lg\:block {
    display: block;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:grid {
    display: grid;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:h-10 {
    height: 2.5rem;
  }

  .lg\:h-12 {
    height: 3rem;
  }

  .lg\:h-14 {
    height: 3.5rem;
  }

  .lg\:h-18 {
    height: 4.5rem;
  }

  .lg\:h-24 {
    height: 6rem;
  }

  .lg\:h-28 {
    height: 7rem;
  }

  .lg\:h-30 {
    height: 7.5rem;
  }

  .lg\:h-34 {
    height: 8.5rem;
  }

  .lg\:h-40 {
    height: 10rem;
  }

  .lg\:h-96 {
    height: 24rem;
  }

  .lg\:h-\[560px\] {
    height: 560px;
  }

  .lg\:h-\[520px\] {
    height: 520px;
  }

  .lg\:w-10 {
    width: 2.5rem;
  }

  .lg\:w-14 {
    width: 3.5rem;
  }

  .lg\:w-18 {
    width: 4.5rem;
  }

  .lg\:w-28 {
    width: 7rem;
  }

  .lg\:w-30 {
    width: 7.5rem;
  }

  .lg\:w-34 {
    width: 8.5rem;
  }

  .lg\:w-40 {
    width: 10rem;
  }

  .lg\:w-full {
    width: 100%;
  }

  .lg\:max-w-\[500px\] {
    max-width: 500px;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:flex-col {
    flex-direction: column;
  }

  .lg\:justify-between {
    justify-content: space-between;
  }

  .lg\:gap-10 {
    gap: 2.5rem;
  }

  .lg\:gap-4 {
    gap: 1rem;
  }

  .lg\:gap-6 {
    gap: 1.5rem;
  }

  .lg\:gap-8 {
    gap: 2rem;
  }

  .lg\:gap-y-6 {
    row-gap: 1.5rem;
  }

  .lg\:\!p-6 {
    padding: 1.5rem !important;
  }

  .lg\:p-6 {
    padding: 1.5rem;
  }

  .lg\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .lg\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .lg\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .lg\:py-2 {
    padding-top: .5rem;
    padding-bottom: .5rem;
  }

  .lg\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .lg\:pt-16 {
    padding-top: 4rem;
  }

  .lg\:pt-3 {
    padding-top: .75rem;
  }

  .lg\:text-2xl {
    font-size: 2rem;
    line-height: 1.2;
  }

  .lg\:text-2xs {
    font-size: .625rem;
    line-height: 1.2;
  }

  .lg\:text-3xl {
    font-size: 2.25rem;
    line-height: 1.2;
  }

  .lg\:text-4xl {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .lg\:text-5xl {
    font-size: 3rem;
    line-height: 1.2;
  }

  .lg\:text-6xl {
    font-size: 3.5rem;
    line-height: 1.2;
  }

  .lg\:text-base {
    font-size: 1rem;
    line-height: 1.2;
  }

  .lg\:text-base18 {
    font-size: 1.125rem;
    line-height: 1.2;
  }

  .lg\:text-lg {
    font-size: 1.5rem;
    line-height: 1.2;
  }

  .lg\:text-md {
    font-size: 1.25rem;
    line-height: 1.2;
  }

  .lg\:text-sm {
    font-size: .875rem;
    line-height: 1.2;
  }

  .lg\:text-xl {
    font-size: 1.75rem;
    line-height: 1.2;
  }
}

@media (width >= 1280px) {
  .xl\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .xl\:col-span-9 {
    grid-column: span 9 / span 9;
  }

  .xl\:-col-start-1 {
    grid-column-start: -1;
  }

  .xl\:-col-start-10 {
    grid-column-start: -10;
  }

  .xl\:-col-start-11 {
    grid-column-start: -11;
  }

  .xl\:-col-start-12 {
    grid-column-start: -12;
  }

  .xl\:-col-start-13 {
    grid-column-start: -13;
  }

  .xl\:-col-start-2 {
    grid-column-start: -2;
  }

  .xl\:-col-start-3 {
    grid-column-start: -3;
  }

  .xl\:-col-start-4 {
    grid-column-start: -4;
  }

  .xl\:-col-start-5 {
    grid-column-start: -5;
  }

  .xl\:-col-start-6 {
    grid-column-start: -6;
  }

  .xl\:-col-start-7 {
    grid-column-start: -7;
  }

  .xl\:-col-start-8 {
    grid-column-start: -8;
  }

  .xl\:-col-start-9 {
    grid-column-start: -9;
  }

  .xl\:col-start-1 {
    grid-column-start: 1;
  }

  .xl\:col-start-10 {
    grid-column-start: 10;
  }

  .xl\:col-start-11 {
    grid-column-start: 11;
  }

  .xl\:col-start-12 {
    grid-column-start: 12;
  }

  .xl\:col-start-13 {
    grid-column-start: 13;
  }

  .xl\:col-start-2 {
    grid-column-start: 2;
  }

  .xl\:col-start-3 {
    grid-column-start: 3;
  }

  .xl\:col-start-4 {
    grid-column-start: 4;
  }

  .xl\:col-start-5 {
    grid-column-start: 5;
  }

  .xl\:col-start-6 {
    grid-column-start: 6;
  }

  .xl\:col-start-7 {
    grid-column-start: 7;
  }

  .xl\:col-start-8 {
    grid-column-start: 8;
  }

  .xl\:col-start-9 {
    grid-column-start: 9;
  }

  .xl\:-col-end-1 {
    grid-column-end: -1;
  }

  .xl\:-col-end-10 {
    grid-column-end: -10;
  }

  .xl\:-col-end-11 {
    grid-column-end: -11;
  }

  .xl\:-col-end-12 {
    grid-column-end: -12;
  }

  .xl\:-col-end-13 {
    grid-column-end: -13;
  }

  .xl\:-col-end-2 {
    grid-column-end: -2;
  }

  .xl\:-col-end-3 {
    grid-column-end: -3;
  }

  .xl\:-col-end-4 {
    grid-column-end: -4;
  }

  .xl\:-col-end-5 {
    grid-column-end: -5;
  }

  .xl\:-col-end-6 {
    grid-column-end: -6;
  }

  .xl\:-col-end-7 {
    grid-column-end: -7;
  }

  .xl\:-col-end-8 {
    grid-column-end: -8;
  }

  .xl\:-col-end-9 {
    grid-column-end: -9;
  }

  .xl\:col-end-1 {
    grid-column-end: 1;
  }

  .xl\:col-end-10 {
    grid-column-end: 10;
  }

  .xl\:col-end-11 {
    grid-column-end: 11;
  }

  .xl\:col-end-12 {
    grid-column-end: 12;
  }

  .xl\:col-end-13 {
    grid-column-end: 13;
  }

  .xl\:col-end-2 {
    grid-column-end: 2;
  }

  .xl\:col-end-3 {
    grid-column-end: 3;
  }

  .xl\:col-end-4 {
    grid-column-end: 4;
  }

  .xl\:col-end-5 {
    grid-column-end: 5;
  }

  .xl\:col-end-6 {
    grid-column-end: 6;
  }

  .xl\:col-end-7 {
    grid-column-end: 7;
  }

  .xl\:col-end-8 {
    grid-column-end: 8;
  }

  .xl\:col-end-9 {
    grid-column-end: 9;
  }

  .xl\:mb-20 {
    margin-bottom: 5rem;
  }

  .xl\:mt-32 {
    margin-top: 8rem;
  }

  .xl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .xl\:gap-10 {
    gap: 2.5rem;
  }

  .xl\:gap-12 {
    gap: 3rem;
  }

  .xl\:gap-4 {
    gap: 1rem;
  }

  .xl\:text-2xl {
    font-size: 2rem;
    line-height: 1.2;
  }

  .xl\:text-3xl {
    font-size: 2.25rem;
    line-height: 1.2;
  }

  .xl\:text-4xl {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .xl\:text-5xl {
    font-size: 3rem;
    line-height: 1.2;
  }

  .xl\:text-6xl {
    font-size: 3.5rem;
    line-height: 1.2;
  }

  .xl\:text-7xl {
    font-size: 4rem;
    line-height: 1.2;
  }

  .xl\:text-base {
    font-size: 1rem;
    line-height: 1.2;
  }

  .xl\:text-base18 {
    font-size: 1.125rem;
    line-height: 1.2;
  }

  .xl\:text-lg {
    font-size: 1.5rem;
    line-height: 1.2;
  }

  .xl\:text-md {
    font-size: 1.25rem;
    line-height: 1.2;
  }

  .xl\:text-sm {
    font-size: .875rem;
    line-height: 1.2;
  }

  .xl\:text-xl {
    font-size: 1.75rem;
    line-height: 1.2;
  }

  .xl\:text-xs {
    font-size: .75rem;
    line-height: 1.2;
  }
}

@media (width >= 1440px) {
  .\32 xl\:flex {
    display: flex;
  }

  .\32 xl\:h-\[520px\] {
    height: 520px;
  }

  .\32 xl\:w-1\/4 {
    width: 25%;
  }

  .\32 xl\:max-w-\[400px\] {
    max-width: 400px;
  }

  .\32 xl\:flex-1 {
    flex: 1;
  }

  .\32 xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .\32 xl\:gap-4 {
    gap: 1rem;
  }

  .\32 xl\:px-20 {
    padding-left: 5rem;
    padding-right: 5rem;
  }
}

@media (width >= 1920px) {
  .\33 xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

.\[\&\:\:--webkit-search-cancel-button\]\:hidden::--webkit-search-cancel-button {
  display: none;
}

.\[\&\:\:-webkit-inner-spin-button\]\:\[-webkit-appearance\:none\]::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

.\[\&\:\:-webkit-outer-spin-button\]\:\[-webkit-appearance\:none\]::-webkit-outer-spin-button {
  -webkit-appearance: none;
}

.\[\&\:\:-webkit-search-cancel-button\]\:hidden::-webkit-search-cancel-button {
  display: none;
}

.\[\&\:\:-webkit-search-decoration\]\:hidden::-webkit-search-decoration {
  display: none;
}

.\[\&\:has\(\>\.day-range-end\)\]\:rounded-r-md:has( > .day-range-end) {
  border-top-right-radius: calc(var(--radius)  - 2px);
  border-bottom-right-radius: calc(var(--radius)  - 2px);
}

.\[\&\:has\(\>\.day-range-start\)\]\:rounded-l-md:has( > .day-range-start) {
  border-top-left-radius: calc(var(--radius)  - 2px);
  border-bottom-left-radius: calc(var(--radius)  - 2px);
}

.\[\&\:has\(\[aria-selected\]\)\]\:rounded-md:has([aria-selected]) {
  border-radius: calc(var(--radius)  - 2px);
}

.\[\&\:has\(\[aria-selected\]\)\]\:bg-accent:has([aria-selected]) {
  background-color: hsl(var(--accent));
}

.first\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-l-md:has([aria-selected]):first-child {
  border-top-left-radius: calc(var(--radius)  - 2px);
  border-bottom-left-radius: calc(var(--radius)  - 2px);
}

.last\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-r-md:has([aria-selected]):last-child {
  border-top-right-radius: calc(var(--radius)  - 2px);
  border-bottom-right-radius: calc(var(--radius)  - 2px);
}

.\[\&\:has\(\[aria-selected\]\.day-outside\)\]\:bg-accent\/50:has([aria-selected].day-outside) {
  background-color: hsl(var(--accent) / .5);
}

.\[\&\:has\(\[aria-selected\]\.day-range-end\)\]\:rounded-r-md:has([aria-selected].day-range-end) {
  border-top-right-radius: calc(var(--radius)  - 2px);
  border-bottom-right-radius: calc(var(--radius)  - 2px);
}

.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role="checkbox"]) {
  padding-right: 0;
}

.\[\&\>\*\]\:flex > * {
  display: flex;
}

.\[\&\>\*\]\:items-center > * {
  align-items: center;
}

.\[\&\>\*\]\:justify-center > * {
  justify-content: center;
}

.\[\&\>\[role\=checkbox\]\]\:translate-y-\[2px\] > [role="checkbox"] {
  --tw-translate-y: 2px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&\>a\]\:bg-transparent > a {
  background-color: #0000;
}

.\[\&\>button\]\:hidden > button {
  display: none;
}

.\[\&\>button\]\:bg-transparent > button {
  background-color: #0000;
}

.\[\&\>code\]\:mx-2 > code {
  margin-left: .5rem;
  margin-right: .5rem;
}

.\[\&\>code\]\:text-ui-contrast-fg-primary > code {
  color: var(--contrast-fg-primary);
}

.\[\&\>span\]\:line-clamp-1 > span {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.\[\&\>svg\]\:size-4 > svg {
  width: 1rem;
  height: 1rem;
}

.\[\&\>svg\]\:h-3\.5 > svg {
  height: .875rem;
}

.\[\&\>svg\]\:w-3\.5 > svg {
  width: .875rem;
}

.\[\&\>svg\]\:shrink-0 > svg {
  flex-shrink: 0;
}

.\[\&\>svg\]\:fill-\[\#344054\] > svg {
  fill: #344054;
}

.\[\&\>svg\]\:stroke-\[\#344054\] > svg {
  stroke: #344054;
}

.\[\&\>svg\]\:stroke-white > svg {
  stroke: #fff;
}

.\[\&\>svg\]\:text-ui-fg-subtle > svg {
  color: var(--fg-subtle);
}

.\[\&\>svg\]\:hover\:\!fill-primary-lighter:hover > svg {
  fill: #eefbe6 !important;
}

.\[\&\>svg\]\:hover\:\!stroke-primary-lighter:hover > svg {
  stroke: #eefbe6 !important;
}

.\[\&\>svg\]\:hover\:\!stroke-primary-main:hover > svg {
  stroke: #59b71f !important;
}

.\[\&\>tr\]\:last\:border-b-0:last-child > tr {
  border-bottom-width: 0;
}

.\[\&\>ul\]\:rounded-none > ul {
  border-radius: 0;
}

.\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state="open"] > svg {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading] {
  padding-left: .5rem;
  padding-right: .5rem;
}

.\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading] {
  padding-top: .375rem;
  padding-bottom: .375rem;
}

.\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading] {
  font-size: .75rem;
  line-height: 1.2;
}

.\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading] {
  font-weight: 500;
}

.\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading] {
  color: hsl(var(--muted-foreground));
}

.\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden]) ~ [cmdk-group] {
  padding-top: 0;
}

.\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group] {
  padding-left: .5rem;
  padding-right: .5rem;
}

.\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg {
  height: 1.25rem;
}

.\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg {
  width: 1.25rem;
}

.\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input] {
  height: 3rem;
}

.\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item] {
  padding-left: .5rem;
  padding-right: .5rem;
}

.\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item] {
  padding-top: .75rem;
  padding-bottom: .75rem;
}

.\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg {
  height: 1.25rem;
}

.\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg {
  width: 1.25rem;
}

.\[\&_div\]\:h-2 div {
  height: .5rem;
}

.\[\&_div\]\:w-2 div {
  width: .5rem;
}

.\[\&_div\]\:rounded-sm div {
  border-radius: calc(var(--radius)  - 4px);
}

.\[\&_div\]\:bg-ui-tag-blue-icon div {
  background-color: var(--tag-blue-icon);
}

.\[\&_div\]\:bg-ui-tag-green-icon div {
  background-color: var(--tag-green-icon);
}

.\[\&_div\]\:bg-ui-tag-neutral-icon div {
  background-color: var(--tag-neutral-icon);
}

.\[\&_div\]\:bg-ui-tag-orange-icon div {
  background-color: var(--tag-orange-icon);
}

.\[\&_div\]\:bg-ui-tag-purple-icon div {
  background-color: var(--tag-purple-icon);
}

.\[\&_div\]\:bg-ui-tag-red-icon div {
  background-color: var(--tag-red-icon);
}

.\[\&_path\]\:shadow-details-contrast-on-bg-interactive path {
  --tw-shadow: var(--details-contrast-on-bg-interactive);
  --tw-shadow-colored: var(--details-contrast-on-bg-interactive);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.\[\&_svg\]\:pointer-events-none svg {
  pointer-events: none;
}

.\[\&_svg\]\:size-4 svg {
  width: 1rem;
  height: 1rem;
}

.\[\&_svg\]\:shrink-0 svg {
  flex-shrink: 0;
}

.\[\&_svg\]\:text-ui-tag-blue-icon svg {
  color: var(--tag-blue-icon);
}

.\[\&_svg\]\:text-ui-tag-green-icon svg {
  color: var(--tag-green-icon);
}

.\[\&_svg\]\:text-ui-tag-neutral-icon svg {
  color: var(--tag-neutral-icon);
}

.\[\&_svg\]\:text-ui-tag-orange-icon svg {
  color: var(--tag-orange-icon);
}

.\[\&_svg\]\:text-ui-tag-purple-icon svg {
  color: var(--tag-purple-icon);
}

.\[\&_svg\]\:text-ui-tag-red-icon svg {
  color: var(--tag-red-icon);
}

.\[\&_td\:first-child\]\:pl-6 td:first-child {
  padding-left: 1.5rem;
}

.\[\&_td\:last-child\]\:pr-6 td:last-child {
  padding-right: 1.5rem;
}

.\[\&_th\:first-child\]\:pl-6 th:first-child {
  padding-left: 1.5rem;
}

.\[\&_th\:first-of-type\]\:w-\[1\%\] th:first-of-type {
  width: 1%;
}

.\[\&_th\:first-of-type\]\:whitespace-nowrap th:first-of-type {
  white-space: nowrap;
}

.\[\&_th\:last-child\]\:pr-6 th:last-child {
  padding-right: 1.5rem;
}

.\[\&_th\:last-of-type\]\:w-\[1\%\] th:last-of-type {
  width: 1%;
}

.\[\&_th\:last-of-type\]\:whitespace-nowrap th:last-of-type {
  white-space: nowrap;
}

.\[\&_tr\:last-child\]\:border-0 tr:last-child {
  border-width: 0;
}

.\[\&_tr\]\:border-b tr {
  border-bottom-width: 1px;
}

.\[\&_tr\]\:bg-ui-bg-subtle tr {
  background-color: var(--bg-subtle);
}

.\[\&_tr\]\:hover\:bg-ui-bg-subtle:hover tr {
  background-color: var(--bg-subtle);
}

/*# sourceMappingURL=src_styles_globals_b52d8e.css.map*/