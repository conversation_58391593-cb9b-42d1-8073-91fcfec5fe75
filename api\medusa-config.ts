import { defineConfig, loadEnv } from "@medusajs/framework/utils";

loadEnv(process.env.NODE_ENV || "development", process.cwd());

const development = process.env.SERVER_ENV === "development";

const defaultModules = [
  {
    resolve: "./src/modules/cms",
    options: null,
  },
  {
    resolve: "./src/modules/navigation",
    options: null,
  },
  {
    resolve: "./src/modules/layout",
    options: null,
  },
  {
    resolve: "./src/modules/contact-form",
    options: null,
  },
  {
    resolve: "./src/modules/product-variant-image",
    options: null,
  },
  {
    resolve: "./src/modules/product-option-image",
    options: null,
  },
  {
    resolve: "./src/modules/product-review",
    options: null,
  },
  {
    resolve: "./src/modules/product-mix-match",
    options: null,
  },
  {
    resolve: "./src/modules/wishlist",
    options: null,
  },
  {
    resolve: "@medusajs/medusa/cache-redis",
    options: {
      redisUrl: process.env.REDIS_URL,
    },
  },
  {
    resolve: "@medusajs/medusa/event-bus-redis",
    options: {
      redisUrl: process.env.REDIS_URL,
    },
  },
  {
    resolve: "@medusajs/medusa/workflow-engine-redis",
    options: {
      redis: {
        url: process.env.REDIS_URL,
      },
    },
  },
  {
    resolve: "@medusajs/medusa/notification",
    options: {
      providers: [
        {
          resolve: "@medusajs/medusa/notification-sendgrid",
          id: "sendgrid",
          options: {
            channels: ["email"],
            api_key: process.env.SENDGRID_API_KEY,
            from: `${process.env.SENDGRID_NAME}<${process.env.SENDGRID_FROM}>`,
          },
        },
        {
          resolve: "@medusajs/notification-local",
          id: "local",
          options: {
            name: "Local Notification Provider",
            channels: ["feed"],
          },
        },
      ],
    },
  },
  // {
  //   resolve: "@medusajs/medusa/payment",
  //   options: {
  //     providers: [
  //       {
  //         resolve: "./src/modules/payos-payment",
  //         id: "payos",
  //         options: {
  //           apiKey: process.env.PAYOS_API_KEY,
  //           clientId: process.env.PAYOS_CLIENT_ID,
  //           checksumKey: process.env.PAYOS_CHECKSUM_KEY,
  //           cancelUrl: process.env.PAYOS_CANCEL_URL,
  //           returnUrl: process.env.PAYOS_RETURN_URL,
  //           paymentDescription: "Payment for your order",
  //         },
  //       },
  //     ],
  //   },
  // },
  {
    resolve: "./src/modules/languages",
    options: {
      defaultLanguage: "en",
      availableLanguages: [
        { label: "English", tag: "en" },
        { label: "Dutch", tag: "nl" },
      ],
      productsKeys: ["title", "subtitle", "description"],
      collectionsKeys: ["title"],
      productCategoriesKeys: ["title", "description"],
    },
  },
];

const s3Module = {
  resolve: "@medusajs/medusa/file",
  options: {
    providers: [
      {
        resolve: "@medusajs/medusa/file-s3",
        id: "s3",
        options: {
          file_url: process.env.SPACE_URL,
          access_key_id: process.env.SPACE_ACCESS_KEY_ID,
          secret_access_key: process.env.SPACE_SECRET_ACCESS_KEY,
          region: process.env.SPACE_REGION,
          bucket: process.env.SPACE_BUCKET,
          endpoint: process.env.SPACE_ENDPOINT,
          download_file_duration: 3600,
        },
      },
    ],
  },
};

const plugins = [
  {
    resolve: "@rokmohar/medusa-plugin-meilisearch",
    options: {
      config: {
        host: process.env.MEILISEARCH_HOST ?? "",
        apiKey: process.env.MEILISEARCH_API_KEY ?? "",
      },
      settings: {
        products: {
          indexSettings: {
            filterableAttributes: [
              "category_handles",
              "categories",
              "variant_prices",
              "variants",
              "variants.prices",
              "variant_title",
              "variant_sku",
            ],
            searchableAttributes: [
              "title",
              "description",
              "variant_sku",
              "categories",
              "category_handles",
              "variant_title",
            ],
            displayedAttributes: ["*"],
            sortableAttributes: [
              "created_at",
              "updated_at",
              "variant_prices.*.min",
              "variant_prices.*.max",
            ],
          },
          primaryKey: "id",
          // Create your own transformer
          /*transformer: (product) => ({
            id: product.id,
            // other attributes...
          }),*/
        },
      },
    },
  },
];

const modules = development
  ? [...defaultModules]
  : [...defaultModules, s3Module];

module.exports = defineConfig({
  admin: {
    backendUrl: process.env.BACKEND_URL ?? "http://localhost:9000",
    storefrontUrl: process.env.STOREFRONT_URL,
  },
  projectConfig: {
    databaseUrl: process.env.DATABASE_URL,
    redisUrl: process.env.REDIS_URL,
    workerMode: process.env.WORKER_MODE as "shared" | "worker" | "server",
    http: {
      storeCors: process.env.STORE_CORS!,
      adminCors: process.env.ADMIN_CORS!,
      authCors: process.env.AUTH_CORS!,
      jwtSecret: process.env.JWT_SECRET || "supersecret",
      cookieSecret: process.env.COOKIE_SECRET || "supersecret",
    },
  },
  modules,
  plugins,
});
