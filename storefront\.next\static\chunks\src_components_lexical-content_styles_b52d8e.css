/* [project]/src/components/lexical-content/styles.css [app-client] (css) */
#lexical-wrapper .editor-shell {
  margin: 20px auto;
  border-radius: 8px;
  max-width: 1100px;
  color: #000;
  position: relative;
  line-height: 1.7;
  font-weight: 400;
  border: 1px solid #ddd;
  padding: 8px;
}

#lexical-wrapper .editor-shell .editor-container {
  background: #fff;
  position: relative;
  display: block;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

#lexical-wrapper .editor-shell .editor-container.tree-view {
  border-radius: 0;
}

#lexical-wrapper .editor-shell .editor-container.plain-text {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

#lexical-wrapper .editor-scroller {
  min-height: 150px;
  border: 0;
  display: flex;
  position: relative;
  outline: 0;
  z-index: 0;
  overflow: auto;
  resize: vertical;
}

#lexical-wrapper .editor {
  flex: auto;
  position: relative;
  resize: vertical;
  z-index: -1;
}

#lexical-wrapper .test-recorder-output {
  margin: 20px auto;
  width: 100%;
}

#lexical-wrapper pre {
  line-height: 1.1;
  background: #222;
  color: #fff;
  margin: 0;
  padding: 10px;
  font-size: 12px;
  overflow: auto;
  max-height: 400px;
}

#lexical-wrapper .tree-view-output {
  display: block;
  background: #222;
  color: #fff;
  padding: 0;
  font-size: 12px;
  margin: 1px auto 10px;
  position: relative;
  overflow: hidden;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

#lexical-wrapper pre::-webkit-scrollbar {
  background: none;
  width: 10px;
}

#lexical-wrapper pre::-webkit-scrollbar-thumb {
  background: #999;
}

#lexical-wrapper .editor-dev-button {
  position: relative;
  display: block;
  width: 40px;
  height: 40px;
  font-size: 12px;
  border-radius: 20px;
  border: none;
  cursor: pointer;
  outline: none;
  box-shadow: 0 1px 10px #0000004d;
  background-color: #444;
}

#lexical-wrapper .editor-dev-button:after {
  content: "";
  position: absolute;
  top: 10px;
  right: 10px;
  bottom: 10px;
  left: 10px;
  display: block;
  background-size: contain;
  filter: invert();
}

#lexical-wrapper .editor-dev-button:hover {
  background-color: #555;
}

#lexical-wrapper .editor-dev-button.active {
  background-color: #e92323;
}

#lexical-wrapper .test-recorder-toolbar {
  display: flex;
}

#lexical-wrapper .test-recorder-button {
  position: relative;
  display: block;
  width: 32px;
  height: 32px;
  font-size: 10px;
  padding: 6px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  outline: none;
  box-shadow: 1px 2px 2px #0006;
  background-color: #222;
  transition: box-shadow 50ms ease-out;
}

#lexical-wrapper .test-recorder-button:active {
  box-shadow: 1px 2px 4px #0006;
}

#lexical-wrapper .test-recorder-button + .test-recorder-button {
  margin-left: 4px;
}

#lexical-wrapper .test-recorder-button:after {
  content: "";
  position: absolute;
  top: 8px;
  right: 8px;
  bottom: 8px;
  left: 8px;
  display: block;
  background-size: contain;
  filter: invert();
}

#lexical-wrapper #options-button {
  position: fixed;
  left: 20px;
  bottom: 20px;
}

#lexical-wrapper #test-recorder-button {
  position: fixed;
  left: 70px;
  bottom: 20px;
}

#lexical-wrapper #paste-log-button {
  position: fixed;
  left: 120px;
  bottom: 20px;
}

#lexical-wrapper #docs-button {
  position: fixed;
  left: 170px;
  bottom: 20px;
}

#lexical-wrapper #options-button:after {
  background-image: url("/public/lexical/icons/gear.svg");
}

#lexical-wrapper #test-recorder-button:after {
  background-image: url("/public/lexical/icons/journal-code.svg");
}

#lexical-wrapper #paste-log-button:after {
  background-image: url("/public/lexical/icons/clipboard.svg");
}

#lexical-wrapper #docs-button:after {
  background-image: url("/public/lexical/icons/file-earmark-text.svg");
}

#lexical-wrapper #test-recorder-button-snapshot {
  margin-right: auto;
}

#lexical-wrapper #test-recorder-button-snapshot:after {
  background-image: url("/public/lexical/icons/camera.svg");
}

#lexical-wrapper #test-recorder-button-copy:after {
  background-image: url("/public/lexical/icons/clipboard.svg");
}

#lexical-wrapper #test-recorder-button-download:after {
  background-image: url("/public/lexical/icons/download.svg");
}

#lexical-wrapper .typeahead-popover {
  background: #fff;
  box-shadow: 0 5px 10px #0000004d;
  border-radius: 8px;
  position: fixed;
}

#lexical-wrapper .typeahead-popover ul {
  padding: 0;
  list-style: none;
  margin: 0;
  border-radius: 8px;
  max-height: 200px;
  overflow-y: scroll;
}

#lexical-wrapper .typeahead-popover ul::-webkit-scrollbar {
  display: none;
}

#lexical-wrapper .typeahead-popover ul {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

#lexical-wrapper .typeahead-popover ul li {
  margin: 0;
  min-width: 180px;
  font-size: 14px;
  outline: none;
  cursor: pointer;
  border-radius: 8px;
}

#lexical-wrapper .typeahead-popover ul li.selected {
  background: #eee;
}

#lexical-wrapper .typeahead-popover li {
  margin: 0 8px;
  padding: 8px;
  color: #050505;
  cursor: pointer;
  line-height: 16px;
  font-size: 15px;
  display: flex;
  align-content: center;
  flex-direction: row;
  flex-shrink: 0;
  background-color: #fff;
  border-radius: 8px;
  border: 0;
}

#lexical-wrapper .typeahead-popover li.active {
  display: flex;
  width: 20px;
  height: 20px;
  background-size: contain;
}

#lexical-wrapper .typeahead-popover li:first-child {
  border-radius: 8px 8px 0 0;
}

#lexical-wrapper .typeahead-popover li:last-child {
  border-radius: 0 0 8px 8px;
}

#lexical-wrapper .typeahead-popover li:hover {
  background-color: #eee;
}

#lexical-wrapper .typeahead-popover li .text {
  display: flex;
  line-height: 20px;
  flex-grow: 1;
  min-width: 150px;
}

#lexical-wrapper .typeahead-popover li .icon {
  display: flex;
  width: 20px;
  height: 20px;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  margin-right: 8px;
  line-height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

#lexical-wrapper .component-picker-menu {
  width: 200px;
}

#lexical-wrapper .mentions-menu {
  width: 250px;
}

#lexical-wrapper .auto-embed-menu {
  width: 150px;
}

#lexical-wrapper .emoji-menu {
  width: 200px;
}

#lexical-wrapper i.palette {
  background-image: url("/public/lexical/icons/palette.svg");
}

#lexical-wrapper i.bucket {
  background-image: url("/public/lexical/icons/paint-bucket.svg");
}

#lexical-wrapper i.bold {
  background-image: url("/public/lexical/icons/type-bold.svg");
}

#lexical-wrapper i.italic {
  background-image: url("/public/lexical/icons/type-italic.svg");
}

#lexical-wrapper i.clear {
  background-image: url("/public/lexical/icons/trash.svg");
}

#lexical-wrapper i.code {
  background-image: url("/public/lexical/icons/code.svg");
}

#lexical-wrapper i.underline {
  background-image: url("/public/lexical/icons/type-underline.svg");
}

#lexical-wrapper i.strikethrough {
  background-image: url("/public/lexical/icons/type-strikethrough.svg");
}

#lexical-wrapper i.subscript {
  background-image: url("/public/lexical/icons/type-subscript.svg");
}

#lexical-wrapper i.superscript {
  background-image: url("/public/lexical/icons/type-superscript.svg");
}

#lexical-wrapper i.link {
  background-image: url("/public/lexical/icons/link.svg");
}

#lexical-wrapper i.horizontal-rule {
  background-image: url("/public/lexical/icons/horizontal-rule.svg");
}

#lexical-wrapper .icon.plus {
  background-image: url("/public/lexical/icons/plus.svg");
}

#lexical-wrapper .icon.caret-right {
  background-image: url("/public/lexical/icons/caret-right-fill.svg");
}

#lexical-wrapper .icon.dropdown-more {
  background-image: url("/public/lexical/icons/dropdown-more.svg");
}

#lexical-wrapper .icon.font-color {
  background-image: url("/public/lexical/icons/font-color.svg");
}

#lexical-wrapper .icon.font-family {
  background-image: url("/public/lexical/icons/font-family.svg");
}

#lexical-wrapper .icon.bg-color {
  background-image: url("/public/lexical/icons/bg-color.svg");
}

#lexical-wrapper .icon.table {
  background-color: #6c757d;
  mask-image: url("/public/lexical/icons/table.svg");
  -webkit-mask-image: url("/public/lexical/icons/table.svg");
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  mask-size: contain;
  -webkit-mask-size: contain;
}

#lexical-wrapper i.image {
  background-image: url("/public/lexical/icons/file-image.svg");
}

#lexical-wrapper i.table {
  background-image: url("/public/lexical/icons/table.svg");
}

#lexical-wrapper i.close {
  background-image: url("/public/lexical/icons/close.svg");
}

#lexical-wrapper i.figma {
  background-image: url("/public/lexical/icons/figma.svg");
}

#lexical-wrapper i.poll {
  background-image: url("/public/lexical/icons/card-checklist.svg");
}

#lexical-wrapper i.columns {
  background-image: url("/public/lexical/icons/3-columns.svg");
}

#lexical-wrapper i.tweet {
  background-image: url("/public/lexical/icons/tweet.svg");
}

#lexical-wrapper i.youtube {
  background-image: url("/public/lexical/icons/youtube.svg");
}

#lexical-wrapper .icon.left-align, #lexical-wrapper i.left-align {
  background-image: url("/public/lexical/icons/text-left.svg");
}

#lexical-wrapper .icon.center-align, #lexical-wrapper i.center-align {
  background-image: url("/public/lexical/icons/text-center.svg");
}

#lexical-wrapper .icon.right-align, #lexical-wrapper i.right-align {
  background-image: url("/public/lexical/icons/text-right.svg");
}

#lexical-wrapper .icon.justify-align, #lexical-wrapper i.justify-align {
  background-image: url("/public/lexical/icons/justify.svg");
}

#lexical-wrapper i.indent {
  background-image: url("/public/lexical/icons/indent.svg");
}

#lexical-wrapper i.markdown {
  background-image: url("/public/lexical/icons/markdown.svg");
}

#lexical-wrapper i.outdent {
  background-image: url("/public/lexical/icons/outdent.svg");
}

#lexical-wrapper i.undo {
  background-image: url("/public/lexical/icons/arrow-counterclockwise.svg");
}

#lexical-wrapper i.redo {
  background-image: url("/public/lexical/icons/arrow-clockwise.svg");
}

#lexical-wrapper i.sticky {
  background-image: url("/public/lexical/icons/sticky.svg");
}

#lexical-wrapper i.mic {
  background-image: url("/public/lexical/icons/mic.svg");
}

#lexical-wrapper i.import {
  background-image: url("/public/lexical/icons/upload.svg");
}

#lexical-wrapper i.export {
  background-image: url("/public/lexical/icons/download.svg");
}

#lexical-wrapper i.share {
  background-image: url("/public/lexical/icons/send.svg");
}

#lexical-wrapper i.diagram-2 {
  background-image: url("/public/lexical/icons/diagram-2.svg");
}

#lexical-wrapper i.user {
  background-image: url("/public/lexical/icons/user.svg");
}

#lexical-wrapper i.equation {
  background-image: url("/public/lexical/icons/plus-slash-minus.svg");
}

#lexical-wrapper i.gif {
  background-image: url("/public/lexical/icons/filetype-gif.svg");
}

#lexical-wrapper i.copy {
  background-image: url("/public/lexical/icons/copy.svg");
}

#lexical-wrapper i.success {
  background-image: url("/public/lexical/icons/success.svg");
}

#lexical-wrapper i.prettier {
  background-image: url("/public/lexical/icons/prettier.svg");
}

#lexical-wrapper i.prettier-error {
  background-image: url("/public/lexical/icons/prettier-error.svg");
}

#lexical-wrapper i.page-break, #lexical-wrapper .icon.page-break {
  background-image: url("/public/lexical/icons/scissors.svg");
}

#lexical-wrapper .link-editor .button.active, #lexical-wrapper .toolbar .button.active {
  background-color: #dfe8fa;
}

#lexical-wrapper .link-editor .link-input {
  display: block;
  width: calc(100% - 75px);
  box-sizing: border-box;
  margin: 12px;
  padding: 8px 12px;
  border-radius: 15px;
  background-color: #eee;
  font-size: 15px;
  color: #050505;
  border: 0;
  outline: 0;
  position: relative;
  font-family: inherit;
}

#lexical-wrapper .link-editor .link-view {
  display: block;
  width: calc(100% - 24px);
  margin: 8px 12px;
  padding: 8px 12px;
  border-radius: 15px;
  font-size: 15px;
  color: #050505;
  border: 0;
  outline: 0;
  position: relative;
  font-family: inherit;
}

#lexical-wrapper .link-editor .link-view a {
  display: block;
  word-break: break-word;
  width: calc(100% - 33px);
}

#lexical-wrapper .link-editor div.link-edit {
  background-image: url("/public/lexical/icons/pencil-fill.svg");
  background-size: 16px;
  background-position: center;
  background-repeat: no-repeat;
  width: 35px;
  vertical-align: -.25em;
  position: absolute;
  right: 30px;
  top: 0;
  bottom: 0;
  cursor: pointer;
}

#lexical-wrapper .link-editor div.link-trash {
  background-image: url("/public/lexical/icons/trash.svg");
  background-size: 16px;
  background-position: center;
  background-repeat: no-repeat;
  width: 35px;
  vertical-align: -.25em;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  cursor: pointer;
}

#lexical-wrapper .link-editor div.link-cancel {
  background-image: url("/public/lexical/icons/close.svg");
  background-size: 16px;
  background-position: center;
  background-repeat: no-repeat;
  width: 35px;
  vertical-align: -.25em;
  margin-right: 28px;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  cursor: pointer;
}

#lexical-wrapper .link-editor div.link-confirm {
  background-image: url("/public/lexical/icons/success-alt.svg");
  background-size: 16px;
  background-position: center;
  background-repeat: no-repeat;
  width: 35px;
  vertical-align: -.25em;
  margin-right: 2px;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  cursor: pointer;
}

#lexical-wrapper .link-editor .link-input a {
  color: #216fdb;
  text-decoration: underline;
  white-space: nowrap;
  overflow: hidden;
  margin-right: 30px;
  text-overflow: ellipsis;
}

#lexical-wrapper .link-editor .link-input a:hover {
  text-decoration: underline;
}

#lexical-wrapper .link-editor .font-size-wrapper, #lexical-wrapper .link-editor .font-family-wrapper {
  display: flex;
  margin: 0 4px;
}

#lexical-wrapper .link-editor select {
  padding: 6px;
  border: none;
  background-color: #00000013;
  border-radius: 4px;
}

#lexical-wrapper .mention:focus {
  box-shadow: 0 0 0 2px #000;
  outline: none;
}

#lexical-wrapper .characters-limit {
  color: #888;
  font-size: 12px;
  text-align: right;
  display: block;
  position: absolute;
  left: 12px;
  bottom: 5px;
}

#lexical-wrapper .characters-limit.characters-limit-exceeded {
  color: red;
}

#lexical-wrapper .dropdown {
  z-index: 100;
  display: block;
  position: fixed;
  box-shadow: 0 12px 28px #0003, 0 2px 4px #0000001a, inset 0 0 0 1px #ffffff80;
  border-radius: 8px;
  min-height: 40px;
  background-color: #fff;
}

#lexical-wrapper .dropdown .item {
  margin: 0 8px;
  padding: 8px;
  color: #050505;
  cursor: pointer;
  line-height: 16px;
  font-size: 15px;
  display: flex;
  align-content: center;
  flex-direction: row;
  flex-shrink: 0;
  justify-content: space-between;
  background-color: #fff;
  border-radius: 8px;
  border: 0;
  max-width: 250px;
  min-width: 100px;
}

#lexical-wrapper .dropdown .item.fontsize-item, #lexical-wrapper .dropdown .item.fontsize-item .text {
  min-width: unset;
}

#lexical-wrapper .dropdown .item .active {
  display: flex;
  width: 20px;
  height: 20px;
  background-size: contain;
}

#lexical-wrapper .dropdown .item:first-child {
  margin-top: 8px;
}

#lexical-wrapper .dropdown .item:last-child {
  margin-bottom: 8px;
}

#lexical-wrapper .dropdown .item:hover {
  background-color: #eee;
}

#lexical-wrapper .dropdown .item .text {
  display: flex;
  line-height: 20px;
  flex-grow: 1;
  min-width: 150px;
}

#lexical-wrapper .dropdown .item .icon {
  display: flex;
  width: 20px;
  height: 20px;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  margin-right: 12px;
  line-height: 16px;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

#lexical-wrapper .dropdown .divider {
  width: auto;
  background-color: #eee;
  margin: 4px 8px;
  height: 1px;
}

@media screen and (width <= 1100px) {
  #lexical-wrapper .dropdown-button-text {
    display: none !important;
  }

  #lexical-wrapper .dialog-dropdown > .dropdown-button-text {
    display: flex !important;
  }

  #lexical-wrapper .font-size .dropdown-button-text {
    display: flex !important;
  }

  #lexical-wrapper .code-language .dropdown-button-text {
    display: flex !important;
  }
}

#lexical-wrapper .icon.paragraph {
  background-image: url("/public/lexical/icons/text-paragraph.svg");
}

#lexical-wrapper .icon.h1 {
  background-image: url("/public/lexical/icons/type-h1.svg");
}

#lexical-wrapper .icon.h2 {
  background-image: url("/public/lexical/icons/type-h2.svg");
}

#lexical-wrapper .icon.h3 {
  background-image: url("/public/lexical/icons/type-h3.svg");
}

#lexical-wrapper .icon.h4 {
  background-image: url("/public/lexical/icons/type-h4.svg");
}

#lexical-wrapper .icon.h5 {
  background-image: url("/public/lexical/icons/type-h5.svg");
}

#lexical-wrapper .icon.h6 {
  background-image: url("/public/lexical/icons/type-h6.svg");
}

#lexical-wrapper .icon.bullet-list, #lexical-wrapper .icon.bullet {
  background-image: url("/public/lexical/icons/list-ul.svg");
}

#lexical-wrapper .icon.check-list, #lexical-wrapper .icon.check {
  background-image: url("/public/lexical/icons/square-check.svg");
}

#lexical-wrapper .icon.numbered-list, #lexical-wrapper .icon.number {
  background-image: url("/public/lexical/icons/list-ol.svg");
}

#lexical-wrapper .icon.quote {
  background-image: url("/public/lexical/icons/chat-square-quote.svg");
}

#lexical-wrapper .icon.code {
  background-image: url("/public/lexical/icons/code.svg");
}

#lexical-wrapper .switches {
  z-index: 6;
  position: fixed;
  left: 10px;
  bottom: 70px;
  animation: .4s slide-in;
}

@keyframes slide-in {
  0% {
    opacity: 0;
    transform: translateX(-200px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

#lexical-wrapper .switch {
  display: block;
  color: #444;
  margin: 5px 0;
  background-color: #eeeeeeb3;
  padding: 5px 10px;
  border-radius: 10px;
}

#lexical-wrapper #rich-text-switch {
  right: 0;
}

#lexical-wrapper #character-count-switch {
  right: 130px;
}

#lexical-wrapper .switch label {
  margin-right: 5px;
  line-height: 24px;
  width: 100px;
  font-size: 14px;
  display: inline-block;
  vertical-align: middle;
}

#lexical-wrapper .switch button {
  background-color: #ced0d4;
  height: 24px;
  box-sizing: border-box;
  border-radius: 12px;
  width: 44px;
  display: inline-block;
  vertical-align: middle;
  position: relative;
  outline: none;
  cursor: pointer;
  transition: background-color .1s;
  border: 2px solid #0000;
}

#lexical-wrapper .switch button:focus-visible {
  border-color: #00f;
}

#lexical-wrapper .switch button span {
  top: 0;
  left: 0;
  display: block;
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 12px;
  background-color: #fff;
  transition: transform .2s;
}

#lexical-wrapper .switch button[aria-checked="true"] {
  background-color: #1877f2;
}

#lexical-wrapper .switch button[aria-checked="true"] span {
  transform: translateX(20px);
}

#lexical-wrapper .editor-shell span.editor-image {
  cursor: default;
  display: inline-block;
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

#lexical-wrapper .editor-shell .editor-image img {
  max-width: 100%;
  cursor: default;
}

#lexical-wrapper .editor-shell .editor-image img.focused {
  outline: 2px solid #3c84f4;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

#lexical-wrapper .editor-shell .editor-image img.focused.draggable {
  cursor: grab;
}

#lexical-wrapper .editor-shell .editor-image img.focused.draggable:active {
  cursor: grabbing;
}

#lexical-wrapper .editor-shell .editor-image .image-caption-container .tree-view-output {
  margin: 0;
  border-radius: 0;
}

#lexical-wrapper .editor-shell .editor-image .image-caption-container {
  display: block;
  position: absolute;
  bottom: 4px;
  left: 0;
  right: 0;
  padding: 0;
  margin: 0;
  border-top: 1px solid #fff;
  background-color: #ffffffe6;
  min-width: 100px;
  color: #000;
  overflow: hidden;
}

#lexical-wrapper .editor-shell .editor-image .image-caption-button {
  display: block;
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  width: 30%;
  padding: 10px;
  margin: 0 auto;
  border: 1px solid #ffffff4d;
  border-radius: 5px;
  background-color: #00000080;
  min-width: 100px;
  color: #fff;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

#lexical-wrapper .editor-shell .editor-image .image-caption-button:hover {
  background-color: #3c84f480;
}

#lexical-wrapper .editor-shell .editor-image .image-edit-button {
  border: 1px solid #0000004d;
  border-radius: 5px;
  background-image: url("/public/lexical/icons/pencil-fill.svg");
  background-size: 16px;
  background-position: center;
  background-repeat: no-repeat;
  width: 35px;
  height: 35px;
  vertical-align: -.25em;
  position: absolute;
  right: 4px;
  top: 4px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

#lexical-wrapper .editor-shell .editor-image .image-edit-button:hover {
  background-color: #3c84f41a;
}

#lexical-wrapper .editor-shell .editor-image .image-resizer {
  display: block;
  width: 7px;
  height: 7px;
  position: absolute;
  background-color: #3c84f4;
  border: 1px solid #fff;
}

#lexical-wrapper .editor-shell .editor-image .image-resizer.image-resizer-n {
  top: -6px;
  left: 48%;
  cursor: n-resize;
}

#lexical-wrapper .editor-shell .editor-image .image-resizer.image-resizer-ne {
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}

#lexical-wrapper .editor-shell .editor-image .image-resizer.image-resizer-e {
  bottom: 48%;
  right: -6px;
  cursor: e-resize;
}

#lexical-wrapper .editor-shell .editor-image .image-resizer.image-resizer-se {
  bottom: -2px;
  right: -6px;
  cursor: nwse-resize;
}

#lexical-wrapper .editor-shell .editor-image .image-resizer.image-resizer-s {
  bottom: -2px;
  left: 48%;
  cursor: s-resize;
}

#lexical-wrapper .editor-shell .editor-image .image-resizer.image-resizer-sw {
  bottom: -2px;
  left: -6px;
  cursor: sw-resize;
}

#lexical-wrapper .editor-shell .editor-image .image-resizer.image-resizer-w {
  bottom: 48%;
  left: -6px;
  cursor: w-resize;
}

#lexical-wrapper .editor-shell .editor-image .image-resizer.image-resizer-nw {
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}

#lexical-wrapper .editor-shell span.inline-editor-image {
  cursor: default;
  display: inline-block;
  position: relative;
  z-index: 1;
}

#lexical-wrapper .editor-shell .inline-editor-image img {
  max-width: 100%;
  cursor: default;
}

#lexical-wrapper .editor-shell .inline-editor-image img.focused {
  outline: 2px solid #3c84f4;
}

#lexical-wrapper .editor-shell .inline-editor-image img.focused.draggable {
  cursor: grab;
}

#lexical-wrapper .editor-shell .inline-editor-image img.focused.draggable:active {
  cursor: grabbing;
}

#lexical-wrapper .editor-shell .inline-editor-image .image-caption-container .tree-view-output {
  margin: 0;
  border-radius: 0;
}

#lexical-wrapper .editor-shell .inline-editor-image.position-full {
  margin: 1em 0;
}

#lexical-wrapper .editor-shell .inline-editor-image.position-left {
  float: left;
  width: 50%;
  margin: 1em 1em 0 0;
}

#lexical-wrapper .editor-shell .inline-editor-image.position-right {
  float: right;
  width: 50%;
  margin: 1em 0 0 1em;
}

#lexical-wrapper .editor-shell .inline-editor-image .image-edit-button {
  display: block;
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 6px 8px;
  margin: 0 auto;
  border: 1px solid #ffffff4d;
  border-radius: 5px;
  background-color: #00000080;
  min-width: 60px;
  color: #fff;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

#lexical-wrapper .editor-shell .inline-editor-image .image-edit-button:hover {
  background-color: #3c84f480;
}

#lexical-wrapper .editor-shell .inline-editor-image .image-caption-container {
  display: block;
  background-color: #f4f4f4;
  min-width: 100%;
  color: #000;
  overflow: hidden;
}

#lexical-wrapper .emoji {
  color: #0000;
  caret-color: #050505;
  background-size: 16px 16px;
  background-position: center;
  background-repeat: no-repeat;
  vertical-align: middle;
  margin: 0 -1px;
}

#lexical-wrapper .emoji-inner {
  padding: 0 .15em;
}

#lexical-wrapper .emoji-inner::-moz-selection {
  color: #0000;
  background-color: #96969666;
}

#lexical-wrapper .emoji-inner::selection {
  color: #0000;
  background-color: #96969666;
}

#lexical-wrapper .emoji-inner {
  color: #0000;
  background-color: #96969666;
}

#lexical-wrapper .emoji.happysmile {
  background-image: url("/public/lexical/emoji/1F642.png");
}

#lexical-wrapper .emoji.veryhappysmile {
  background-image: url("/public/lexical/emoji/1F600.png");
}

#lexical-wrapper .emoji.unhappysmile {
  background-image: url("/public/lexical/emoji/1F641.png");
}

#lexical-wrapper .emoji.heart {
  background-image: url("/public/lexical/emoji/2764.png");
}

#lexical-wrapper .keyword {
  color: #f1765e;
  font-weight: bold;
}

#lexical-wrapper .actions {
  position: absolute;
  text-align: right;
  margin: 10px;
  bottom: 0;
  right: 0;
}

#lexical-wrapper .actions.tree-view {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

#lexical-wrapper .actions i {
  background-size: contain;
  display: inline-block;
  height: 15px;
  width: 15px;
  vertical-align: -.25em;
}

#lexical-wrapper .actions i.indent {
  background-image: url("/public/lexical/icons/indent.svg");
}

#lexical-wrapper .actions i.outdent {
  background-image: url("/public/lexical/icons/outdent.svg");
}

#lexical-wrapper .actions i.lock {
  background-image: url("/public/lexical/icons/lock-fill.svg");
}

#lexical-wrapper .actions i.image {
  background-image: url("/public/lexical/icons/file-image.svg");
}

#lexical-wrapper .actions i.table {
  background-image: url("/public/lexical/icons/table.svg");
}

#lexical-wrapper .actions i.unlock {
  background-image: url("/public/lexical/icons/lock.svg");
}

#lexical-wrapper .actions i.left-align {
  background-image: url("/public/lexical/icons/text-left.svg");
}

#lexical-wrapper .actions i.center-align {
  background-image: url("/public/lexical/icons/text-center.svg");
}

#lexical-wrapper .actions i.right-align {
  background-image: url("/public/lexical/icons/text-right.svg");
}

#lexical-wrapper .actions i.justify-align {
  background-image: url("/public/lexical/icons/justify.svg");
}

#lexical-wrapper .actions i.disconnect {
  background-image: url("/public/lexical/icons/plug.svg");
}

#lexical-wrapper .actions i.connect {
  background-image: url("/public/lexical/icons/plug-fill.svg");
}

#lexical-wrapper .table-cell-action-button-container {
  position: absolute;
  top: 0;
  left: 0;
  will-change: transform;
}

#lexical-wrapper .table-cell-action-button {
  background-color: none;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 0;
  position: relative;
  border-radius: 15px;
  color: #222;
  display: inline-block;
  cursor: pointer;
}

#lexical-wrapper i.chevron-down {
  background-color: #0000;
  background-size: contain;
  display: inline-block;
  height: 8px;
  width: 8px;
  background-image: url("/public/lexical/icons/chevron-down.svg");
}

#lexical-wrapper .action-button {
  background-color: #eee;
  border: 0;
  padding: 8px 12px;
  position: relative;
  margin-left: 5px;
  border-radius: 15px;
  color: #222;
  display: inline-block;
  cursor: pointer;
}

#lexical-wrapper .action-button:hover {
  background-color: #ddd;
  color: #000;
}

#lexical-wrapper .action-button-mic.active {
  animation: 3s infinite mic-pulsate-color;
}

#lexical-wrapper button.action-button:disabled {
  opacity: .6;
  background: #eee;
  cursor: not-allowed;
}

@keyframes mic-pulsate-color {
  0% {
    background-color: #ffdcdc;
  }

  50% {
    background-color: #ff8585;
  }

  100% {
    background-color: #ffdcdc;
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }

  100% {
    transform: scale(2.5);
    opacity: 0;
  }
}

#lexical-wrapper .debug-timetravel-panel {
  overflow: hidden;
  padding: 0 0 10px;
  margin: auto;
  display: flex;
}

#lexical-wrapper .debug-timetravel-panel-slider {
  padding: 0;
  flex: 8;
}

#lexical-wrapper .debug-timetravel-panel-button {
  padding: 0;
  border: 0;
  background: none;
  flex: 1;
  color: #fff;
  font-size: 12px;
}

#lexical-wrapper .debug-timetravel-panel-button:hover {
  text-decoration: underline;
}

#lexical-wrapper .debug-timetravel-button {
  border: 0;
  padding: 0;
  font-size: 12px;
  top: 10px;
  right: 15px;
  position: absolute;
  background: none;
  color: #fff;
}

#lexical-wrapper .debug-timetravel-button:hover {
  text-decoration: underline;
}

#lexical-wrapper .debug-treetype-button {
  border: 0;
  padding: 0;
  font-size: 12px;
  top: 10px;
  right: 85px;
  position: absolute;
  background: none;
  color: #fff;
}

#lexical-wrapper .debug-treetype-button:hover {
  text-decoration: underline;
}

#lexical-wrapper .connecting {
  font-size: 15px;
  color: #999;
  overflow: hidden;
  position: absolute;
  text-overflow: ellipsis;
  top: 10px;
  left: 10px;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  white-space: nowrap;
  display: inline-block;
  pointer-events: none;
}

#lexical-wrapper .ltr {
  text-align: left;
}

#lexical-wrapper .rtl {
  text-align: right;
}

#lexical-wrapper .toolbar {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 1px;
  background: #fff;
  padding: 4px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  vertical-align: middle;
  overflow: auto;
  height: -moz-max-content;
  height: max-content;
  width: 100%;
  min-height: 36px;
  position: sticky;
  top: 0;
  z-index: 0;
}

#lexical-wrapper button.toolbar-item {
  border: 0;
  display: flex;
  background: none;
  border-radius: 10px;
  padding: 8px;
  cursor: pointer;
  vertical-align: middle;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
}

#lexical-wrapper button.toolbar-item:disabled {
  cursor: not-allowed;
}

#lexical-wrapper button.toolbar-item.spaced {
  margin-right: 2px;
}

#lexical-wrapper button.toolbar-item i.format {
  background-size: contain;
  display: inline-block;
  height: 18px;
  width: 18px;
  vertical-align: -.25em;
  display: flex;
  opacity: .6;
}

#lexical-wrapper button.toolbar-item:disabled .icon, #lexical-wrapper button.toolbar-item:disabled .text, #lexical-wrapper button.toolbar-item:disabled i.format, #lexical-wrapper button.toolbar-item:disabled .chevron-down {
  opacity: .2;
}

#lexical-wrapper button.toolbar-item.active {
  background-color: #dfe8fa4d;
}

#lexical-wrapper button.toolbar-item.active i {
  opacity: 1;
}

#lexical-wrapper .toolbar-item:hover:not([disabled]) {
  background-color: #eee;
}

#lexical-wrapper .toolbar-item.font-family .text {
  display: block;
  max-width: 40px;
}

#lexical-wrapper .toolbar .code-language {
  width: 150px;
}

#lexical-wrapper .toolbar .toolbar-item .text {
  display: flex;
  line-height: 20px;
  vertical-align: middle;
  font-size: 14px;
  color: #777;
  text-overflow: ellipsis;
  overflow: hidden;
  height: 20px;
  text-align: left;
  padding-right: 10px;
}

#lexical-wrapper .toolbar .toolbar-item .icon {
  display: flex;
  width: 20px;
  height: 20px;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  margin-right: 8px;
  line-height: 16px;
  background-size: contain;
}

#lexical-wrapper .toolbar i.chevron-down, #lexical-wrapper .toolbar-item i.chevron-down {
  margin-top: 3px;
  width: 16px;
  height: 16px;
  display: flex;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

#lexical-wrapper .toolbar i.chevron-down.inside {
  width: 16px;
  height: 16px;
  display: flex;
  margin-left: -25px;
  margin-top: 11px;
  margin-right: 10px;
  pointer-events: none;
}

#lexical-wrapper .toolbar .divider {
  width: 1px;
  background-color: #eee;
  margin: 0 4px;
}

#lexical-wrapper .sticky-note-container {
  position: absolute;
  z-index: 9;
  width: 120px;
  display: inline-block;
}

#lexical-wrapper .sticky-note {
  line-height: 1;
  text-align: left;
  width: 120px;
  margin: 25px;
  padding: 20px 10px;
  position: relative;
  border: 1px solid #e8e8e8;
  font-family: Reenie Beanie;
  font-size: 24px;
  border-bottom-right-radius: 60px 5px;
  display: block;
  cursor: move;
}

#lexical-wrapper .sticky-note:after {
  content: "";
  position: absolute;
  z-index: -1;
  right: 0;
  bottom: 20px;
  width: 120px;
  height: 25px;
  background: #0003;
  box-shadow: 2px 15px 5px #0006;
  transform: matrix(-1, -.1, 0, 1, 0, 0);
}

#lexical-wrapper .sticky-note.yellow {
  border-top: 1px solid #fdfd86;
  background: linear-gradient(135deg, #ff8 81% 82%, #ff8 82%, #ffffc6 100%);
}

#lexical-wrapper .sticky-note.pink {
  border-top: 1px solid #e7d1e4;
  background: linear-gradient(135deg, #f7cbe8 81% 82%, #f7cbe8 82%, #e7bfe1 100%);
}

#lexical-wrapper .sticky-note-container.dragging {
  transition: none !important;
}

#lexical-wrapper .sticky-note div {
  cursor: text;
}

#lexical-wrapper .sticky-note .delete {
  border: 0;
  background: none;
  position: absolute;
  top: 8px;
  right: 10px;
  font-size: 10px;
  cursor: pointer;
  opacity: .5;
}

#lexical-wrapper .sticky-note .delete:hover {
  font-weight: bold;
  opacity: 1;
}

#lexical-wrapper .sticky-note .color {
  border: 0;
  background: none;
  position: absolute;
  top: 8px;
  right: 25px;
  cursor: pointer;
  opacity: .5;
}

#lexical-wrapper .sticky-note .color:hover {
  opacity: 1;
}

#lexical-wrapper .sticky-note .color i {
  display: block;
  width: 12px;
  height: 12px;
  background-size: contain;
}

#lexical-wrapper .github-corner:hover .octo-arm {
  animation: .56s ease-in-out octocat-wave;
}

@keyframes octocat-wave {
  0%, 100% {
    transform: rotate(0);
  }

  20%, 60% {
    transform: rotate(-25deg);
  }

  40%, 80% {
    transform: rotate(10deg);
  }
}

@media (width <= 500px) {
  #lexical-wrapper .github-corner:hover .octo-arm {
    animation: none;
  }

  #lexical-wrapper .github-corner .octo-arm {
    animation: .56s ease-in-out octocat-wave;
  }
}

#lexical-wrapper .spacer {
  letter-spacing: -2px;
}

#lexical-wrapper .editor-equation {
  cursor: default;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

#lexical-wrapper .editor-equation.focused {
  outline: 2px solid #3c84f4;
}

#lexical-wrapper button.item i {
  opacity: .6;
}

#lexical-wrapper button.item.dropdown-item-active {
  background-color: #dfe8fa4d;
}

#lexical-wrapper button.item.dropdown-item-active i {
  opacity: 1;
}

#lexical-wrapper .TableNode__contentEditable {
  min-height: 20px;
  border: 0;
  resize: none;
  cursor: text;
  display: block;
  position: relative;
  outline: 0;
  padding: 0;
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
  font-size: 15px;
  white-space: pre-wrap;
  word-break: break-word;
  z-index: 3;
}

#lexical-wrapper .LexicalEditorTheme__blockCursor {
  display: block;
  pointer-events: none;
  position: absolute;
}

#lexical-wrapper .LexicalEditorTheme__blockCursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid #000;
  animation: 1.1s steps(2, start) infinite CursorBlink;
}

@keyframes CursorBlink {
  to {
    visibility: hidden;
  }
}

#lexical-wrapper .dialog-dropdown {
  margin-bottom: 10px;
  width: 100%;
  background-color: #eee !important;
}

#lexical-wrapper img, #lexical-wrapper iframe, #lexical-wrapper audio, #lexical-wrapper video {
  max-width: 100%;
  margin: 20px auto;
}

#lexical-wrapper .LexicalEditorTheme__paragraph > img {
  display: initial !important;
}

.LexicalEditorTheme__ul {
  margin-top: 12px;
  margin-bottom: 12px;
}

.LexicalEditorTheme__listItem {
  margin-top: 8px;
  margin-bottom: 8px;
  line-height: 150%;
}

.LexicalEditorTheme__paragraph {
  line-height: 28px;
}

#lexical-wrapper .LexicalEditorTheme__layoutContainer, .LexicalEditorTheme__layoutContainer, #lexical-wrapper div[data-lexical-layout-container="true"], div[data-lexical-layout-container="true"] {
  display: grid;
  gap: 20px;
  margin: 10px 0;
}

#lexical-wrapper .LexicalEditorTheme__layoutItem, .LexicalEditorTheme__layoutItem {
  min-height: 50px;
}

#lexical-wrapper .LexicalEditorTheme__layoutItem, .LexicalEditorTheme__layoutItem {
  display: block !important;
  grid-template-columns: unset !important;
}

/*# sourceMappingURL=src_components_lexical-content_styles_b52d8e.css.map*/