const CHUNK_PUBLIC_PATH = "server/app/[countryCode]/[localeLanguage]/(main)/(home-page)/page.js";
const runtime = require("../../../../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_50b7b4._.js");
runtime.loadChunk("server/chunks/ssr/[root of the server]__ea5163._.js");
runtime.loadChunk("server/chunks/ssr/src_app_89b09d._.js");
runtime.loadChunk("server/chunks/ssr/src_app_67de58._.js");
runtime.loadChunk("server/chunks/ssr/_bcea10._.js");
runtime.loadChunk("server/chunks/ssr/_5573e6._.css");
runtime.loadChunk("server/chunks/ssr/src_0c754e._.js");
runtime.loadChunk("server/chunks/ssr/src_app_global-error_tsx_55201b._.js");
runtime.loadChunk("server/chunks/ssr/_995c29._.js");
runtime.loadChunk("server/chunks/ssr/src_app_[countryCode]_[localeLanguage]_not-found_tsx_7b1bc1._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_423ba3._.js");
runtime.loadChunk("server/chunks/ssr/[root of the server]__b7d537._.js");
runtime.loadChunk("server/chunks/ssr/src_426e81._.js");
runtime.loadChunk("server/chunks/ssr/_15a3ba._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_esm_build_templates_app-page_725593.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/[countryCode]/[localeLanguage]/(main)/(home-page)/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/lib/data/cart.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/lib/data/customer.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/lib/data/onboarding.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/src/lib/data/products.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/src/lib/data/orders.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/src/lib/data/fulfillment.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE6 => \"[project]/src/lib/data/cookies.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE7 => \"[project]/src/lib/data/regions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE8 => \"[project]/src/lib/data/payment.ts [app-rsc] (ecmascript)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/[countryCode]/[localeLanguage]/(main)/(home-page)/page { GLOBAL_ERROR_MODULE => \"[project]/src/app/global-error.tsx [app-rsc] (ecmascript, Next.js server component)\", METADATA_0 => \"[project]/src/app/twitter-image.jpg.mjs { IMAGE => \\\"[project]/src/app/twitter-image.jpg [app-rsc] (static)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", METADATA_1 => \"[project]/src/app/opengraph-image.jpg.mjs { IMAGE => \\\"[project]/src/app/opengraph-image.jpg [app-rsc] (static)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_2 => \"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/src/app/not-found.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/src/app/global-error.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/src/app/[countryCode]/[localeLanguage]/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/src/app/[countryCode]/[localeLanguage]/not-found.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/src/app/[countryCode]/[localeLanguage]/(main)/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_8 => \"[project]/src/app/[countryCode]/[localeLanguage]/(main)/(home-page)/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_9 => \"[project]/src/app/[countryCode]/[localeLanguage]/(main)/(home-page)/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
