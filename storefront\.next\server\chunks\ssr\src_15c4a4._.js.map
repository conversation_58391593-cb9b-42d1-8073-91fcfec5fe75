{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/utils/index.ts"], "sourcesContent": ["import { twMerge } from \"tailwind-merge\"\r\nimport { clsx } from \"clsx\"\r\nimport { ClassValue } from \"class-variance-authority/dist/types\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\n\r\nexport const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));\r\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAIO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,QAAQ,CAAC,KAAe,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS"}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"rounded-md inline-flex items-center justify-center gap-2 whitespace-nowrap text-xs md:text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none  [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        icon: \"p-0 bg-transparent border-none\",\r\n        default:\r\n          \"bg-primary-main text-primary-foreground shadow hover:bg-primary-main/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-gray-300 text-gray-700 font-medium bg-white shadow-sm hover:bg-primary-lighter hover:text-primary-main hover:border-primary-main\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary-main underline-offset-4 hover:underline\",\r\n        pagination:\r\n          \"hover:font-bold hover:text-primary-main hover:bg-gray-200 rounded-full\",\r\n        paginationActive:\r\n          \"bg-primary-lighter text-primary-main rounded-full font-bold\",\r\n      },\r\n      size: {\r\n        default: \"h-10 md:h-12 px-4 py-3\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n  VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,sSACA;IACE,UAAU;QACR,SAAS;YACP,MAAM;YACN,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,YACE;YACF,kBACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,sMAAM,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/app/global-error.tsx"], "sourcesContent": ["\"use client\"\n\nimport { But<PERSON> } from \"components/ui/button\"\nimport { useEffect, useState } from \"react\"\n\nexport default function GlobalError({\n  error,\n  reset,\n}: {\n  error: Error & { digest?: string }\n  reset: () => void\n}) {\n  const [locale, setLocale] = useState<\"vi\" | \"en\">(\"vi\")\n\n  useEffect(() => {\n    // Log the error to an error reporting service\n    console.error(\"Global error:\", error)\n\n    // Detect locale from URL or browser\n    const detectLocale = () => {\n      // Try to get from current URL\n      const path = window.location.pathname\n      const urlLocale = path.split(\"/\")[2] // /vn/vi/ or /vn/en/\n\n      if (urlLocale === \"en\" || urlLocale === \"vi\") {\n        return urlLocale as \"vi\" | \"en\"\n      }\n\n      // Fallback to browser language\n      const browserLang = navigator.language.toLowerCase()\n      return browserLang.includes(\"vi\") ? \"vi\" : \"en\"\n    }\n\n    setLocale(detectLocale())\n  }, [error])\n\n  // Translations object\n  const translations = {\n    vi: {\n      title: \"Oops!\",\n      subtitle: \"Đã xảy ra lỗi\",\n      description: \"Xin lỗi, đã có lỗi xảy ra. Vui lòng thử lại sau.\",\n      retry: \"Thử lại\",\n      goHome: \"Về trang chủ\",\n      errorDetails: \"Chi tiết lỗi\",\n    },\n    en: {\n      title: \"Oops!\",\n      subtitle: \"An error occurred\",\n      description: \"Sorry, an error occurred. Please try again later.\",\n      retry: \"Try again\",\n      goHome: \"Go to homepage\",\n      errorDetails: \"Error details\",\n    },\n  }\n\n  const t = translations[locale]\n\n  const handleGoHome = () => {\n    // Try to maintain locale in URL\n    const path = window.location.pathname\n    const segments = path.split(\"/\")\n    if (\n      segments.length >= 3 &&\n      (segments[2] === \"vi\" || segments[2] === \"en\")\n    ) {\n      window.location.href = `/${segments[1]}/${segments[2]}`\n    } else {\n      window.location.href = \"/\"\n    }\n  }\n\n  return (\n    <html>\n      <body>\n        <div className=\"flex min-h-screen flex-col items-center justify-center bg-gray-50 px-4\">\n          <div className=\"w-full max-w-md text-center\">\n            <div className=\"mb-8\">\n              <h1 className=\"mb-4 text-6xl font-bold text-gray-900\">\n                {t.title}\n              </h1>\n              <h2 className=\"mb-4 text-2xl font-semibold text-gray-700\">\n                {t.subtitle}\n              </h2>\n              <p className=\"text-gray-600\">{t.description}</p>\n            </div>\n\n            <div className=\"space-y-4\">\n              <Button onClick={reset} className=\"w-full\">\n                {t.retry}\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                onClick={handleGoHome}\n                className=\"w-full\"\n              >\n                {t.goHome}\n              </Button>\n            </div>\n\n            {process.env.NODE_ENV === \"development\" && (\n              <details className=\"mt-8 text-left\">\n                <summary className=\"cursor-pointer text-sm text-gray-500\">\n                  {t.errorDetails} (Development)\n                </summary>\n                <pre className=\"rounded mt-2 overflow-auto bg-gray-100 p-4 text-xs\">\n                  {error.message}\n                  {error.stack}\n                </pre>\n              </details>\n            )}\n          </div>\n        </div>\n      </body>\n    </html>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS,YAAY,EAClC,KAAK,EACL,KAAK,EAIN;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8CAA8C;QAC9C,QAAQ,KAAK,CAAC,iBAAiB;QAE/B,oCAAoC;QACpC,MAAM,eAAe;YACnB,8BAA8B;YAC9B,MAAM,OAAO,OAAO,QAAQ,CAAC,QAAQ;YACrC,MAAM,YAAY,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,qBAAqB;;YAE1D,IAAI,cAAc,QAAQ,cAAc,MAAM;gBAC5C,OAAO;YACT;YAEA,+BAA+B;YAC/B,MAAM,cAAc,UAAU,QAAQ,CAAC,WAAW;YAClD,OAAO,YAAY,QAAQ,CAAC,QAAQ,OAAO;QAC7C;QAEA,UAAU;IACZ,GAAG;QAAC;KAAM;IAEV,sBAAsB;IACtB,MAAM,eAAe;QACnB,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,OAAO;YACP,QAAQ;YACR,cAAc;QAChB;QACA,IAAI;YACF,OAAO;YACP,UAAU;YACV,aAAa;YACb,OAAO;YACP,QAAQ;YACR,cAAc;QAChB;IACF;IAEA,MAAM,IAAI,YAAY,CAAC,OAAO;IAE9B,MAAM,eAAe;QACnB,gCAAgC;QAChC,MAAM,OAAO,OAAO,QAAQ,CAAC,QAAQ;QACrC,MAAM,WAAW,KAAK,KAAK,CAAC;QAC5B,IACE,SAAS,MAAM,IAAI,KACnB,CAAC,QAAQ,CAAC,EAAE,KAAK,QAAQ,QAAQ,CAAC,EAAE,KAAK,IAAI,GAC7C;YACA,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,EAAE;QACzD,OAAO;YACL,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,qBACE,8OAAC;kBACC,cAAA,8OAAC;sBACC,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,EAAE,KAAK;;;;;;8CAEV,8OAAC;oCAAG,WAAU;8CACX,EAAE,QAAQ;;;;;;8CAEb,8OAAC;oCAAE,WAAU;8CAAiB,EAAE,WAAW;;;;;;;;;;;;sCAG7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAO,WAAU;8CAC/B,EAAE,KAAK;;;;;;8CAGV,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;8CAET,EAAE,MAAM;;;;;;;;;;;;wBAIZ,oDAAyB,+BACxB,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAQ,WAAU;;wCAChB,EAAE,YAAY;wCAAC;;;;;;;8CAElB,8OAAC;oCAAI,WAAU;;wCACZ,MAAM,OAAO;wCACb,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9B"}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}