{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/lib/data/banners.ts"], "sourcesContent": ["import { cache } from \"react\";\nimport { sdk } from \"@lib/config\";\nimport { T_BannerListResp, T_BannerListFilters, T_Banner } from \"types/banner\";\n\nconst DEFAULT_REGION = \"vn\";\n\nexport const listBanners = cache(\n  async ({\n    filters,\n    fields,\n  }: {\n    filters: T_BannerListFilters\n    fields?: string[]\n  }) => {\n    const query: Record<string, any> = {\n      ...(filters.page && { page: filters.page }),\n      ...(filters.limit && { limit: filters.limit }),\n      ...(filters.position && { position: filters.position }),\n      ...(filters.is_active !== undefined && { is_active: filters.is_active }),\n      ...(filters.order && { order: filters.order }),\n      fields: fields?.join(\", \"),\n    }\n\n    try {\n      const response = await sdk.client.fetch(\"/store/cms/banners\", {\n        query,\n        headers: { next: { tags: [\"cms\", \"banners\"] } },\n      })\n      return response as T_BannerListResp\n    } catch (error) {\n      console.error(\"Error fetching banners\", error)\n      throw error // Rethrow the error for further handling\n    }\n  }\n)\n\nexport const getBannersList = cache(async function ({\n  filters,\n  countryCode = DEFAULT_REGION,\n}: {\n  filters?: T_BannerListFilters\n  countryCode?: string\n}) {\n  const { page, limit, position, is_active, order } = filters || {}\n\n  return sdk.client\n    .fetch(\"/store/cms/banners\", {\n      query: {\n        limit,\n        page,\n        position,\n        is_active: is_active !== undefined ? is_active : true,\n        order: order || \"rank\",\n      },\n      headers: { next: { tags: [\"cms\", \"banners\"] } },\n    })\n    .then((res) => res as T_BannerListResp)\n    .catch((err) => {\n      console.error(\"Error fetching banners\", err)\n      return {\n        banners: [],\n        count: 0,\n        page: 1,\n        last_page: 1,\n        limit: 20,\n      }\n    })\n})\n\n//--------------------------------\n//Get banners by position\n//--------------------------------\nexport const getBannersByPosition = cache(async function (position: string) {\n  return sdk.client\n    .fetch(\"/store/cms/banners\", {\n      query: {\n        position,\n        is_active: true,\n        limit: 50,\n      },\n      headers: { next: { tags: [\"cms\", \"banners\"] } },\n    })\n    .then((res) => (res as T_BannerListResp).banners)\n    .catch((err) => {\n      console.error(\"Error fetching banners by position\", err)\n      return []\n    })\n})\n\n//--------------------------------\n//Get hero banners\n//--------------------------------\nexport const getHeroBanners = cache(async function () {\n  return getBannersByPosition(\"hero\")\n})\n\n//--------------------------------\n//Get sidebar banners\n//--------------------------------\nexport const getSidebarBanners = cache(async function () {\n  return getBannersByPosition(\"sidebar\")\n})\n\n//--------------------------------\n//Get footer banners\n//--------------------------------\nexport const getFooterBanners = cache(async function () {\n  return getBannersByPosition(\"footer\")\n})\n\n//--------------------------------\n//Get popup banners\n//--------------------------------\nexport const getPopupBanners = cache(async function () {\n  return getBannersByPosition(\"popup\")\n})\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAGA,MAAM,iBAAiB;AAEhB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAC7B,OAAO,EACL,OAAO,EACP,MAAM,EAIP;IACC,MAAM,QAA6B;QACjC,GAAI,QAAQ,IAAI,IAAI;YAAE,MAAM,QAAQ,IAAI;QAAC,CAAC;QAC1C,GAAI,QAAQ,KAAK,IAAI;YAAE,OAAO,QAAQ,KAAK;QAAC,CAAC;QAC7C,GAAI,QAAQ,QAAQ,IAAI;YAAE,UAAU,QAAQ,QAAQ;QAAC,CAAC;QACtD,GAAI,QAAQ,SAAS,KAAK,aAAa;YAAE,WAAW,QAAQ,SAAS;QAAC,CAAC;QACvE,GAAI,QAAQ,KAAK,IAAI;YAAE,OAAO,QAAQ,KAAK;QAAC,CAAC;QAC7C,QAAQ,QAAQ,KAAK;IACvB;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,oHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB;YAC5D;YACA,SAAS;gBAAE,MAAM;oBAAE,MAAM;wBAAC;wBAAO;qBAAU;gBAAC;YAAE;QAChD;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM,MAAM,yCAAyC;;IACvD;AACF;AAGK,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,eAAgB,EAClD,OAAO,EACP,cAAc,cAAc,EAI7B;IACC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC;IAEhE,OAAO,oHAAA,CAAA,MAAG,CAAC,MAAM,CACd,KAAK,CAAC,sBAAsB;QAC3B,OAAO;YACL;YACA;YACA;YACA,WAAW,cAAc,YAAY,YAAY;YACjD,OAAO,SAAS;QAClB;QACA,SAAS;YAAE,MAAM;gBAAE,MAAM;oBAAC;oBAAO;iBAAU;YAAC;QAAE;IAChD,GACC,IAAI,CAAC,CAAC,MAAQ,KACd,KAAK,CAAC,CAAC;QACN,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YACL,SAAS,EAAE;YACX,OAAO;YACP,MAAM;YACN,WAAW;YACX,OAAO;QACT;IACF;AACJ;AAKO,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,eAAgB,QAAgB;IACxE,OAAO,oHAAA,CAAA,MAAG,CAAC,MAAM,CACd,KAAK,CAAC,sBAAsB;QAC3B,OAAO;YACL;YACA,WAAW;YACX,OAAO;QACT;QACA,SAAS;YAAE,MAAM;gBAAE,MAAM;oBAAC;oBAAO;iBAAU;YAAC;QAAE;IAChD,GACC,IAAI,CAAC,CAAC,MAAQ,AAAC,IAAyB,OAAO,EAC/C,KAAK,CAAC,CAAC;QACN,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO,EAAE;IACX;AACJ;AAKO,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE;IAClC,OAAO,qBAAqB;AAC9B;AAKO,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE;IACrC,OAAO,qBAAqB;AAC9B;AAKO,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE;IACpC,OAAO,qBAAqB;AAC9B;AAKO,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE;IACnC,OAAO,qBAAqB;AAC9B"}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/lib/data/posts.ts"], "sourcesContent": ["import { sdk } from \"@lib/config\"\r\nimport { cache } from \"react\"\r\nimport { T_Post, T_PostListResp } from \"types/post\"\r\nimport { DEFAULT_REGION } from \"utils/constant\"\r\n\r\ntype T_PostListFilters = {\r\n  page: number | string\r\n  limit: number | string\r\n  order?: string\r\n  category_handle?: string | string[]\r\n  keyWord?: string\r\n}\r\nexport const listPosts = cache(\r\n  async ({\r\n    filters,\r\n    fields,\r\n  }: {\r\n    filters: T_PostListFilters\r\n    fields?: string[]\r\n  }) => {\r\n    const query: Record<string, any> = {\r\n      ...(filters.page && { page: filters.page }),\r\n      ...(filters.limit && { limit: filters.limit }),\r\n      ...(filters.order && { order: filters.order }),\r\n      ...(filters.category_handle && {\r\n        category_handle: filters.category_handle,\r\n      }),\r\n      ...(filters.keyWord && { keyWord: filters.keyWord }),\r\n      fields: fields?.join(\", \"),\r\n    }\r\n\r\n    try {\r\n      const response = await sdk.client.fetch(\"/store/cms/posts\", {\r\n        query,\r\n        headers: { next: { tags: [\"cms\", \"posts\"] } },\r\n      })\r\n      return response as T_PostListResp\r\n    } catch (error) {\r\n      console.error(\"Error fetching posts\", error)\r\n      throw error // Rethrow the error for further handling\r\n    }\r\n  }\r\n)\r\n\r\n//--------------------------------\r\n//Get posts list\r\n//--------------------------------\r\ntype T_PostListParams = {\r\n  filters?: {\r\n    page: number\r\n    limit: number\r\n    order?: string\r\n    fields?: (keyof T_Post)[]\r\n    category_handle?: string\r\n  }\r\n  countryCode?: string\r\n}\r\n\r\nexport const getPostsList = cache(async function ({\r\n  filters,\r\n  countryCode = DEFAULT_REGION,\r\n}: T_PostListParams) {\r\n  const { page, limit, order, fields, category_handle } = filters || {}\r\n\r\n  return sdk.client\r\n    .fetch(\"/store/cms/posts\", {\r\n      query: {\r\n        limit,\r\n        page,\r\n        order: order || \"-created_at\",\r\n        fields: fields ? fields.join(\",\") : undefined,\r\n        ...(category_handle && { category_handle }),\r\n      },\r\n      headers: { next: { tags: [\"cms\", \"posts\"] } },\r\n    })\r\n    .then((res) => res as T_PostListResp)\r\n    .catch((err) => {\r\n      console.error(\"Error fetching posts\", err)\r\n      return {\r\n        posts: [],\r\n        paging: null,\r\n      }\r\n    })\r\n})\r\n\r\n//--------------------------------\r\n//Get post by handle\r\n//--------------------------------\r\nexport const getPostByHandle = cache(async function (handle: string) {\r\n  return sdk.client\r\n    .fetch(`/store/cms/posts/${handle}`, {\r\n      query: { handle },\r\n      headers: { next: { tags: [\"cms\", \"posts\"] } },\r\n    })\r\n    .then((res) => (res as { post: T_Post }).post)\r\n})\r\n\r\n//--------------------------------\r\n//Get featured posts\r\n//--------------------------------\r\nexport const getFeaturedPosts = cache(async function () {\r\n  return sdk.client\r\n    .fetch(\"/store/cms/posts\", {\r\n      query: {\r\n        featured: true,\r\n      },\r\n      headers: { next: { tags: [\"cms\", \"cms-posts\"] } },\r\n    })\r\n    .then((res) => res as T_PostListResp)\r\n})\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;AASO,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAC3B,OAAO,EACL,OAAO,EACP,MAAM,EAIP;IACC,MAAM,QAA6B;QACjC,GAAI,QAAQ,IAAI,IAAI;YAAE,MAAM,QAAQ,IAAI;QAAC,CAAC;QAC1C,GAAI,QAAQ,KAAK,IAAI;YAAE,OAAO,QAAQ,KAAK;QAAC,CAAC;QAC7C,GAAI,QAAQ,KAAK,IAAI;YAAE,OAAO,QAAQ,KAAK;QAAC,CAAC;QAC7C,GAAI,QAAQ,eAAe,IAAI;YAC7B,iBAAiB,QAAQ,eAAe;QAC1C,CAAC;QACD,GAAI,QAAQ,OAAO,IAAI;YAAE,SAAS,QAAQ,OAAO;QAAC,CAAC;QACnD,QAAQ,QAAQ,KAAK;IACvB;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,oHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB;YAC1D;YACA,SAAS;gBAAE,MAAM;oBAAE,MAAM;wBAAC;wBAAO;qBAAQ;gBAAC;YAAE;QAC9C;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM,MAAM,yCAAyC;;IACvD;AACF;AAiBK,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,eAAgB,EAChD,OAAO,EACP,cAAc,wHAAA,CAAA,iBAAc,EACX;IACjB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,WAAW,CAAC;IAEpE,OAAO,oHAAA,CAAA,MAAG,CAAC,MAAM,CACd,KAAK,CAAC,oBAAoB;QACzB,OAAO;YACL;YACA;YACA,OAAO,SAAS;YAChB,QAAQ,SAAS,OAAO,IAAI,CAAC,OAAO;YACpC,GAAI,mBAAmB;gBAAE;YAAgB,CAAC;QAC5C;QACA,SAAS;YAAE,MAAM;gBAAE,MAAM;oBAAC;oBAAO;iBAAQ;YAAC;QAAE;IAC9C,GACC,IAAI,CAAC,CAAC,MAAQ,KACd,KAAK,CAAC,CAAC;QACN,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;YACL,OAAO,EAAE;YACT,QAAQ;QACV;IACF;AACJ;AAKO,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,eAAgB,MAAc;IACjE,OAAO,oHAAA,CAAA,MAAG,CAAC,MAAM,CACd,KAAK,CAAC,CAAC,iBAAiB,EAAE,QAAQ,EAAE;QACnC,OAAO;YAAE;QAAO;QAChB,SAAS;YAAE,MAAM;gBAAE,MAAM;oBAAC;oBAAO;iBAAQ;YAAC;QAAE;IAC9C,GACC,IAAI,CAAC,CAAC,MAAQ,AAAC,IAAyB,IAAI;AACjD;AAKO,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE;IACpC,OAAO,oHAAA,CAAA,MAAG,CAAC,MAAM,CACd,KAAK,CAAC,oBAAoB;QACzB,OAAO;YACL,UAAU;QACZ;QACA,SAAS;YAAE,MAAM;gBAAE,MAAM;oBAAC;oBAAO;iBAAY;YAAC;QAAE;IAClD,GACC,IAAI,CAAC,CAAC,MAAQ;AACnB"}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/home/<USER>/home-templates.tsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const HomeTemplate = registerClientReference(\n    function() { throw new Error(\"Attempted to call HomeTemplate() from the server but HomeTemplate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/modules/home/<USER>/home-templates.tsx <module evaluation>\",\n    \"HomeTemplate\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,+EACA"}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/home/<USER>/home-templates.tsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const HomeTemplate = registerClientReference(\n    function() { throw new Error(\"Attempted to call HomeTemplate() from the server but HomeTemplate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/modules/home/<USER>/home-templates.tsx\",\n    \"HomeTemplate\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2DACA"}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/ui/structured-data.tsx"], "sourcesContent": ["import Script from \"next/script\"\r\n\r\ninterface StructuredDataProps {\r\n  data: any\r\n}\r\n\r\nexport function StructuredData({ data }: StructuredDataProps) {\r\n  return (\r\n    <Script\r\n      id=\"structured-data\"\r\n      type=\"application/ld+json\"\r\n      dangerouslySetInnerHTML={{\r\n        __html: JSON.stringify(data),\r\n      }}\r\n    />\r\n  )\r\n} "], "names": [], "mappings": ";;;;AAAA;;;AAMO,SAAS,eAAe,EAAE,IAAI,EAAuB;IAC1D,qBACE,8OAAC,8HAAA,CAAA,UAAM;QACL,IAAG;QACH,MAAK;QACL,yBAAyB;YACvB,QAAQ,KAAK,SAAS,CAAC;QACzB;;;;;;AAGN"}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/lib/util/structured-data.ts"], "sourcesContent": ["export interface OrganizationStructuredData {\r\n  name: string\r\n  url: string\r\n  logo: string\r\n  description: string\r\n  address?: {\r\n    streetAddress: string\r\n    addressLocality: string\r\n    addressRegion: string\r\n    postalCode: string\r\n    addressCountry: string\r\n  }\r\n  contactPoint?: {\r\n    telephone: string\r\n    contactType: string\r\n  }\r\n}\r\n\r\nexport interface ProductStructuredData {\r\n  name: string\r\n  description: string\r\n  image: string\r\n  url: string\r\n  sku: string\r\n  brand: string\r\n  category: string\r\n  price: number\r\n  priceCurrency: string\r\n  availability: \"InStock\" | \"OutOfStock\" | \"PreOrder\"\r\n  aggregateRating?: {\r\n    ratingValue: number\r\n    reviewCount: number\r\n  }\r\n}\r\n\r\nexport interface BreadcrumbStructuredData {\r\n  items: Array<{\r\n    name: string\r\n    url: string\r\n  }>\r\n}\r\n\r\nexport interface ArticleStructuredData {\r\n  headline: string\r\n  description: string\r\n  image: string\r\n  url: string\r\n  datePublished: string\r\n  dateModified: string\r\n  author: {\r\n    name: string\r\n    url: string\r\n  }\r\n  publisher: {\r\n    name: string\r\n    logo: string\r\n  }\r\n}\r\n\r\nexport function generateOrganizationStructuredData(\r\n  data: OrganizationStructuredData\r\n) {\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"Organization\",\r\n    name: data.name,\r\n    url: data.url,\r\n    logo: data.logo,\r\n    description: data.description,\r\n    ...(data.address && {\r\n      address: {\r\n        \"@type\": \"PostalAddress\",\r\n        streetAddress: data.address.streetAddress,\r\n        addressLocality: data.address.addressLocality,\r\n        addressRegion: data.address.addressRegion,\r\n        postalCode: data.address.postalCode,\r\n        addressCountry: data.address.addressCountry,\r\n      },\r\n    }),\r\n    ...(data.contactPoint && {\r\n      contactPoint: {\r\n        \"@type\": \"ContactPoint\",\r\n        telephone: data.contactPoint.telephone,\r\n        contactType: data.contactPoint.contactType,\r\n      },\r\n    }),\r\n  }\r\n}\r\n\r\nexport function generateProductStructuredData(data: ProductStructuredData) {\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"Product\",\r\n    name: data.name,\r\n    description: data.description,\r\n    image: data.image,\r\n    url: data.url,\r\n    sku: data.sku,\r\n    brand: {\r\n      \"@type\": \"Brand\",\r\n      name: data.brand,\r\n    },\r\n    category: data.category,\r\n    offers: {\r\n      \"@type\": \"Offer\",\r\n      price: data.price,\r\n      priceCurrency: data.priceCurrency,\r\n      availability: `https://schema.org/${data.availability}`,\r\n      url: data.url,\r\n    },\r\n    ...(data.aggregateRating && {\r\n      aggregateRating: {\r\n        \"@type\": \"AggregateRating\",\r\n        ratingValue: data.aggregateRating.ratingValue,\r\n        reviewCount: data.aggregateRating.reviewCount,\r\n      },\r\n    }),\r\n  }\r\n}\r\n\r\nexport function generateBreadcrumbStructuredData(\r\n  data: BreadcrumbStructuredData\r\n) {\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BreadcrumbList\",\r\n    itemListElement: data.items.map((item, index) => ({\r\n      \"@type\": \"ListItem\",\r\n      position: index + 1,\r\n      name: item.name,\r\n      item: item.url,\r\n    })),\r\n  }\r\n}\r\n\r\nexport function generateArticleStructuredData(data: ArticleStructuredData) {\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"Article\",\r\n    headline: data.headline,\r\n    description: data.description,\r\n    image: data.image,\r\n    url: data.url,\r\n    datePublished: data.datePublished,\r\n    dateModified: data.dateModified,\r\n    author: {\r\n      \"@type\": \"Person\",\r\n      name: data.author.name,\r\n      url: data.author.url,\r\n    },\r\n    publisher: {\r\n      \"@type\": \"Organization\",\r\n      name: data.publisher.name,\r\n      logo: {\r\n        \"@type\": \"ImageObject\",\r\n        url: data.publisher.logo,\r\n      },\r\n    },\r\n  }\r\n}\r\n\r\nexport function generateWebsiteStructuredData() {\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"WebSite\",\r\n    name: \"eFruit\",\r\n    url: \"https://efruit.vn\",\r\n    potentialAction: {\r\n      \"@type\": \"SearchAction\",\r\n      target: {\r\n        \"@type\": \"EntryPoint\",\r\n        urlTemplate: \"https://efruit.vn/search?q={search_term_string}\",\r\n      },\r\n      \"query-input\": \"required name=search_term_string\",\r\n    },\r\n  }\r\n}\r\n\r\nexport function generateLocalBusinessStructuredData() {\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"LocalBusiness\",\r\n    name: \"eFruit\",\r\n    description:\r\n      \"Fresh fruits and premium quality products delivered to your door\",\r\n    url: \"https://efruit.vn\",\r\n    telephone: \"+84-xxx-xxx-xxxx\",\r\n    address: {\r\n      \"@type\": \"PostalAddress\",\r\n      streetAddress: \"Your Street Address\",\r\n      addressLocality: \"Ho Chi Minh City\",\r\n      addressRegion: \"Ho Chi Minh\",\r\n      postalCode: \"70000\",\r\n      addressCountry: \"VN\",\r\n    },\r\n    geo: {\r\n      \"@type\": \"GeoCoordinates\",\r\n      latitude: 10.8231,\r\n      longitude: 106.6297,\r\n    },\r\n    openingHoursSpecification: [\r\n      {\r\n        \"@type\": \"OpeningHoursSpecification\",\r\n        dayOfWeek: [\r\n          \"Monday\",\r\n          \"Tuesday\",\r\n          \"Wednesday\",\r\n          \"Thursday\",\r\n          \"Friday\",\r\n          \"Saturday\",\r\n          \"Sunday\",\r\n        ],\r\n        opens: \"08:00\",\r\n        closes: \"22:00\",\r\n      },\r\n    ],\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AA2DO,SAAS,mCACd,IAAgC;IAEhC,OAAO;QACL,YAAY;QACZ,SAAS;QACT,MAAM,KAAK,IAAI;QACf,KAAK,KAAK,GAAG;QACb,MAAM,KAAK,IAAI;QACf,aAAa,KAAK,WAAW;QAC7B,GAAI,KAAK,OAAO,IAAI;YAClB,SAAS;gBACP,SAAS;gBACT,eAAe,KAAK,OAAO,CAAC,aAAa;gBACzC,iBAAiB,KAAK,OAAO,CAAC,eAAe;gBAC7C,eAAe,KAAK,OAAO,CAAC,aAAa;gBACzC,YAAY,KAAK,OAAO,CAAC,UAAU;gBACnC,gBAAgB,KAAK,OAAO,CAAC,cAAc;YAC7C;QACF,CAAC;QACD,GAAI,KAAK,YAAY,IAAI;YACvB,cAAc;gBACZ,SAAS;gBACT,WAAW,KAAK,YAAY,CAAC,SAAS;gBACtC,aAAa,KAAK,YAAY,CAAC,WAAW;YAC5C;QACF,CAAC;IACH;AACF;AAEO,SAAS,8BAA8B,IAA2B;IACvE,OAAO;QACL,YAAY;QACZ,SAAS;QACT,MAAM,KAAK,IAAI;QACf,aAAa,KAAK,WAAW;QAC7B,OAAO,KAAK,KAAK;QACjB,KAAK,KAAK,GAAG;QACb,KAAK,KAAK,GAAG;QACb,OAAO;YACL,SAAS;YACT,MAAM,KAAK,KAAK;QAClB;QACA,UAAU,KAAK,QAAQ;QACvB,QAAQ;YACN,SAAS;YACT,OAAO,KAAK,KAAK;YACjB,eAAe,KAAK,aAAa;YACjC,cAAc,CAAC,mBAAmB,EAAE,KAAK,YAAY,EAAE;YACvD,KAAK,KAAK,GAAG;QACf;QACA,GAAI,KAAK,eAAe,IAAI;YAC1B,iBAAiB;gBACf,SAAS;gBACT,aAAa,KAAK,eAAe,CAAC,WAAW;gBAC7C,aAAa,KAAK,eAAe,CAAC,WAAW;YAC/C;QACF,CAAC;IACH;AACF;AAEO,SAAS,iCACd,IAA8B;IAE9B,OAAO;QACL,YAAY;QACZ,SAAS;QACT,iBAAiB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAChD,SAAS;gBACT,UAAU,QAAQ;gBAClB,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,GAAG;YAChB,CAAC;IACH;AACF;AAEO,SAAS,8BAA8B,IAA2B;IACvE,OAAO;QACL,YAAY;QACZ,SAAS;QACT,UAAU,KAAK,QAAQ;QACvB,aAAa,KAAK,WAAW;QAC7B,OAAO,KAAK,KAAK;QACjB,KAAK,KAAK,GAAG;QACb,eAAe,KAAK,aAAa;QACjC,cAAc,KAAK,YAAY;QAC/B,QAAQ;YACN,SAAS;YACT,MAAM,KAAK,MAAM,CAAC,IAAI;YACtB,KAAK,KAAK,MAAM,CAAC,GAAG;QACtB;QACA,WAAW;YACT,SAAS;YACT,MAAM,KAAK,SAAS,CAAC,IAAI;YACzB,MAAM;gBACJ,SAAS;gBACT,KAAK,KAAK,SAAS,CAAC,IAAI;YAC1B;QACF;IACF;AACF;AAEO,SAAS;IACd,OAAO;QACL,YAAY;QACZ,SAAS;QACT,MAAM;QACN,KAAK;QACL,iBAAiB;YACf,SAAS;YACT,QAAQ;gBACN,SAAS;gBACT,aAAa;YACf;YACA,eAAe;QACjB;IACF;AACF;AAEO,SAAS;IACd,OAAO;QACL,YAAY;QACZ,SAAS;QACT,MAAM;QACN,aACE;QACF,KAAK;QACL,WAAW;QACX,SAAS;YACP,SAAS;YACT,eAAe;YACf,iBAAiB;YACjB,eAAe;YACf,YAAY;YACZ,gBAAgB;QAClB;QACA,KAAK;YACH,SAAS;YACT,UAAU;YACV,WAAW;QACb;QACA,2BAA2B;YACzB;gBACE,SAAS;gBACT,WAAW;oBACT;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,OAAO;gBACP,QAAQ;YACV;SACD;IACH;AACF"}}, {"offset": {"line": 461, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/app/%5BcountryCode%5D/%5BlocaleLanguage%5D/%28main%29/%28home-page%29/page.tsx"], "sourcesContent": ["import { Metadata } from \"next\"\r\n\r\nimport { getHeroBanners } from \"@lib/data/banners\"\r\nimport { listCategories } from \"@lib/data/categories\"\r\nimport { getFeaturedPosts } from \"@lib/data/posts\"\r\nimport { getProductsById } from \"@lib/data/products\"\r\nimport { getRegion } from \"@lib/data/regions\"\r\nimport { HomeTemplate } from \"@modules/home/<USER>/home-templates\"\r\nimport { notFound } from \"next/navigation\"\r\nimport { TStoreProductWithCustomField } from \"types/product\"\r\nimport { StructuredData } from \"components/ui/structured-data\"\r\nimport { generateWebsiteStructuredData, generateLocalBusinessStructuredData } from \"@lib/util/structured-data\"\r\n\r\nexport async function generateMetadata({\r\n  params,\r\n}: {\r\n  params: Promise<{ localeLanguage: string }>\r\n}): Promise<Metadata> {\r\n  const { localeLanguage } = await params\r\n\r\n  const titles = {\r\n    en: \"eFruit - Fresh Fruits & Premium Quality\",\r\n    vi: \"eFruit - Trái C<PERSON>ơ<PERSON> & <PERSON><PERSON><PERSON>\",\r\n  }\r\n\r\n  const descriptions = {\r\n    en: \"Fresh fruits and premium quality products delivered to your door\",\r\n    vi: \"Tr<PERSON>i cây tươi và sản phẩm chất l<PERSON>ng cao giao tận nơi\",\r\n  }\r\n\r\n  return {\r\n    title: titles[localeLanguage as keyof typeof titles] || titles.en,\r\n    description:\r\n      descriptions[localeLanguage as keyof typeof descriptions] ||\r\n      descriptions.en,\r\n    openGraph: {\r\n      title: titles[localeLanguage as keyof typeof titles] || titles.en,\r\n      description: descriptions[localeLanguage as keyof typeof descriptions] || descriptions.en,\r\n      type: \"website\",\r\n      locale: localeLanguage === \"vi\" ? \"vi_VN\" : \"en_US\",\r\n      images: [\r\n        {\r\n          url: \"/images/efruit-home-og.jpg\",\r\n          width: 1200,\r\n          height: 630,\r\n          alt: \"eFruit - Fresh Fruits & Premium Quality\",\r\n        },\r\n      ],\r\n    },\r\n    twitter: {\r\n      card: \"summary_large_image\",\r\n      title: titles[localeLanguage as keyof typeof titles] || titles.en,\r\n      description: descriptions[localeLanguage as keyof typeof descriptions] || descriptions.en,\r\n      images: [\"/images/efruit-home-twitter.jpg\"],\r\n    },\r\n    alternates: {\r\n      canonical: \"/\",\r\n    },\r\n  }\r\n}\r\n\r\nexport default async function Home(props: {\r\n  params: Promise<{ countryCode: string }>\r\n}) {\r\n  const params = await props.params\r\n\r\n  const { countryCode } = params\r\n  const region = await getRegion(countryCode)\r\n  const categories = await listCategories({\r\n    fields: \"*products\",\r\n    limit: 12,\r\n  })\r\n\r\n  const featuredPosts = await getFeaturedPosts()\r\n  const heroBanners = await getHeroBanners()\r\n\r\n  if (!region || !categories) {\r\n    notFound()\r\n  }\r\n\r\n  const filteredCategories = categories.filter(\r\n    (category) => !category.parent_category\r\n  )\r\n\r\n  const productListByCategory = await getProductsById({\r\n    ids: filteredCategories.flatMap(\r\n      (category) => category.products?.map((product) => product.id) || []\r\n    ),\r\n    regionId: region?.id,\r\n    fields: [\r\n      \"*variants.inventory\",\r\n      \"variants.inventory.location_levels.stock_locations.*\",\r\n      \"variants.inventory_items.inventory.location_levels.stock_locations.address.*\",\r\n      \"variants.inventory.location_levels.available_quantity\",\r\n    ],\r\n  })\r\n\r\n  const productMap = new Map(\r\n    productListByCategory.map((product) => [product.id, product])\r\n  )\r\n\r\n  const categoriesWithProducts = filteredCategories.map((category) => ({\r\n    ...category,\r\n    products:\r\n      category.products\r\n        ?.map((product) => productMap.get(product.id))\r\n        .filter(\r\n          (product): product is TStoreProductWithCustomField =>\r\n            product !== undefined\r\n        ) || [],\r\n  }))\r\n\r\n  const websiteStructuredData = generateWebsiteStructuredData()\r\n  const localBusinessStructuredData = generateLocalBusinessStructuredData()\r\n\r\n  return (\r\n    <>\r\n      <StructuredData data={websiteStructuredData} />\r\n      <StructuredData data={localBusinessStructuredData} />\r\n      <HomeTemplate\r\n        productListByCategory={categoriesWithProducts}\r\n        postsList={featuredPosts}\r\n        heroBanners={heroBanners}\r\n        countryCode={countryCode}\r\n      />\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAHA;;;;;;;;;;;AAKO,eAAe,iBAAiB,EACrC,MAAM,EAGP;IACC,MAAM,EAAE,cAAc,EAAE,GAAG,MAAM;IAEjC,MAAM,SAAS;QACb,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe;QACnB,IAAI;QACJ,IAAI;IACN;IAEA,OAAO;QACL,OAAO,MAAM,CAAC,eAAsC,IAAI,OAAO,EAAE;QACjE,aACE,YAAY,CAAC,eAA4C,IACzD,aAAa,EAAE;QACjB,WAAW;YACT,OAAO,MAAM,CAAC,eAAsC,IAAI,OAAO,EAAE;YACjE,aAAa,YAAY,CAAC,eAA4C,IAAI,aAAa,EAAE;YACzF,MAAM;YACN,QAAQ,mBAAmB,OAAO,UAAU;YAC5C,QAAQ;gBACN;oBACE,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,KAAK;gBACP;aACD;QACH;QACA,SAAS;YACP,MAAM;YACN,OAAO,MAAM,CAAC,eAAsC,IAAI,OAAO,EAAE;YACjE,aAAa,YAAY,CAAC,eAA4C,IAAI,aAAa,EAAE;YACzF,QAAQ;gBAAC;aAAkC;QAC7C;QACA,YAAY;YACV,WAAW;QACb;IACF;AACF;AAEe,eAAe,KAAK,KAElC;IACC,MAAM,SAAS,MAAM,MAAM,MAAM;IAEjC,MAAM,EAAE,WAAW,EAAE,GAAG;IACxB,MAAM,SAAS,MAAM,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE;IAC/B,MAAM,aAAa,MAAM,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE;QACtC,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,gBAAgB,MAAM,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD;IAC3C,MAAM,cAAc,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD;IAEvC,IAAI,CAAC,UAAU,CAAC,YAAY;QAC1B,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,MAAM,qBAAqB,WAAW,MAAM,CAC1C,CAAC,WAAa,CAAC,SAAS,eAAe;IAGzC,MAAM,wBAAwB,MAAM,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE;QAClD,KAAK,mBAAmB,OAAO,CAC7B,CAAC,WAAa,SAAS,QAAQ,EAAE,IAAI,CAAC,UAAY,QAAQ,EAAE,KAAK,EAAE;QAErE,UAAU,QAAQ;QAClB,QAAQ;YACN;YACA;YACA;YACA;SACD;IACH;IAEA,MAAM,aAAa,IAAI,IACrB,sBAAsB,GAAG,CAAC,CAAC,UAAY;YAAC,QAAQ,EAAE;YAAE;SAAQ;IAG9D,MAAM,yBAAyB,mBAAmB,GAAG,CAAC,CAAC,WAAa,CAAC;YACnE,GAAG,QAAQ;YACX,UACE,SAAS,QAAQ,EACb,IAAI,CAAC,UAAY,WAAW,GAAG,CAAC,QAAQ,EAAE,GAC3C,OACC,CAAC,UACC,YAAY,cACX,EAAE;QACb,CAAC;IAED,MAAM,wBAAwB,CAAA,GAAA,wIAAA,CAAA,gCAA6B,AAAD;IAC1D,MAAM,8BAA8B,CAAA,GAAA,wIAAA,CAAA,sCAAmC,AAAD;IAEtE,qBACE;;0BACE,8OAAC,8IAAA,CAAA,iBAAc;gBAAC,MAAM;;;;;;0BACtB,8OAAC,8IAAA,CAAA,iBAAc;gBAAC,MAAM;;;;;;0BACtB,8OAAC,yJAAA,CAAA,eAAY;gBACX,uBAAuB;gBACvB,WAAW;gBACX,aAAa;gBACb,aAAa;;;;;;;;AAIrB"}}, {"offset": {"line": 595, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/lib/data/onboarding.ts"], "sourcesContent": ["\"use server\"\r\nimport { cookies as nextCookies } from \"next/headers\"\r\nimport { redirect } from \"next/navigation\"\r\n\r\nexport async function resetOnboardingState(orderId: string) {\r\n  const cookies = await nextCookies()\r\n  cookies.set(\"_medusa_onboarding\", \"false\", { maxAge: -1 })\r\n  redirect(`http://localhost:7001/a/orders/${orderId}`)\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;;AAAA;;;;;AAEO,eAAe,qBAAqB,OAAe;IACxD,MAAM,UAAU,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAW,AAAD;IAChC,QAAQ,GAAG,CAAC,sBAAsB,SAAS;QAAE,QAAQ,CAAC;IAAE;IACxD,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,+BAA+B,EAAE,SAAS;AACtD;;;IAJsB;;AAAA,+OAAA"}}, {"offset": {"line": 632, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/lib/data/orders.ts"], "sourcesContent": ["\"use server\"\r\n\r\nimport { sdk } from \"@lib/config\"\r\nimport medusaError from \"@lib/util/medusa-error\"\r\nimport { HttpTypes } from \"@medusajs/types\"\r\nimport { getAuthHeaders, getCacheOptions } from \"./cookies\"\r\n\r\nexport const retrieveOrder = async (id: string) => {\r\n  const headers = {\r\n    ...(await getAuthHeaders()),\r\n  }\r\n\r\n  const next = {\r\n    ...(await getCacheOptions(\"orders\")),\r\n  }\r\n  const fieldDefault = [\r\n    \"*cart\",\r\n    \"*payment_collections.payments\",\r\n    \"*items\",\r\n    \"*items.metadata\",\r\n    \"*items.variant\",\r\n    \"*items.product\",\r\n    \"*product_reviews.*\",\r\n  ]\r\n\r\n  return sdk.client\r\n    .fetch<HttpTypes.StoreOrderResponse>(`/store/orders/${id}`, {\r\n      method: \"GET\",\r\n      query: {\r\n        fields: fieldDefault.join(\",\"),\r\n      },\r\n      headers,\r\n      next,\r\n    })\r\n    .then(({ order }) => {\r\n      return order\r\n    })\r\n    .catch((err) => medusaError(err))\r\n}\r\n\r\nexport const listOrders = async ({\r\n  limit = 10,\r\n  offset = 0,\r\n  filters,\r\n  queryString,\r\n}: {\r\n  limit?: number\r\n  offset?: number\r\n  filters?: Record<string, any>\r\n  queryString?: string\r\n}) => {\r\n  const headers = {\r\n    ...(await getAuthHeaders()),\r\n  }\r\n\r\n  const next = {\r\n    ...(await getCacheOptions(\"orders\")),\r\n  }\r\n\r\n  return sdk.client\r\n    .fetch<HttpTypes.StoreOrderListResponse>(`/store/orders`, {\r\n      method: \"GET\",\r\n      query: {\r\n        limit,\r\n        offset,\r\n        order: \"-created_at\",\r\n        fields:\r\n          \"*items,+items.metadata,*items.variant,*items.product\" +\r\n          (queryString ? `,${queryString}` : \"\"),\r\n        ...filters,\r\n      },\r\n      headers,\r\n      next,\r\n    })\r\n    .then(({ orders }) => orders)\r\n    .catch((err) => medusaError(err))\r\n}\r\n\r\nexport const createTransferRequest = async (\r\n  state: {\r\n    success: boolean\r\n    error: string | null\r\n    order: HttpTypes.StoreOrder | null\r\n  },\r\n  formData: FormData\r\n): Promise<{\r\n  success: boolean\r\n  error: string | null\r\n  order: HttpTypes.StoreOrder | null\r\n}> => {\r\n  const id = formData.get(\"order_id\") as string\r\n\r\n  if (!id) {\r\n    return { success: false, error: \"Order ID is required\", order: null }\r\n  }\r\n\r\n  const headers = await getAuthHeaders()\r\n\r\n  return await sdk.store.order\r\n    .requestTransfer(\r\n      id,\r\n      {},\r\n      {\r\n        fields: \"id, email\",\r\n      },\r\n      headers\r\n    )\r\n    .then(({ order }) => ({ success: true, error: null, order }))\r\n    .catch((err) => ({ success: false, error: err.message, order: null }))\r\n}\r\n\r\nexport const acceptTransferRequest = async (id: string, token: string) => {\r\n  const headers = await getAuthHeaders()\r\n\r\n  return await sdk.store.order\r\n    .acceptTransfer(id, { token }, {}, headers)\r\n    .then(({ order }) => ({ success: true, error: null, order }))\r\n    .catch((err) => ({ success: false, error: err.message, order: null }))\r\n}\r\n\r\nexport const declineTransferRequest = async (id: string, token: string) => {\r\n  const headers = await getAuthHeaders()\r\n\r\n  return await sdk.store.order\r\n    .declineTransfer(id, { token }, {}, headers)\r\n    .then(({ order }) => ({ success: true, error: null, order }))\r\n    .catch((err) => ({ success: false, error: err.message, order: null }))\r\n}\r\n\r\nexport const checkOrderAllowReview = async ({\r\n  orderId,\r\n  productId,\r\n}: {\r\n  orderId: string\r\n  productId: string\r\n}) => {\r\n  const headers = {\r\n    ...(await getAuthHeaders()),\r\n  }\r\n  return sdk.client\r\n    .fetch<HttpTypes.StoreOrderListResponse>(\r\n      `/store/product-reviews/${productId}/status`,\r\n      {\r\n        method: \"GET\",\r\n        query: {\r\n          order_id: orderId,\r\n        },\r\n        headers,\r\n      }\r\n    )\r\n    .then((res) => res)\r\n    .catch((err) => medusaError(err))\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AAEA;;;;;;;AAEO,MAAM,gBAAgB,OAAO;IAClC,MAAM,UAAU;QACd,GAAI,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,GAAG;IAC5B;IAEA,MAAM,OAAO;QACX,GAAI,MAAM,CAAA,GAAA,6HAAA,CAAA,kBAAe,AAAD,EAAE,SAAS;IACrC;IACA,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,oHAAA,CAAA,MAAG,CAAC,MAAM,CACd,KAAK,CAA+B,CAAC,cAAc,EAAE,IAAI,EAAE;QAC1D,QAAQ;QACR,OAAO;YACL,QAAQ,aAAa,IAAI,CAAC;QAC5B;QACA;QACA;IACF,GACC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE;QACd,OAAO;IACT,GACC,KAAK,CAAC,CAAC,MAAQ,CAAA,GAAA,qIAAA,CAAA,UAAW,AAAD,EAAE;AAChC;AAEO,MAAM,aAAa,OAAO,EAC/B,QAAQ,EAAE,EACV,SAAS,CAAC,EACV,OAAO,EACP,WAAW,EAMZ;IACC,MAAM,UAAU;QACd,GAAI,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,GAAG;IAC5B;IAEA,MAAM,OAAO;QACX,GAAI,MAAM,CAAA,GAAA,6HAAA,CAAA,kBAAe,AAAD,EAAE,SAAS;IACrC;IAEA,OAAO,oHAAA,CAAA,MAAG,CAAC,MAAM,CACd,KAAK,CAAmC,CAAC,aAAa,CAAC,EAAE;QACxD,QAAQ;QACR,OAAO;YACL;YACA;YACA,OAAO;YACP,QACE,yDACA,CAAC,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,EAAE;YACvC,GAAG,OAAO;QACZ;QACA;QACA;IACF,GACC,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,GAAK,QACrB,KAAK,CAAC,CAAC,MAAQ,CAAA,GAAA,qIAAA,CAAA,UAAW,AAAD,EAAE;AAChC;AAEO,MAAM,wBAAwB,OACnC,OAKA;IAMA,MAAM,KAAK,SAAS,GAAG,CAAC;IAExB,IAAI,CAAC,IAAI;QACP,OAAO;YAAE,SAAS;YAAO,OAAO;YAAwB,OAAO;QAAK;IACtE;IAEA,MAAM,UAAU,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD;IAEnC,OAAO,MAAM,oHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,KAAK,CACzB,eAAe,CACd,IACA,CAAC,GACD;QACE,QAAQ;IACV,GACA,SAED,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;YAAE,SAAS;YAAM,OAAO;YAAM;QAAM,CAAC,GAC1D,KAAK,CAAC,CAAC,MAAQ,CAAC;YAAE,SAAS;YAAO,OAAO,IAAI,OAAO;YAAE,OAAO;QAAK,CAAC;AACxE;AAEO,MAAM,wBAAwB,OAAO,IAAY;IACtD,MAAM,UAAU,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD;IAEnC,OAAO,MAAM,oHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,KAAK,CACzB,cAAc,CAAC,IAAI;QAAE;IAAM,GAAG,CAAC,GAAG,SAClC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;YAAE,SAAS;YAAM,OAAO;YAAM;QAAM,CAAC,GAC1D,KAAK,CAAC,CAAC,MAAQ,CAAC;YAAE,SAAS;YAAO,OAAO,IAAI,OAAO;YAAE,OAAO;QAAK,CAAC;AACxE;AAEO,MAAM,yBAAyB,OAAO,IAAY;IACvD,MAAM,UAAU,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD;IAEnC,OAAO,MAAM,oHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,KAAK,CACzB,eAAe,CAAC,IAAI;QAAE;IAAM,GAAG,CAAC,GAAG,SACnC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;YAAE,SAAS;YAAM,OAAO;YAAM;QAAM,CAAC,GAC1D,KAAK,CAAC,CAAC,MAAQ,CAAC;YAAE,SAAS;YAAO,OAAO,IAAI,OAAO;YAAE,OAAO;QAAK,CAAC;AACxE;AAEO,MAAM,wBAAwB,OAAO,EAC1C,OAAO,EACP,SAAS,EAIV;IACC,MAAM,UAAU;QACd,GAAI,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,GAAG;IAC5B;IACA,OAAO,oHAAA,CAAA,MAAG,CAAC,MAAM,CACd,KAAK,CACJ,CAAC,uBAAuB,EAAE,UAAU,OAAO,CAAC,EAC5C;QACE,QAAQ;QACR,OAAO;YACL,UAAU;QACZ;QACA;IACF,GAED,IAAI,CAAC,CAAC,MAAQ,KACd,KAAK,CAAC,CAAC,MAAQ,CAAA,GAAA,qIAAA,CAAA,UAAW,AAAD,EAAE;AAChC;;;IAjJa;IAiCA;IAsCA;IAiCA;IASA;IASA;;AA1HA,+OAAA;AAiCA,+OAAA;AAsCA,+OAAA;AAiCA,+OAAA;AASA,+OAAA;AASA,+OAAA"}}, {"offset": {"line": 781, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 787, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/lib/data/fulfillment.ts"], "sourcesContent": ["\"use server\"\r\n\r\nimport { sdk } from \"@lib/config\"\r\nimport { HttpTypes } from \"@medusajs/types\"\r\nimport { getAuthHeaders, getCacheOptions } from \"./cookies\"\r\n\r\nexport const listCartShippingMethods = async (cartId: string) => {\r\n  const headers = {\r\n    ...(await getAuthHeaders()),\r\n  }\r\n\r\n  const next = {\r\n    ...(await getCacheOptions(\"fulfillment\")),\r\n  }\r\n\r\n  try {\r\n    const response =\r\n      await sdk.client.fetch<HttpTypes.StoreShippingOptionListResponse>(\r\n        `/store/shipping-options`,\r\n        {\r\n          method: \"GET\",\r\n          query: { cart_id: cartId },\r\n          headers,\r\n          next,\r\n        }\r\n      )\r\n\r\n    return response.shipping_options\r\n  } catch (err) {\r\n    console.error(\"Shipping Options Error:\", err)\r\n    return null\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;;;;AAEO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,UAAU;QACd,GAAI,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,GAAG;IAC5B;IAEA,MAAM,OAAO;QACX,GAAI,MAAM,CAAA,GAAA,6HAAA,CAAA,kBAAe,AAAD,EAAE,cAAc;IAC1C;IAEA,IAAI;QACF,MAAM,WACJ,MAAM,oHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,KAAK,CACpB,CAAC,uBAAuB,CAAC,EACzB;YACE,QAAQ;YACR,OAAO;gBAAE,SAAS;YAAO;YACzB;YACA;QACF;QAGJ,OAAO,SAAS,gBAAgB;IAClC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF;;;IA1Ba;;AAAA,+OAAA"}}, {"offset": {"line": 826, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 832, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/lib/data/payment.ts"], "sourcesContent": ["\"use server\"\r\n\r\nimport { sdk } from \"@lib/config\"\r\nimport { getAuthHeaders, getCacheOptions } from \"./cookies\"\r\nimport { HttpTypes } from \"@medusajs/types\"\r\n\r\nexport const listCartPaymentMethods = async (regionId: string) => {\r\n  const headers = {\r\n    ...(await getAuthHeaders()),\r\n  }\r\n\r\n  const next = {\r\n    ...(await getCacheOptions(\"payment_providers\")),\r\n  }\r\n\r\n  return sdk.client\r\n    .fetch<HttpTypes.StorePaymentProviderListResponse>(\r\n      `/store/payment-providers`,\r\n      {\r\n        method: \"GET\",\r\n        query: { region_id: regionId },\r\n        headers,\r\n        next,\r\n      }\r\n    )\r\n    .then(({ payment_providers }) => payment_providers)\r\n    .catch(() => {\r\n      return null\r\n    })\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;;;;AAGO,MAAM,yBAAyB,OAAO;IAC3C,MAAM,UAAU;QACd,GAAI,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,GAAG;IAC5B;IAEA,MAAM,OAAO;QACX,GAAI,MAAM,CAAA,GAAA,6HAAA,CAAA,kBAAe,AAAD,EAAE,oBAAoB;IAChD;IAEA,OAAO,oHAAA,CAAA,MAAG,CAAC,MAAM,CACd,KAAK,CACJ,CAAC,wBAAwB,CAAC,EAC1B;QACE,QAAQ;QACR,OAAO;YAAE,WAAW;QAAS;QAC7B;QACA;IACF,GAED,IAAI,CAAC,CAAC,EAAE,iBAAiB,EAAE,GAAK,mBAChC,KAAK,CAAC;QACL,OAAO;IACT;AACJ;;;IAvBa;;AAAA,+OAAA"}}, {"offset": {"line": 867, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 873, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 929, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}