{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/lib/context/checkout-context.tsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CheckoutProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call CheckoutProvider() from the server but CheckoutProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/context/checkout-context.tsx <module evaluation>\",\n    \"CheckoutProvider\",\n);\nexport const useCheckout = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCheckout() from the server but useCheckout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/context/checkout-context.tsx <module evaluation>\",\n    \"useCheckout\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,sEACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,sEACA"}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/lib/context/checkout-context.tsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CheckoutProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call CheckoutProvider() from the server but CheckoutProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/context/checkout-context.tsx\",\n    \"CheckoutProvider\",\n);\nexport const useCheckout = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCheckout() from the server but useCheckout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/context/checkout-context.tsx\",\n    \"useCheckout\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,kDACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,kDACA"}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/layout/components/main-nav-content/index.tsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/modules/layout/components/main-nav-content/index.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/modules/layout/components/main-nav-content/index.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4T,GACzV,0FACA"}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/layout/components/main-nav-content/index.tsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/modules/layout/components/main-nav-content/index.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/modules/layout/components/main-nav-content/index.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA"}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/lib/data/categories.ts"], "sourcesContent": ["import { sdk } from \"@lib/config\"\r\nimport { HttpTypes } from \"@medusajs/types\"\r\nimport { cache } from \"react\"\r\nimport { getCacheOptions } from \"./cookies\"\r\nimport { ICategoryListWithMetadata } from \"types/category\"\r\n\r\nexport const listCategories = async (query?: Record<string, any>) => {\r\n  const next = {\r\n    ...(await getCacheOptions(\"categories\")),\r\n  }\r\n\r\n  const limit = query?.limit || 100\r\n\r\n  return sdk.client\r\n    .fetch<{ product_categories: HttpTypes.StoreProductCategory[] }>(\r\n      \"/store/product-categories\",\r\n      {\r\n        query: {\r\n          fields:\r\n            \"*category_children, *products, *parent_category, *parent_category.parent_category\",\r\n          limit,\r\n          ...query,\r\n        },\r\n        next,\r\n      }\r\n    )\r\n    .then(({ product_categories }) => product_categories)\r\n}\r\n\r\nexport const getCategoryByHandle = async (categoryHandle: string[]) => {\r\n  const handle = `${categoryHandle.join(\"/\")}`\r\n\r\n  const next = {\r\n    ...(await getCacheOptions(\"categories\")),\r\n  }\r\n\r\n  return sdk.client\r\n    .fetch<HttpTypes.StoreProductCategoryListResponse>(\r\n      `/store/product-categories`,\r\n      {\r\n        query: {\r\n          fields: \"*category_children, *products, *products.variants\",\r\n          handle,\r\n        },\r\n        next,\r\n      }\r\n    )\r\n    .then(({ product_categories }) => product_categories[0])\r\n}\r\n\r\n//--------------------------------------------------------------------\r\nexport const getCategoryListWithMetadata = cache(async function ({\r\n  offset = 0,\r\n  limit = 100,\r\n  fields,\r\n  customFields,\r\n}: {\r\n  offset?: number\r\n  limit?: number\r\n  fields?: (keyof HttpTypes.StoreProductCategory)[]\r\n  customFields?: string[]\r\n}): Promise<{ categories: ICategoryListWithMetadata[]; count: number }> {\r\n  const defaultFields = [\r\n    \"*category_children\",\r\n    \"*parent_category\",\r\n    \"*parent_category.parent_category\",\r\n    \"translations.*\",\r\n    \"metadata\",\r\n  ]\r\n\r\n  const fieldsString = [\r\n    ...(fields || []),\r\n    ...(customFields ? customFields : []),\r\n    defaultFields,\r\n  ].join(\", \")\r\n\r\n  return sdk.store.category\r\n    .list(\r\n      {\r\n        limit,\r\n        offset,\r\n        fields: fieldsString,\r\n        include_descendants_tree: true,\r\n        include_ancestors_tree: true,\r\n        parent_category_id: \"null\",\r\n      },\r\n      {\r\n        next: { tags: [\"categories\"] },\r\n      }\r\n    )\r\n    .then((res) => {\r\n      return {\r\n        categories: res.product_categories as ICategoryListWithMetadata[],\r\n        count: res.product_categories.length,\r\n      }\r\n    })\r\n})\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AACA;;;;AAGO,MAAM,iBAAiB,OAAO;IACnC,MAAM,OAAO;QACX,GAAI,MAAM,CAAA,GAAA,6HAAA,CAAA,kBAAe,AAAD,EAAE,aAAa;IACzC;IAEA,MAAM,QAAQ,OAAO,SAAS;IAE9B,OAAO,oHAAA,CAAA,MAAG,CAAC,MAAM,CACd,KAAK,CACJ,6BACA;QACE,OAAO;YACL,QACE;YACF;YACA,GAAG,KAAK;QACV;QACA;IACF,GAED,IAAI,CAAC,CAAC,EAAE,kBAAkB,EAAE,GAAK;AACtC;AAEO,MAAM,sBAAsB,OAAO;IACxC,MAAM,SAAS,GAAG,eAAe,IAAI,CAAC,MAAM;IAE5C,MAAM,OAAO;QACX,GAAI,MAAM,CAAA,GAAA,6HAAA,CAAA,kBAAe,AAAD,EAAE,aAAa;IACzC;IAEA,OAAO,oHAAA,CAAA,MAAG,CAAC,MAAM,CACd,KAAK,CACJ,CAAC,yBAAyB,CAAC,EAC3B;QACE,OAAO;YACL,QAAQ;YACR;QACF;QACA;IACF,GAED,IAAI,CAAC,CAAC,EAAE,kBAAkB,EAAE,GAAK,kBAAkB,CAAC,EAAE;AAC3D;AAGO,MAAM,8BAA8B,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,eAAgB,EAC/D,SAAS,CAAC,EACV,QAAQ,GAAG,EACX,MAAM,EACN,YAAY,EAMb;IACC,MAAM,gBAAgB;QACpB;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,eAAe;WACf,UAAU,EAAE;WACZ,eAAe,eAAe,EAAE;QACpC;KACD,CAAC,IAAI,CAAC;IAEP,OAAO,oHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,QAAQ,CACtB,IAAI,CACH;QACE;QACA;QACA,QAAQ;QACR,0BAA0B;QAC1B,wBAAwB;QACxB,oBAAoB;IACtB,GACA;QACE,MAAM;YAAE,MAAM;gBAAC;aAAa;QAAC;IAC/B,GAED,IAAI,CAAC,CAAC;QACL,OAAO;YACL,YAAY,IAAI,kBAAkB;YAClC,OAAO,IAAI,kBAAkB,CAAC,MAAM;QACtC;IACF;AACJ"}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/modules/layout/templates/nav/index.tsx"], "sourcesContent": ["import { enrichLineItems, retrieveCart } from \"@lib/data/cart\"\r\nimport { getMenuByHandle } from \"@lib/data/menu\"\r\nimport { getRegion } from \"@lib/data/regions\"\r\nimport { getSettingsPreferences } from \"@lib/data/setting\"\r\n\r\nimport MainNavContent from \"@modules/layout/components/main-nav-content\"\r\n\r\nimport { listCategories } from \"@lib/data/categories\"\r\nimport { retrieveCustomer } from \"@lib/data/customer\"\r\nimport { getProductsById } from \"@lib/data/products\"\r\nimport { TCartItemVariant } from \"types/cart\"\r\nimport { TStoreProductWithCustomField } from \"types/product\"\r\nimport { HANDLE_MENU } from \"utils/constant\"\r\ntype TNavMainProps = {\r\n  countryCode: string\r\n  isHome?: boolean\r\n}\r\n\r\nexport const NavMain: React.FC<TNavMainProps> = async ({\r\n  countryCode,\r\n  isHome,\r\n}) => {\r\n  const [settings, dataHeaderMenu, cart, region] = await Promise.all([\r\n    getSettingsPreferences(),\r\n    getMenuByHandle(HANDLE_MENU.MAIN_MENU),\r\n    retrieveCart(),\r\n    getRegion(countryCode),\r\n  ])\r\n\r\n  const customer = await retrieveCustomer().catch(() => null)\r\n  const currency_code = region?.currency_code || \"VND\"\r\n\r\n  const categories = await listCategories({\r\n    fields: \"*products\",\r\n  })\r\n\r\n  if (!region) {\r\n    return null\r\n  }\r\n\r\n  if (cart?.items?.length) {\r\n    const enrichedItems = await enrichLineItems(cart?.items, cart?.region_id!)\r\n    cart.items = enrichedItems as TCartItemVariant[]\r\n  }\r\n\r\n  const filteredCategories = categories.filter(\r\n    (category) => !category.parent_category\r\n  )\r\n\r\n  const productListByCategory = await getProductsById({\r\n    ids: filteredCategories.flatMap(\r\n      (category) => category.products?.map((product) => product.id) || []\r\n    ),\r\n    regionId: region?.id,\r\n    fields: [\r\n      \"*variants.inventory\",\r\n      \"*variants.inventory\",\r\n      \"variants.inventory.location_levels.stock_locations.*\",\r\n      \"variants.inventory_items.inventory.location_levels.stock_locations.address.*\",\r\n      \"variants.inventory.location_levels.available_quantity\",\r\n    ],\r\n  })\r\n\r\n  const productMap = new Map(\r\n    productListByCategory.map((product) => [product.id, product])\r\n  )\r\n\r\n  const categoriesWithProducts = filteredCategories.map((category) => ({\r\n    ...category,\r\n    products:\r\n      category.products\r\n        ?.map((product) => productMap.get(product.id))\r\n        .filter(\r\n          (product): product is TStoreProductWithCustomField =>\r\n            product !== undefined\r\n        ) || [],\r\n  }))\r\n\r\n  const navContentProps = {\r\n    countryCode,\r\n    dataHeaderMenu,\r\n    settings,\r\n    cart,\r\n    customer,\r\n    categoriesWithProducts,\r\n    ...(isHome && { isHome }),\r\n  }\r\n\r\n  return isHome ? (\r\n    <div className=\"fixed left-0 top-0 z-50 w-full bg-white\">\r\n      <header className=\"relative mx-auto h-auto duration-200\">\r\n        <MainNavContent {...navContentProps} />\r\n      </header>\r\n    </div>\r\n  ) : (\r\n    <div className=\"fixed inset-x-0 top-0 z-50 bg-white\">\r\n      <header className=\"relative mx-auto h-auto duration-200\">\r\n        <MainNavContent {...navContentProps} />\r\n      </header>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAGA;;;;;;;;;;;AAMO,MAAM,UAAmC,OAAO,EACrD,WAAW,EACX,MAAM,EACP;IACC,MAAM,CAAC,UAAU,gBAAgB,MAAM,OAAO,GAAG,MAAM,QAAQ,GAAG,CAAC;QACjE,CAAA,GAAA,6HAAA,CAAA,yBAAsB,AAAD;QACrB,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD,EAAE,wHAAA,CAAA,cAAW,CAAC,SAAS;QACrC,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;QACX,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE;KACX;IAED,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,IAAI,KAAK,CAAC,IAAM;IACtD,MAAM,gBAAgB,QAAQ,iBAAiB;IAE/C,MAAM,aAAa,MAAM,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE;QACtC,QAAQ;IACV;IAEA,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,IAAI,MAAM,OAAO,QAAQ;QACvB,MAAM,gBAAgB,MAAM,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,OAAO,MAAM;QAC/D,KAAK,KAAK,GAAG;IACf;IAEA,MAAM,qBAAqB,WAAW,MAAM,CAC1C,CAAC,WAAa,CAAC,SAAS,eAAe;IAGzC,MAAM,wBAAwB,MAAM,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE;QAClD,KAAK,mBAAmB,OAAO,CAC7B,CAAC,WAAa,SAAS,QAAQ,EAAE,IAAI,CAAC,UAAY,QAAQ,EAAE,KAAK,EAAE;QAErE,UAAU,QAAQ;QAClB,QAAQ;YACN;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,MAAM,aAAa,IAAI,IACrB,sBAAsB,GAAG,CAAC,CAAC,UAAY;YAAC,QAAQ,EAAE;YAAE;SAAQ;IAG9D,MAAM,yBAAyB,mBAAmB,GAAG,CAAC,CAAC,WAAa,CAAC;YACnE,GAAG,QAAQ;YACX,UACE,SAAS,QAAQ,EACb,IAAI,CAAC,UAAY,WAAW,GAAG,CAAC,QAAQ,EAAE,GAC3C,OACC,CAAC,UACC,YAAY,cACX,EAAE;QACb,CAAC;IAED,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA,GAAI,UAAU;YAAE;QAAO,CAAC;IAC1B;IAEA,OAAO,uBACL,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAO,WAAU;sBAChB,cAAA,8OAAC,0KAAA,CAAA,UAAc;gBAAE,GAAG,eAAe;;;;;;;;;;;;;;;6BAIvC,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAO,WAAU;sBAChB,cAAA,8OAAC,0KAAA,CAAA,UAAc;gBAAE,GAAG,eAAe;;;;;;;;;;;;;;;;AAI3C"}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/app/%5BcountryCode%5D/%5BlocaleLanguage%5D/%28main%29/%28home-page%29/layout.tsx"], "sourcesContent": ["import { Metadata } from \"next\"\r\n\r\nimport { CheckoutProvider } from \"@lib/context/checkout-context\"\r\nimport { getBaseURL } from \"@lib/util/env\"\r\nimport { NavMain } from \"@modules/layout/templates/nav\"\r\n\r\nexport const metadata: Metadata = {\r\n  metadataBase: new URL(getBaseURL()),\r\n}\r\n\r\nexport default async function HomeLayout(props: {\r\n  children: React.ReactNode\r\n  params: { countryCode: string }\r\n}) {\r\n  const { countryCode } = await props.params\r\n\r\n  return (\r\n    <>\r\n      <CheckoutProvider>\r\n        <NavMain countryCode={countryCode} isHome={true} />\r\n      </CheckoutProvider>\r\n      <div className=\"mt-20 lg:mt-28 xl:mt-32\">{props.children}</div>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;;;AAEO,MAAM,WAAqB;IAChC,cAAc,IAAI,IAAI,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD;AACjC;AAEe,eAAe,WAAW,KAGxC;IACC,MAAM,EAAE,WAAW,EAAE,GAAG,MAAM,MAAM,MAAM;IAE1C,qBACE;;0BACE,8OAAC,6IAAA,CAAA,mBAAgB;0BACf,cAAA,8OAAC,sJAAA,CAAA,UAAO;oBAAC,aAAa;oBAAa,QAAQ;;;;;;;;;;;0BAE7C,8OAAC;gBAAI,WAAU;0BAA2B,MAAM,QAAQ;;;;;;;;AAG9D"}}, {"offset": {"line": 333, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}