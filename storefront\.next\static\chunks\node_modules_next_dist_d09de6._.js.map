{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/build/polyfills/process.ts"], "sourcesContent": ["module.exports =\n  global.process?.env && typeof global.process?.env === 'object'\n    ? global.process\n    : require('next/dist/compiled/process')\n"], "names": ["global", "module", "exports", "process", "env", "require"], "mappings": ";IACEA,iBAA8BA;AADhCC,OAAOC,OAAO,GACZF,CAAAA,CAAAA,kBAAAA,OAAOG,OAAO,KAAA,OAAA,KAAA,IAAdH,gBAAgBI,GAAG,KAAI,OAAA,CAAA,CAAOJ,mBAAAA,OAAOG,OAAO,KAAA,OAAA,KAAA,IAAdH,iBAAgBI,GAAG,MAAK,WAClDJ,OAAOG,OAAO,GACdE,QAAQ", "ignoreList": [0]}}, {"offset": {"line": 9, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/dist/build/polyfills/polyfill-module.js"], "sourcesContent": ["\"trimStart\"in String.prototype||(String.prototype.trimStart=String.prototype.trimLeft),\"trimEnd\"in String.prototype||(String.prototype.trimEnd=String.prototype.trimRight),\"description\"in Symbol.prototype||Object.defineProperty(Symbol.prototype,\"description\",{configurable:!0,get:function(){var t=/\\((.*)\\)/.exec(this.toString());return t?t[1]:void 0}}),Array.prototype.flat||(Array.prototype.flat=function(t,r){return r=this.concat.apply([],this),t>1&&r.some(Array.isArray)?r.flat(t-1):r},Array.prototype.flatMap=function(t,r){return this.map(t,r).flat()}),Promise.prototype.finally||(Promise.prototype.finally=function(t){if(\"function\"!=typeof t)return this.then(t,t);var r=this.constructor||Promise;return this.then(function(n){return r.resolve(t()).then(function(){return n})},function(n){return r.resolve(t()).then(function(){throw n})})}),Object.fromEntries||(Object.fromEntries=function(t){return Array.from(t).reduce(function(t,r){return t[r[0]]=r[1],t},{})}),Array.prototype.at||(Array.prototype.at=function(t){var r=Math.trunc(t)||0;if(r<0&&(r+=this.length),!(r<0||r>=this.length))return this[r]}),Object.hasOwn||(Object.hasOwn=function(t,r){if(null==t)throw new TypeError(\"Cannot convert undefined or null to object\");return Object.prototype.hasOwnProperty.call(Object(t),r)}),\"canParse\"in URL||(URL.canParse=function(t,r){try{return!!new URL(t,r)}catch(t){return!1}});\n"], "names": [], "mappings": "AAAA,eAAc,OAAO,SAAS,IAAE,CAAC,OAAO,SAAS,CAAC,SAAS,GAAC,OAAO,SAAS,CAAC,QAAQ,GAAE,aAAY,OAAO,SAAS,IAAE,CAAC,OAAO,SAAS,CAAC,OAAO,GAAC,OAAO,SAAS,CAAC,SAAS,GAAE,iBAAgB,OAAO,SAAS,IAAE,OAAO,cAAc,CAAC,OAAO,SAAS,EAAC,eAAc;IAAC,cAAa,CAAC;IAAE,KAAI;QAAW,IAAI,IAAE,WAAW,IAAI,CAAC,IAAI,CAAC,QAAQ;QAAI,OAAO,IAAE,CAAC,CAAC,EAAE,GAAC,KAAK;IAAC;AAAC,IAAG,MAAM,SAAS,CAAC,IAAI,IAAE,CAAC,MAAM,SAAS,CAAC,IAAI,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,OAAO,IAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAC,IAAI,GAAE,IAAE,KAAG,EAAE,IAAI,CAAC,MAAM,OAAO,IAAE,EAAE,IAAI,CAAC,IAAE,KAAG;AAAC,GAAE,MAAM,SAAS,CAAC,OAAO,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAE,GAAG,IAAI;AAAE,CAAC,GAAE,QAAQ,SAAS,CAAC,OAAO,IAAE,CAAC,QAAQ,SAAS,CAAC,OAAO,GAAC,SAAS,CAAC;IAAE,IAAG,cAAY,OAAO,GAAE,OAAO,IAAI,CAAC,IAAI,CAAC,GAAE;IAAG,IAAI,IAAE,IAAI,CAAC,WAAW,IAAE;IAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;QAAE,OAAO,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC;YAAW,OAAO;QAAC;IAAE,GAAE,SAAS,CAAC;QAAE,OAAO,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC;YAAW,MAAM;QAAC;IAAE;AAAE,CAAC,GAAE,OAAO,WAAW,IAAE,CAAC,OAAO,WAAW,GAAC,SAAS,CAAC;IAAE,OAAO,MAAM,IAAI,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,EAAC,CAAC;QAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAC,CAAC,CAAC,EAAE,EAAC;IAAC,GAAE,CAAC;AAAE,CAAC,GAAE,MAAM,SAAS,CAAC,EAAE,IAAE,CAAC,MAAM,SAAS,CAAC,EAAE,GAAC,SAAS,CAAC;IAAE,IAAI,IAAE,KAAK,KAAK,CAAC,MAAI;IAAE,IAAG,IAAE,KAAG,CAAC,KAAG,IAAI,CAAC,MAAM,GAAE,CAAC,CAAC,IAAE,KAAG,KAAG,IAAI,CAAC,MAAM,GAAE,OAAO,IAAI,CAAC,EAAE;AAAA,CAAC,GAAE,OAAO,MAAM,IAAE,CAAC,OAAO,MAAM,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,IAAG,QAAM,GAAE,MAAM,IAAI,UAAU;IAA8C,OAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,IAAG;AAAE,CAAC,GAAE,cAAa,OAAK,CAAC,IAAI,QAAQ,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,IAAG;QAAC,OAAM,CAAC,CAAC,IAAI,IAAI,GAAE;IAAE,EAAC,OAAM,GAAE;QAAC,OAAM,CAAC;IAAC;AAAC,CAAC", "ignoreList": [0]}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/shared/lib/is-plain-object.ts"], "sourcesContent": ["export function getObjectClassLabel(value: any): string {\n  return Object.prototype.toString.call(value)\n}\n\nexport function isPlainObject(value: any): boolean {\n  if (getObjectClassLabel(value) !== '[object Object]') {\n    return false\n  }\n\n  const prototype = Object.getPrototypeOf(value)\n\n  /**\n   * this used to be previously:\n   *\n   * `return prototype === null || prototype === Object.prototype`\n   *\n   * but Edge Runtime expose Object from vm, being that kind of type-checking wrongly fail.\n   *\n   * It was changed to the current implementation since it's resilient to serialization.\n   */\n  return prototype === null || prototype.hasOwnProperty('isPrototypeOf')\n}\n"], "names": ["getObjectClassLabel", "isPlainObject", "value", "Object", "prototype", "toString", "call", "getPrototypeOf", "hasOwnProperty"], "mappings": ";;;;;;;;;;;;;;;IAAgBA,mBAAmB,EAAA;eAAnBA;;IAIAC,aAAa,EAAA;eAAbA;;;AAJT,SAASD,oBAAoBE,KAAU;IAC5C,OAAOC,OAAOC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ;AACxC;AAEO,SAASD,cAAcC,KAAU;IACtC,IAAIF,oBAAoBE,WAAW,mBAAmB;QACpD,OAAO;IACT;IAEA,MAAME,YAAYD,OAAOI,cAAc,CAACL;IAExC;;;;;;;;GAQC,GACD,OAAOE,cAAc,QAAQA,UAAUI,cAAc,CAAC;AACxD", "ignoreList": [0]}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/shared/lib/head-manager-context.shared-runtime.ts"], "sourcesContent": ["import React from 'react'\n\nexport const HeadManagerContext: React.Context<{\n  updateHead?: (state: any) => void\n  mountedInstances?: any\n  updateScripts?: (state: any) => void\n  scripts?: any\n  getIsSsr?: () => boolean\n\n  // Used in app directory, to render script tags as server components.\n  appDir?: boolean\n  nonce?: string\n}> = React.createContext({})\n\nif (process.env.NODE_ENV !== 'production') {\n  HeadManagerContext.displayName = 'HeadManagerContext'\n}\n"], "names": ["HeadManagerContext", "React", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "AAcIG,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAZhBL,sBAAAA;;;eAAAA;;;;gEAFK;AAEX,MAAMA,qBAURC,OAAAA,OAAK,CAACC,aAAa,CAAC,CAAC;AAE1B,wCAA2C;IACzCF,mBAAmBM,WAAW,GAAG;AACnC", "ignoreList": [0]}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/shared/lib/lazy-dynamic/bailout-to-csr.ts"], "sourcesContent": ["// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n"], "names": ["BailoutToCSRError", "isBailoutToCSRError", "BAILOUT_TO_CSR", "Error", "constructor", "reason", "digest", "err"], "mappings": "AAAA,+GAA+G;;;;;;;;;;;;;;;;IAIlGA,iBAAiB,EAAA;eAAjBA;;IASGC,mBAAmB,EAAA;eAAnBA;;;AAZhB,MAAMC,iBAAiB;AAGhB,MAAMF,0BAA0BG;IAGrCC,YAA4BC,MAAc,CAAE;QAC1C,KAAK,CAAE,wCAAqCA;aADlBA,MAAAA,GAAAA;aAFZC,MAAAA,GAASJ;IAIzB;AACF;AAGO,SAASD,oBAAoBM,GAAY;IAC9C,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAID,MAAM,KAAKJ;AACxB", "ignoreList": [0]}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/shared/lib/hash.ts"], "sourcesContent": ["// http://www.cse.yorku.ca/~oz/hash.html\n// More specifically, 32-bit hash via djbxor\n// (ref: https://gist.github.com/eplawless/52813b1d8ad9af510d85?permalink_comment_id=3367765#gistcomment-3367765)\n// This is due to number type differences between rust for turbopack to js number types,\n// where rust does not have easy way to repreesnt js's 53-bit float number type for the matching\n// overflow behavior. This is more `correct` in terms of having canonical hash across different runtime / implementation\n// as can gaurantee determinstic output from 32bit hash.\nexport function djb2Hash(str: string) {\n  let hash = 5381\n  for (let i = 0; i < str.length; i++) {\n    const char = str.charCodeAt(i)\n    hash = ((hash << 5) + hash + char) & 0xffffffff\n  }\n  return hash >>> 0\n}\n\nexport function hexHash(str: string) {\n  return djb2Hash(str).toString(36).slice(0, 5)\n}\n"], "names": ["djb2Hash", "hexHash", "str", "hash", "i", "length", "char", "charCodeAt", "toString", "slice"], "mappings": "AAAA,wCAAwC;AACxC,4CAA4C;AAC5C,iHAAiH;AACjH,wFAAwF;AACxF,gGAAgG;AAChG,wHAAwH;AACxH,wDAAwD;;;;;;;;;;;;;;;;IACxCA,QAAQ,EAAA;eAARA;;IASAC,OAAO,EAAA;eAAPA;;;AATT,SAASD,SAASE,GAAW;IAClC,IAAIC,OAAO;IACX,IAAK,IAAIC,IAAI,GAAGA,IAAIF,IAAIG,MAAM,EAAED,IAAK;QACnC,MAAME,OAAOJ,IAAIK,UAAU,CAACH;QAC5BD,OAASA,CAAAA,QAAQ,CAAA,IAAKA,OAAOG,OAAQ;IACvC;IACA,OAAOH,SAAS;AAClB;AAEO,SAASF,QAAQC,GAAW;IACjC,OAAOF,SAASE,KAAKM,QAAQ,CAAC,IAAIC,KAAK,CAAC,GAAG;AAC7C", "ignoreList": [0]}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/shared/lib/segment.ts"], "sourcesContent": ["import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n"], "names": ["DEFAULT_SEGMENT_KEY", "PAGE_SEGMENT_KEY", "addSearchParamsIfPageSegment", "isGroupSegment", "segment", "endsWith", "searchParams", "isPageSegment", "includes", "stringified<PERSON><PERSON>y", "JSON", "stringify"], "mappings": ";;;;;;;;;;;;;;;;;IAwBaA,mBAAmB,EAAA;eAAnBA;;IADAC,gBAAgB,EAAA;eAAhBA;;IAhBGC,4BAA4B,EAAA;eAA5BA;;IALAC,cAAc,EAAA;eAAdA;;;AAAT,SAASA,eAAeC,OAAe;IAC5C,sCAAsC;IACtC,OAAOA,OAAO,CAAC,EAAE,KAAK,OAAOA,QAAQC,QAAQ,CAAC;AAChD;AAEO,SAASH,6BACdE,OAAgB,EAChBE,YAA2D;IAE3D,MAAMC,gBAAgBH,QAAQI,QAAQ,CAACP;IAEvC,IAAIM,eAAe;QACjB,MAAME,mBAAmBC,KAAKC,SAAS,CAACL;QACxC,OAAOG,qBAAqB,OACxBR,mBAAmB,MAAMQ,mBACzBR;IACN;IAEA,OAAOG;AACT;AAEO,MAAMH,mBAAmB;AACzB,MAAMD,sBAAsB", "ignoreList": [0]}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/shared/lib/page-path/ensure-leading-slash.ts"], "sourcesContent": ["/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */\nexport function ensureLeadingSlash(path: string) {\n  return path.startsWith('/') ? path : `/${path}`\n}\n"], "names": ["ensureLeadingSlash", "path", "startsWith"], "mappings": "AAAA;;;CAGC,GAAA;;;;+BACeA,sBAAAA;;;eAAAA;;;AAAT,SAASA,mBAAmBC,IAAY;IAC7C,OAAOA,KAAKC,UAAU,CAAC,OAAOD,OAAQ,MAAGA;AAC3C", "ignoreList": [0]}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/shared/lib/router/utils/app-paths.ts"], "sourcesContent": ["import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash'\nimport { isGroupSegment } from '../../segment'\n\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */\nexport function normalizeAppPath(route: string) {\n  return ensureLeadingSlash(\n    route.split('/').reduce((pathname, segment, index, segments) => {\n      // Empty segments are ignored.\n      if (!segment) {\n        return pathname\n      }\n\n      // Groups are ignored.\n      if (isGroupSegment(segment)) {\n        return pathname\n      }\n\n      // Parallel segments are ignored.\n      if (segment[0] === '@') {\n        return pathname\n      }\n\n      // The last segment (if it's a leaf) should be ignored.\n      if (\n        (segment === 'page' || segment === 'route') &&\n        index === segments.length - 1\n      ) {\n        return pathname\n      }\n\n      return `${pathname}/${segment}`\n    }, '')\n  )\n}\n\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */\nexport function normalizeRscURL(url: string) {\n  return url.replace(\n    /\\.rsc($|\\?)/,\n    // $1 ensures `?` is preserved\n    '$1'\n  )\n}\n"], "names": ["normalizeAppPath", "normalizeRscURL", "route", "ensureLeadingSlash", "split", "reduce", "pathname", "segment", "index", "segments", "isGroupSegment", "length", "url", "replace"], "mappings": ";;;;;;;;;;;;;;;IAsBgBA,gBAAgB,EAAA;eAAhBA;;IAmCAC,eAAe,EAAA;eAAfA;;;oCAzDmB;yBACJ;AAqBxB,SAASD,iBAAiBE,KAAa;IAC5C,OAAOC,CAAAA,GAAAA,oBAAAA,kBAAkB,EACvBD,MAAME,KAAK,CAAC,KAAKC,MAAM,CAAC,CAACC,UAAUC,SAASC,OAAOC;QACjD,8BAA8B;QAC9B,IAAI,CAACF,SAAS;YACZ,OAAOD;QACT;QAEA,sBAAsB;QACtB,IAAII,CAAAA,GAAAA,SAAAA,cAAc,EAACH,UAAU;YAC3B,OAAOD;QACT;QAEA,iCAAiC;QACjC,IAAIC,OAAO,CAAC,EAAE,KAAK,KAAK;YACtB,OAAOD;QACT;QAEA,uDAAuD;QACvD,IACGC,CAAAA,YAAY,UAAUA,YAAY,OAAM,KACzCC,UAAUC,SAASE,MAAM,GAAG,GAC5B;YACA,OAAOL;QACT;QAEA,OAAUA,WAAS,MAAGC;IACxB,GAAG;AAEP;AAMO,SAASN,gBAAgBW,GAAW;IACzC,OAAOA,IAAIC,OAAO,CAChB,eACA,AACA,8BAD8B;AAGlC", "ignoreList": [0]}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/shared/lib/app-router-context.shared-runtime.ts"], "sourcesContent": ["'use client'\n\nimport type { FetchServerResponseResult } from '../../client/components/router-reducer/fetch-server-response'\nimport type {\n  FocusAndScrollRef,\n  PrefetchKind,\n  RouterChangeByServerResponse,\n} from '../../client/components/router-reducer/router-reducer-types'\nimport type { <PERSON>RouterState } from '../../server/app-render/types'\nimport React from 'react'\n\nexport type ChildSegmentMap = Map<string, CacheNode>\n\n/**\n * Cache node used in app-router / layout-router.\n */\nexport type CacheNode = ReadyCacheNode | LazyCacheNode\n\nexport type LoadingModuleData =\n  | [React.JSX.Element, React.ReactNode, React.ReactNode]\n  | null\n\nexport type LazyCacheNode = {\n  /**\n   * When rsc is null, this is a lazily-initialized cache node.\n   *\n   * If the app attempts to render it, it triggers a lazy data fetch,\n   * postpones the render, and schedules an update to a new tree.\n   *\n   * TODO: This mechanism should not be used when PPR is enabled, though it\n   * currently is in some cases until we've implemented partial\n   * segment fetching.\n   */\n  rsc: null\n\n  /**\n   * A prefetched version of the segment data. See explanation in corresponding\n   * field of ReadyCacheNode (below).\n   *\n   * Since LazyCacheNode mostly only exists in the non-PPR implementation, this\n   * will usually be null, but it could have been cloned from a previous\n   * CacheNode that was created by the PPR implementation. Eventually we want\n   * to migrate everything away from LazyCacheNode entirely.\n   */\n  prefetchRsc: React.ReactNode\n\n  /**\n   * A pending response for the lazy data fetch. If this is not present\n   * during render, it is lazily created.\n   */\n  lazyData: Promise<FetchServerResponseResult> | null\n\n  prefetchHead: React.ReactNode\n  head: React.ReactNode\n\n  loading: LoadingModuleData\n\n  /**\n   * Child parallel routes.\n   */\n  parallelRoutes: Map<string, ChildSegmentMap>\n}\n\nexport type ReadyCacheNode = {\n  /**\n   * When rsc is not null, it represents the RSC data for the\n   * corresponding segment.\n   *\n   * `null` is a valid React Node but because segment data is always a\n   * <LayoutRouter> component, we can use `null` to represent empty.\n   *\n   * TODO: For additional type safety, update this type to\n   * Exclude<React.ReactNode, null>. Need to update createEmptyCacheNode to\n   * accept rsc as an argument, or just inline the callers.\n   */\n  rsc: React.ReactNode\n\n  /**\n   * Represents a static version of the segment that can be shown immediately,\n   * and may or may not contain dynamic holes. It's prefetched before a\n   * navigation occurs.\n   *\n   * During rendering, we will choose whether to render `rsc` or `prefetchRsc`\n   * with `useDeferredValue`. As with the `rsc` field, a value of `null` means\n   * no value was provided. In this case, the LayoutRouter will go straight to\n   * rendering the `rsc` value; if that one is also missing, it will suspend and\n   * trigger a lazy fetch.\n   */\n  prefetchRsc: React.ReactNode\n\n  /**\n   * There should never be a lazy data request in this case.\n   */\n  lazyData: null\n  prefetchHead: React.ReactNode\n  head: React.ReactNode\n\n  loading: LoadingModuleData\n\n  parallelRoutes: Map<string, ChildSegmentMap>\n}\n\nexport interface NavigateOptions {\n  scroll?: boolean\n}\n\nexport interface PrefetchOptions {\n  kind: PrefetchKind\n}\n\nexport interface AppRouterInstance {\n  /**\n   * Navigate to the previous history entry.\n   */\n  back(): void\n  /**\n   * Navigate to the next history entry.\n   */\n  forward(): void\n  /**\n   * Refresh the current page.\n   */\n  refresh(): void\n  /**\n   * Refresh the current page. Use in development only.\n   * @internal\n   */\n  hmrRefresh(): void\n  /**\n   * Navigate to the provided href.\n   * Pushes a new history entry.\n   */\n  push(href: string, options?: NavigateOptions): void\n  /**\n   * Navigate to the provided href.\n   * Replaces the current history entry.\n   */\n  replace(href: string, options?: NavigateOptions): void\n  /**\n   * Prefetch the provided href.\n   */\n  prefetch(href: string, options?: PrefetchOptions): void\n}\n\nexport const AppRouterContext = React.createContext<AppRouterInstance | null>(\n  null\n)\nexport const LayoutRouterContext = React.createContext<{\n  childNodes: CacheNode['parallelRoutes']\n  tree: FlightRouterState\n  url: string\n  loading: LoadingModuleData\n} | null>(null)\n\nexport const GlobalLayoutRouterContext = React.createContext<{\n  buildId: string\n  tree: FlightRouterState\n  changeByServerResponse: RouterChangeByServerResponse\n  focusAndScrollRef: FocusAndScrollRef\n  nextUrl: string | null\n}>(null as any)\n\nexport const TemplateContext = React.createContext<React.ReactNode>(null as any)\n\nif (process.env.NODE_ENV !== 'production') {\n  AppRouterContext.displayName = 'AppRouterContext'\n  LayoutRouterContext.displayName = 'LayoutRouterContext'\n  GlobalLayoutRouterContext.displayName = 'GlobalLayoutRouterContext'\n  TemplateContext.displayName = 'TemplateContext'\n}\n\nexport const MissingSlotContext = React.createContext<Set<string>>(new Set())\n"], "names": ["AppRouterContext", "GlobalLayoutRouterContext", "LayoutRouterContext", "MissingSlotContext", "TemplateContext", "React", "createContext", "process", "env", "NODE_ENV", "displayName", "Set"], "mappings": "AAqKEA,iBAAiBU,WAAW,GAAG;AArKjC;;;;;;;;;;;;;;;;;;IAgJaV,IAAAA,SAAAA,GAAgB;eAAhBA,OAAAA;;IAUAC,yBAAyB;eAAzBA,gBAAAA;;IAPAC,mBAAmB;eAAnBA,UAAAA;;IAwBAC,kBAAkB;eAAlBA,SAAAA;;IATAC,eAAe;eAAfA,MAAAA;;;;gEAzJK;AAuIX,MAAMJ,SAAAA,UAAmBK,CAAAA,GAAAA,UAAK,CAACC,aAAa,CACjD,CAAA;AAEK,MAAMJ,mBAAAA,GAAsBG,IAAAA,OAAAA,CAAAA,EAAK,CAACC,UAAAA,CAAAA,EAAa,CAK5C;AAEH,MAAML,sBAAAA,MAA4BI,CAAAA,OAAAA,CAAAA,KAAK,CAACC,OAAAA,CAAAA,KAAa,CAMzD;AAEI,MAAMF,kBAAkBC,UAAAA,IAAK,CAACC,EAAAA,OAAAA,CAAAA,GAAa,CAAkB,SAAA,CAAA;AAEpE,IAAIC,EAAAA,MAAQC,GAAG,CAACC,QAAQ,KAAK,EAAA,OAAA,CAAA,IAAc,SAAA,CAAA;wCACV;IAC/BP,iBAAAA,GAAoBQ,QAAAA,GAAW,GAAG;IAClCT,oBAAAA,MAA0BS,KAAAA,GAAAA,GAAW,GAAG;IACxCN,gBAAgBM,UAAAA,CAAW,GAAG,OAAA,GAAA;IAChC,gBAAA,WAAA,GAAA;AAEO,MAAMP,qBAAqBE,cAAK,CAACC,aAAa,CAAc,IAAIK", "ignoreList": [0]}}, {"offset": {"line": 384, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/shared/lib/hooks-client-context.shared-runtime.ts"], "sourcesContent": ["'use client'\n\nimport { createContext } from 'react'\nimport type { Params } from '../../server/request/params'\n\nexport const SearchParamsContext = createContext<URLSearchParams | null>(null)\nexport const PathnameContext = createContext<string | null>(null)\nexport const PathParamsContext = createContext<Params | null>(null)\n\nif (process.env.NODE_ENV !== 'production') {\n  SearchParamsContext.displayName = 'SearchParamsContext'\n  PathnameContext.displayName = 'PathnameContext'\n  PathParamsContext.displayName = 'PathParamsContext'\n}\n"], "names": ["PathParamsContext", "PathnameContext", "SearchParamsContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "AAUEE,oBAAoBK,WAAW,GAAG;AAVpC;;;;;;;;;;;;;;;;IAOaP,IAAAA,SAAAA,IAAiB;eAAjBA,QAAAA;;IADAC,eAAe;eAAfA,MAAAA;;IADAC,mBAAmB;eAAnBA,UAAAA;;;uBAHiB;AAGvB,MAAMA,sBAAsBC,IAAAA,oBAAa,EAAyB;AAClE,MAAMF,kBAAkBE,IAAAA,CAAAA,GAAAA,OAAAA,SAAa,EAAgB,EAAA,EAAA;AACrD,MAAMH,kBAAAA,CAAAA,CAAoBG,EAAAA,EAAAA,KAAAA,aAAAA,EAAa,EAAgB;AAE9D,IAAIC,EAAAA,MAAQC,GAAG,CAACC,QAAQ,EAAA,CAAA,EAAK,CAAA,OAAA,MAAc,OAAA,EAAA;wCACP;IAClCL,gBAAgBM,IAAAA,OAAW,GAAG,CAAA,GAAA;IAC9BP,gBAAAA,EAAkBO,SAAAA,EAAW,CAAA,EAAG;IAClC,kBAAA,WAAA,GAAA", "ignoreList": [0]}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/shared/lib/is-thenable.ts"], "sourcesContent": ["/**\n * Check to see if a value is Thenable.\n *\n * @param promise the maybe-thenable value\n * @returns true if the value is thenable\n */\nexport function isThenable<T = unknown>(\n  promise: Promise<T> | T\n): promise is Promise<T> {\n  return (\n    promise !== null &&\n    typeof promise === 'object' &&\n    'then' in promise &&\n    typeof promise.then === 'function'\n  )\n}\n"], "names": ["isThenable", "promise", "then"], "mappings": "AAAA;;;;;CAKC,GAAA;;;;+BACeA,cAAAA;;;eAAAA;;;AAAT,SAASA,WACdC,OAAuB;IAEvB,OACEA,YAAY,QACZ,OAAOA,YAAY,YACnB,UAAUA,WACV,OAAOA,QAAQC,IAAI,KAAK;AAE5B", "ignoreList": [0]}}, {"offset": {"line": 449, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 454, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/shared/lib/router/utils/is-bot.ts"], "sourcesContent": ["const BOT_UA_RE =\n  /Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i\n\nexport function isBot(userAgent: string): boolean {\n  return BOT_UA_RE.test(userAgent)\n}\n"], "names": ["isBot", "BOT_UA_RE", "userAgent", "test"], "mappings": ";;;;+BAGgBA,SAAAA;;;eAAAA;;;AAHhB,MAAMC,YACJ;AAEK,SAASD,MAAME,SAAiB;IACrC,OAAOD,UAAUE,IAAI,CAACD;AACxB", "ignoreList": [0]}}, {"offset": {"line": 468, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/shared/lib/router/utils/parse-path.ts"], "sourcesContent": ["/**\n * Given a path this function will find the pathname, query and hash and return\n * them. This is useful to parse full paths on the client side.\n * @param path A path to parse e.g. /foo/bar?id=1#hash\n */\nexport function parsePath(path: string) {\n  const hashIndex = path.indexOf('#')\n  const queryIndex = path.indexOf('?')\n  const hasQuery = queryIndex > -1 && (hashIndex < 0 || queryIndex < hashIndex)\n\n  if (hasQuery || hashIndex > -1) {\n    return {\n      pathname: path.substring(0, hasQuery ? queryIndex : hashIndex),\n      query: hasQuery\n        ? path.substring(queryIndex, hashIndex > -1 ? hashIndex : undefined)\n        : '',\n      hash: hashIndex > -1 ? path.slice(hashIndex) : '',\n    }\n  }\n\n  return { pathname: path, query: '', hash: '' }\n}\n"], "names": ["parsePath", "path", "hashIndex", "indexOf", "queryIndex", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "substring", "query", "undefined", "hash", "slice"], "mappings": "AAAA;;;;CAIC,GAAA;;;;+BACeA,aAAAA;;;eAAAA;;;AAAT,SAASA,UAAUC,IAAY;IACpC,MAAMC,YAAYD,KAAKE,OAAO,CAAC;IAC/B,MAAMC,aAAaH,KAAKE,OAAO,CAAC;IAChC,MAAME,WAAWD,aAAa,CAAC,KAAMF,CAAAA,YAAY,KAAKE,aAAaF,SAAQ;IAE3E,IAAIG,YAAYH,YAAY,CAAC,GAAG;QAC9B,OAAO;YACLI,UAAUL,KAAKM,SAAS,CAAC,GAAGF,WAAWD,aAAaF;YACpDM,OAAOH,WACHJ,KAAKM,SAAS,CAACH,YAAYF,YAAY,CAAC,IAAIA,YAAYO,aACxD;YACJC,MAAMR,YAAY,CAAC,IAAID,KAAKU,KAAK,CAACT,aAAa;QACjD;IACF;IAEA,OAAO;QAAEI,UAAUL;QAAMO,OAAO;QAAIE,MAAM;IAAG;AAC/C", "ignoreList": [0]}}, {"offset": {"line": 504, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 509, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/shared/lib/router/utils/add-path-prefix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Adds the provided prefix to the given path. It first ensures that the path\n * is indeed starting with a slash.\n */\nexport function addPathPrefix(path: string, prefix?: string) {\n  if (!path.startsWith('/') || !prefix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${prefix}${pathname}${query}${hash}`\n}\n"], "names": ["addPathPrefix", "path", "prefix", "startsWith", "pathname", "query", "hash", "parsePath"], "mappings": ";;;;+BAMgBA,iBAAAA;;;eAAAA;;;2BANU;AAMnB,SAASA,cAAcC,IAAY,EAAEC,MAAe;IACzD,IAAI,CAACD,KAAKE,UAAU,CAAC,QAAQ,CAACD,QAAQ;QACpC,OAAOD;IACT;IAEA,MAAM,EAAEG,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,GAAGC,CAAAA,GAAAA,WAAAA,SAAS,EAACN;IAC5C,OAAQ,KAAEC,SAASE,WAAWC,QAAQC;AACxC", "ignoreList": [0]}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/shared/lib/router/utils/remove-trailing-slash.ts"], "sourcesContent": ["/**\n * Removes the trailing slash for a given route or page path. Preserves the\n * root page. Examples:\n *   - `/foo/bar/` -> `/foo/bar`\n *   - `/foo/bar` -> `/foo/bar`\n *   - `/` -> `/`\n */\nexport function removeTrailingSlash(route: string) {\n  return route.replace(/\\/$/, '') || '/'\n}\n"], "names": ["removeTrailingSlash", "route", "replace"], "mappings": "AAAA;;;;;;CAMC,GAAA;;;;+BACeA,uBAAAA;;;eAAAA;;;AAAT,SAASA,oBAAoBC,KAAa;IAC/C,OAAOA,MAAMC,OAAO,CAAC,OAAO,OAAO;AACrC", "ignoreList": [0]}}, {"offset": {"line": 551, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 556, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/shared/lib/server-inserted-html.shared-runtime.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useContext } from 'react'\n\nexport type ServerInsertedHTMLHook = (callbacks: () => React.ReactNode) => void\n\n// Use `React.createContext` to avoid errors from the RSC checks because\n// it can't be imported directly in Server Components:\n//\n//   import { createContext } from 'react'\n//\n// More info: https://github.com/vercel/next.js/pull/40686\nexport const ServerInsertedHTMLContext =\n  React.createContext<ServerInsertedHTMLHook | null>(null as any)\n\nexport function useServerInsertedHTML(callback: () => React.ReactNode): void {\n  const addInsertedServerHTMLCallback = useContext(ServerInsertedHTMLContext)\n  // Should have no effects on client where there's no flush effects provider\n  if (addInsertedServerHTMLCallback) {\n    addInsertedServerHTMLCallback(callback)\n  }\n}\n"], "names": ["ServerInsertedHTMLContext", "useServerInsertedHTML", "React", "createContext", "callback", "addInsertedServerHTMLCallback", "useContext"], "mappings": "AAAA;;;;;;;;;;;;;;;IAYaA,IAAAA,SAAAA,YAAyB;eAAzBA,gBAAAA;;IAGGC,qBAAqB;eAArBA,YAAAA;;;;iEAbkB;AAU3B,MAAMD,SAAAA,WAAAA,GAAAA,mBACXE,OAAAA,CAAAA,MAAK,CAACC,aAAa,CAAgC;AAE9C,MAAA,GAASF,sBAAsBG,GAAAA,KAA+B,MAAA,GAAA,OAAA,OAAA,CAAA,aAAA,CAAA;IACnE,KAAA,CAAMC,qBAAAA,QAAAA,GAAgCC,IAAAA,iBAAU,EAACN;IACjD,MAAA,gCAAA,CAAA,GAAA,OAAA,UAAA,EAAA,cAA2E;IAC3E,IAAIK,+BAA+B,wCAAA;QACjCA,8BAA8BD,CAAAA;QAChC,8BAAA;IACF", "ignoreList": [0]}}, {"offset": {"line": 589, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 594, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/shared/lib/router/utils/path-has-prefix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Checks if a given path starts with a given prefix. It ensures it matches\n * exactly without containing extra chars. e.g. prefix /docs should replace\n * for /docs, /docs/, /docs/a but not /docsss\n * @param path The path to check.\n * @param prefix The prefix to check against.\n */\nexport function pathHasPrefix(path: string, prefix: string) {\n  if (typeof path !== 'string') {\n    return false\n  }\n\n  const { pathname } = parsePath(path)\n  return pathname === prefix || pathname.startsWith(prefix + '/')\n}\n"], "names": ["pathHasPrefix", "path", "prefix", "pathname", "parsePath", "startsWith"], "mappings": ";;;;+BASgBA,iBAAAA;;;eAAAA;;;2BATU;AASnB,SAASA,cAAcC,IAAY,EAAEC,MAAc;IACxD,IAAI,OAAOD,SAAS,UAAU;QAC5B,OAAO;IACT;IAEA,MAAM,EAAEE,QAAQ,EAAE,GAAGC,CAAAA,GAAAA,WAAAA,SAAS,EAACH;IAC/B,OAAOE,aAAaD,UAAUC,SAASE,UAAU,CAACH,SAAS;AAC7D", "ignoreList": [0]}}, {"offset": {"line": 612, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 617, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/shared/lib/utils/warn-once.ts"], "sourcesContent": ["let warnOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const warnings = new Set<string>()\n  warnOnce = (msg: string) => {\n    if (!warnings.has(msg)) {\n      console.warn(msg)\n    }\n    warnings.add(msg)\n  }\n}\n\nexport { warnOnce }\n"], "names": ["warnOnce", "_", "process", "env", "NODE_ENV", "warnings", "Set", "msg", "has", "console", "warn", "add"], "mappings": "AACIE,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAUpBJ,YAAAA;;;eAAAA;;;AAXT,IAAIA,WAAW,CAACC,KAAe;AAC/B,wCAA2C;IACzC,MAAMI,WAAW,IAAIC;IACrBN,WAAW,CAACO;QACV,IAAI,CAACF,SAASG,GAAG,CAACD,MAAM;YACtBE,QAAQC,IAAI,CAACH;QACf;QACAF,SAASM,GAAG,CAACJ;IACf;AACF", "ignoreList": [0]}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 643, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/shared/lib/magic-identifier.ts"], "sourcesContent": ["function decodeHex(hexStr: string): string {\n  if (hexStr.trim() === '') {\n    throw new Error(\"can't decode empty hex\")\n  }\n\n  const num = parseInt(hexStr, 16)\n  if (isNaN(num)) {\n    throw new Error(`invalid hex: \\`${hexStr}\\``)\n  }\n\n  return String.fromCodePoint(num)\n}\n\nconst enum Mode {\n  Text,\n  Underscore,\n  Hex,\n  LongHex,\n}\n\nconst DECODE_REGEX = /^__TURBOPACK__([a-zA-Z0-9_$]+)__$/\n\nexport function decodeMagicIdentifier(identifier: string): string {\n  const matches = identifier.match(DECODE_REGEX)\n  if (!matches) {\n    return identifier\n  }\n\n  const inner = matches[1]\n\n  let output = ''\n\n  let mode: Mode = Mode.Text\n  let buffer = ''\n  for (let i = 0; i < inner.length; i++) {\n    const char = inner[i]\n\n    if (mode === Mode.Text) {\n      if (char === '_') {\n        mode = Mode.Underscore\n      } else if (char === '$') {\n        mode = Mode.Hex\n      } else {\n        output += char\n      }\n    } else if (mode === Mode.Underscore) {\n      if (char === '_') {\n        output += ' '\n        mode = Mode.Text\n      } else if (char === '$') {\n        output += '_'\n        mode = Mode.Hex\n      } else {\n        output += char\n        mode = Mode.Text\n      }\n    } else if (mode === Mode.Hex) {\n      if (buffer.length === 2) {\n        output += decodeHex(buffer)\n        buffer = ''\n      }\n\n      if (char === '_') {\n        if (buffer !== '') {\n          throw new Error(`invalid hex: \\`${buffer}\\``)\n        }\n\n        mode = Mode.LongHex\n      } else if (char === '$') {\n        if (buffer !== '') {\n          throw new Error(`invalid hex: \\`${buffer}\\``)\n        }\n\n        mode = Mode.Text\n      } else {\n        buffer += char\n      }\n    } else if (mode === Mode.LongHex) {\n      if (char === '_') {\n        throw new Error(`invalid hex: \\`${buffer + char}\\``)\n      } else if (char === '$') {\n        output += decodeHex(buffer)\n        buffer = ''\n\n        mode = Mode.Text\n      } else {\n        buffer += char\n      }\n    }\n  }\n\n  return output\n}\n\nexport const MAGIC_IDENTIFIER_REGEX = /__TURBOPACK__[a-zA-Z0-9_$]+__/g\n"], "names": ["MAGIC_IDENTIFIER_REGEX", "decodeMagicIdentifier", "decodeHex", "hexStr", "trim", "Error", "num", "parseInt", "isNaN", "String", "fromCodePoint", "DECODE_REGEX", "identifier", "matches", "match", "inner", "output", "mode", "buffer", "i", "length", "char"], "mappings": ";;;;;;;;;;;;;;;IA8FaA,sBAAsB,EAAA;eAAtBA;;IAxEGC,qBAAqB,EAAA;eAArBA;;;AAtBhB,SAASC,UAAUC,MAAc;IAC/B,IAAIA,OAAOC,IAAI,OAAO,IAAI;QACxB,MAAM,IAAIC,MAAM;IAClB;IAEA,MAAMC,MAAMC,SAASJ,QAAQ;IAC7B,IAAIK,MAAMF,MAAM;QACd,MAAM,IAAID,MAAO,mBAAiBF,SAAO;IAC3C;IAEA,OAAOM,OAAOC,aAAa,CAACJ;AAC9B;;AASA,MAAMK,eAAe;AAEd,SAASV,sBAAsBW,UAAkB;IACtD,MAAMC,UAAUD,WAAWE,KAAK,CAACH;IACjC,IAAI,CAACE,SAAS;QACZ,OAAOD;IACT;IAEA,MAAMG,QAAQF,OAAO,CAAC,EAAE;IAExB,IAAIG,SAAS;IAEb,IAAIC,OAAAA;IACJ,IAAIC,SAAS;IACb,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,MAAMK,MAAM,EAAED,IAAK;QACrC,MAAME,OAAON,KAAK,CAACI,EAAE;QAErB,IAAIF,SAAAA,GAAoB;YACtB,IAAII,SAAS,KAAK;gBAChBJ,OAAAA;YACF,OAAO,IAAII,SAAS,KAAK;gBACvBJ,OAAAA;YACF,OAAO;gBACLD,UAAUK;YACZ;QACF,OAAO,IAAIJ,SAAAA,GAA0B;YACnC,IAAII,SAAS,KAAK;gBAChBL,UAAU;gBACVC,OAAAA;YACF,OAAO,IAAII,SAAS,KAAK;gBACvBL,UAAU;gBACVC,OAAAA;YACF,OAAO;gBACLD,UAAUK;gBACVJ,OAAAA;YACF;QACF,OAAO,IAAIA,SAAAA,GAAmB;YAC5B,IAAIC,OAAOE,MAAM,KAAK,GAAG;gBACvBJ,UAAUd,UAAUgB;gBACpBA,SAAS;YACX;YAEA,IAAIG,SAAS,KAAK;gBAChB,IAAIH,WAAW,IAAI;oBACjB,MAAM,IAAIb,MAAO,mBAAiBa,SAAO;gBAC3C;gBAEAD,OAAAA;YACF,OAAO,IAAII,SAAS,KAAK;gBACvB,IAAIH,WAAW,IAAI;oBACjB,MAAM,IAAIb,MAAO,mBAAiBa,SAAO;gBAC3C;gBAEAD,OAAAA;YACF,OAAO;gBACLC,UAAUG;YACZ;QACF,OAAO,IAAIJ,SAAAA,GAAuB;YAChC,IAAII,SAAS,KAAK;gBAChB,MAAM,IAAIhB,MAAO,mBAAiBa,CAAAA,SAASG,IAAG,IAAE;YAClD,OAAO,IAAIA,SAAS,KAAK;gBACvBL,UAAUd,UAAUgB;gBACpBA,SAAS;gBAETD,OAAAA;YACF,OAAO;gBACLC,UAAUG;YACZ;QACF;IACF;IAEA,OAAOL;AACT;AAEO,MAAMhB,yBAAyB", "ignoreList": [0]}}, {"offset": {"line": 740, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 745, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/shared/lib/error-source.ts"], "sourcesContent": ["const symbolError = Symbol.for('NextjsError')\n\nexport function getErrorSource(error: Error): 'server' | 'edge-server' | null {\n  return (error as any)[symbolError] || null\n}\n\nexport type ErrorSourceType = 'edge-server' | 'server'\n\nexport function decorateServerError(error: Error, type: ErrorSourceType) {\n  Object.defineProperty(error, symbolError, {\n    writable: false,\n    enumerable: false,\n    configurable: false,\n    value: type,\n  })\n}\n"], "names": ["decorateServerError", "getErrorSource", "symbolError", "Symbol", "for", "error", "type", "Object", "defineProperty", "writable", "enumerable", "configurable", "value"], "mappings": ";;;;;;;;;;;;;;;IAQgBA,mBAAmB,EAAA;eAAnBA;;IANAC,cAAc,EAAA;eAAdA;;;AAFhB,MAAMC,cAAcC,OAAOC,GAAG,CAAC;AAExB,SAASH,eAAeI,KAAY;IACzC,OAAQA,KAAa,CAACH,YAAY,IAAI;AACxC;AAIO,SAASF,oBAAoBK,KAAY,EAAEC,IAAqB;IACrEC,OAAOC,cAAc,CAACH,OAAOH,aAAa;QACxCO,UAAU;QACVC,YAAY;QACZC,cAAc;QACdC,OAAON;IACT;AACF", "ignoreList": [0]}}, {"offset": {"line": 779, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/shared/lib/normalized-asset-prefix.ts"], "sourcesContent": ["export function normalizedAssetPrefix(assetPrefix: string | undefined): string {\n  // remove all leading slashes and trailing slashes\n  const escapedAssetPrefix = assetPrefix?.replace(/^\\/+|\\/+$/g, '') || false\n\n  // if an assetPrefix was '/', we return empty string\n  // because it could be an unnecessary trailing slash\n  if (!escapedAssetPrefix) {\n    return ''\n  }\n\n  if (URL.canParse(escapedAssetPrefix)) {\n    const url = new URL(escapedAssetPrefix).toString()\n    return url.endsWith('/') ? url.slice(0, -1) : url\n  }\n\n  // assuming assetPrefix here is a pathname-style,\n  // restore the leading slash\n  return `/${escapedAssetPrefix}`\n}\n"], "names": ["normalizedAssetPrefix", "assetPrefix", "escapedAssetPrefix", "replace", "URL", "canParse", "url", "toString", "endsWith", "slice"], "mappings": ";;;;+BAAgBA,yBAAAA;;;eAAAA;;;AAAT,SAASA,sBAAsBC,WAA+B;IACnE,kDAAkD;IAClD,MAAMC,qBAAqBD,CAAAA,eAAAA,OAAAA,KAAAA,IAAAA,YAAaE,OAAO,CAAC,cAAc,GAAA,KAAO;IAErE,oDAAoD;IACpD,oDAAoD;IACpD,IAAI,CAACD,oBAAoB;QACvB,OAAO;IACT;IAEA,IAAIE,IAAIC,QAAQ,CAACH,qBAAqB;QACpC,MAAMI,MAAM,IAAIF,IAAIF,oBAAoBK,QAAQ;QAChD,OAAOD,IAAIE,QAAQ,CAAC,OAAOF,IAAIG,KAAK,CAAC,GAAG,CAAC,KAAKH;IAChD;IAEA,iDAAiD;IACjD,4BAA4B;IAC5B,OAAQ,MAAGJ;AACb", "ignoreList": [0]}}, {"offset": {"line": 810, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 815, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/shared/lib/router/action-queue.ts"], "sourcesContent": ["import {\n  type AppRouterState,\n  type ReducerActions,\n  type ReducerState,\n  ACTION_REFRESH,\n  ACTION_SERVER_ACTION,\n  ACTION_NAVIGATE,\n  ACTION_RESTORE,\n} from '../../../client/components/router-reducer/router-reducer-types'\nimport { reducer } from '../../../client/components/router-reducer/router-reducer'\nimport { startTransition } from 'react'\nimport { isThenable } from '../is-thenable'\n\nexport type DispatchStatePromise = React.Dispatch<ReducerState>\n\nexport type AppRouterActionQueue = {\n  state: AppRouterState\n  dispatch: (payload: ReducerActions, setState: DispatchStatePromise) => void\n  action: (state: AppRouterState, action: ReducerActions) => ReducerState\n  pending: ActionQueueNode | null\n  needsRefresh?: boolean\n  last: ActionQueueNode | null\n}\n\nexport type ActionQueueNode = {\n  payload: ReducerActions\n  next: ActionQueueNode | null\n  resolve: (value: ReducerState) => void\n  reject: (err: Error) => void\n  discarded?: boolean\n}\n\nfunction runRemainingActions(\n  actionQueue: AppRouterActionQueue,\n  setState: DispatchStatePromise\n) {\n  if (actionQueue.pending !== null) {\n    actionQueue.pending = actionQueue.pending.next\n    if (actionQueue.pending !== null) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      runAction({\n        actionQueue,\n        action: actionQueue.pending,\n        setState,\n      })\n    } else {\n      // No more actions are pending, check if a refresh is needed\n      if (actionQueue.needsRefresh) {\n        actionQueue.needsRefresh = false\n        actionQueue.dispatch(\n          {\n            type: ACTION_REFRESH,\n            origin: window.location.origin,\n          },\n          setState\n        )\n      }\n    }\n  }\n}\n\nasync function runAction({\n  actionQueue,\n  action,\n  setState,\n}: {\n  actionQueue: AppRouterActionQueue\n  action: ActionQueueNode\n  setState: DispatchStatePromise\n}) {\n  const prevState = actionQueue.state\n\n  actionQueue.pending = action\n\n  const payload = action.payload\n  const actionResult = actionQueue.action(prevState, payload)\n\n  function handleResult(nextState: AppRouterState) {\n    // if we discarded this action, the state should also be discarded\n    if (action.discarded) {\n      return\n    }\n\n    actionQueue.state = nextState\n\n    runRemainingActions(actionQueue, setState)\n    action.resolve(nextState)\n  }\n\n  // if the action is a promise, set up a callback to resolve it\n  if (isThenable(actionResult)) {\n    actionResult.then(handleResult, (err) => {\n      runRemainingActions(actionQueue, setState)\n      action.reject(err)\n    })\n  } else {\n    handleResult(actionResult)\n  }\n}\n\nfunction dispatchAction(\n  actionQueue: AppRouterActionQueue,\n  payload: ReducerActions,\n  setState: DispatchStatePromise\n) {\n  let resolvers: {\n    resolve: (value: ReducerState) => void\n    reject: (reason: any) => void\n  } = { resolve: setState, reject: () => {} }\n\n  // most of the action types are async with the exception of restore\n  // it's important that restore is handled quickly since it's fired on the popstate event\n  // and we don't want to add any delay on a back/forward nav\n  // this only creates a promise for the async actions\n  if (payload.type !== ACTION_RESTORE) {\n    // Create the promise and assign the resolvers to the object.\n    const deferredPromise = new Promise<AppRouterState>((resolve, reject) => {\n      resolvers = { resolve, reject }\n    })\n\n    startTransition(() => {\n      // we immediately notify React of the pending promise -- the resolver is attached to the action node\n      // and will be called when the associated action promise resolves\n      setState(deferredPromise)\n    })\n  }\n\n  const newAction: ActionQueueNode = {\n    payload,\n    next: null,\n    resolve: resolvers.resolve,\n    reject: resolvers.reject,\n  }\n\n  // Check if the queue is empty\n  if (actionQueue.pending === null) {\n    // The queue is empty, so add the action and start it immediately\n    // Mark this action as the last in the queue\n    actionQueue.last = newAction\n\n    runAction({\n      actionQueue,\n      action: newAction,\n      setState,\n    })\n  } else if (\n    payload.type === ACTION_NAVIGATE ||\n    payload.type === ACTION_RESTORE\n  ) {\n    // Navigations (including back/forward) take priority over any pending actions.\n    // Mark the pending action as discarded (so the state is never applied) and start the navigation action immediately.\n    actionQueue.pending.discarded = true\n\n    // Mark this action as the last in the queue\n    actionQueue.last = newAction\n\n    // if the pending action was a server action, mark the queue as needing a refresh once events are processed\n    if (actionQueue.pending.payload.type === ACTION_SERVER_ACTION) {\n      actionQueue.needsRefresh = true\n    }\n\n    runAction({\n      actionQueue,\n      action: newAction,\n      setState,\n    })\n  } else {\n    // The queue is not empty, so add the action to the end of the queue\n    // It will be started by runRemainingActions after the previous action finishes\n    if (actionQueue.last !== null) {\n      actionQueue.last.next = newAction\n    }\n    actionQueue.last = newAction\n  }\n}\n\nexport function createMutableActionQueue(\n  initialState: AppRouterState\n): AppRouterActionQueue {\n  const actionQueue: AppRouterActionQueue = {\n    state: initialState,\n    dispatch: (payload: ReducerActions, setState: DispatchStatePromise) =>\n      dispatchAction(actionQueue, payload, setState),\n    action: async (state: AppRouterState, action: ReducerActions) => {\n      const result = reducer(state, action)\n      return result\n    },\n    pending: null,\n    last: null,\n  }\n\n  return actionQueue\n}\n"], "names": ["createMutableActionQueue", "runRemainingActions", "actionQueue", "setState", "pending", "next", "runAction", "action", "needsRefresh", "dispatch", "type", "ACTION_REFRESH", "origin", "window", "location", "prevState", "state", "payload", "actionResult", "handleResult", "nextState", "discarded", "resolve", "isThenable", "then", "err", "reject", "dispatchAction", "resolvers", "ACTION_RESTORE", "deferred<PERSON><PERSON><PERSON>", "Promise", "startTransition", "newAction", "last", "ACTION_NAVIGATE", "ACTION_SERVER_ACTION", "initialState", "result", "reducer"], "mappings": ";;;;+BAgLgBA,4BAAAA;;;eAAAA;;;oCAxKT;+BACiB;uBACQ;4BACL;AAqB3B,SAASC,oBACPC,WAAiC,EACjCC,QAA8B;IAE9B,IAAID,YAAYE,OAAO,KAAK,MAAM;QAChCF,YAAYE,OAAO,GAAGF,YAAYE,OAAO,CAACC,IAAI;QAC9C,IAAIH,YAAYE,OAAO,KAAK,MAAM;YAChC,mEAAmE;YACnEE,UAAU;gBACRJ;gBACAK,QAAQL,YAAYE,OAAO;gBAC3BD;YACF;QACF,OAAO;YACL,4DAA4D;YAC5D,IAAID,YAAYM,YAAY,EAAE;gBAC5BN,YAAYM,YAAY,GAAG;gBAC3BN,YAAYO,QAAQ,CAClB;oBACEC,MAAMC,oBAAAA,cAAc;oBACpBC,QAAQC,OAAOC,QAAQ,CAACF,MAAM;gBAChC,GACAT;YAEJ;QACF;IACF;AACF;AAEA,eAAeG,UAAU,KAQxB;IARwB,IAAA,EACvBJ,WAAW,EACXK,MAAM,EACNJ,QAAQ,EAKT,GARwB;IASvB,MAAMY,YAAYb,YAAYc,KAAK;IAEnCd,YAAYE,OAAO,GAAGG;IAEtB,MAAMU,UAAUV,OAAOU,OAAO;IAC9B,MAAMC,eAAehB,YAAYK,MAAM,CAACQ,WAAWE;IAEnD,SAASE,aAAaC,SAAyB;QAC7C,kEAAkE;QAClE,IAAIb,OAAOc,SAAS,EAAE;YACpB;QACF;QAEAnB,YAAYc,KAAK,GAAGI;QAEpBnB,oBAAoBC,aAAaC;QACjCI,OAAOe,OAAO,CAACF;IACjB;IAEA,8DAA8D;IAC9D,IAAIG,CAAAA,GAAAA,YAAAA,UAAU,EAACL,eAAe;QAC5BA,aAAaM,IAAI,CAACL,cAAc,CAACM;YAC/BxB,oBAAoBC,aAAaC;YACjCI,OAAOmB,MAAM,CAACD;QAChB;IACF,OAAO;QACLN,aAAaD;IACf;AACF;AAEA,SAASS,eACPzB,WAAiC,EACjCe,OAAuB,EACvBd,QAA8B;IAE9B,IAAIyB,YAGA;QAAEN,SAASnB;QAAUuB,QAAQ,KAAO;IAAE;IAE1C,mEAAmE;IACnE,wFAAwF;IACxF,2DAA2D;IAC3D,oDAAoD;IACpD,IAAIT,QAAQP,IAAI,KAAKmB,oBAAAA,cAAc,EAAE;QACnC,6DAA6D;QAC7D,MAAMC,kBAAkB,IAAIC,QAAwB,CAACT,SAASI;YAC5DE,YAAY;gBAAEN;gBAASI;YAAO;QAChC;QAEAM,CAAAA,GAAAA,OAAAA,eAAe,EAAC;YACd,oGAAoG;YACpG,iEAAiE;YACjE7B,SAAS2B;QACX;IACF;IAEA,MAAMG,YAA6B;QACjChB;QACAZ,MAAM;QACNiB,SAASM,UAAUN,OAAO;QAC1BI,QAAQE,UAAUF,MAAM;IAC1B;IAEA,8BAA8B;IAC9B,IAAIxB,YAAYE,OAAO,KAAK,MAAM;QAChC,iEAAiE;QACjE,4CAA4C;QAC5CF,YAAYgC,IAAI,GAAGD;QAEnB3B,UAAU;YACRJ;YACAK,QAAQ0B;YACR9B;QACF;IACF,OAAO,IACLc,QAAQP,IAAI,KAAKyB,oBAAAA,eAAe,IAChClB,QAAQP,IAAI,KAAKmB,oBAAAA,cAAc,EAC/B;QACA,+EAA+E;QAC/E,oHAAoH;QACpH3B,YAAYE,OAAO,CAACiB,SAAS,GAAG;QAEhC,4CAA4C;QAC5CnB,YAAYgC,IAAI,GAAGD;QAEnB,2GAA2G;QAC3G,IAAI/B,YAAYE,OAAO,CAACa,OAAO,CAACP,IAAI,KAAK0B,oBAAAA,oBAAoB,EAAE;YAC7DlC,YAAYM,YAAY,GAAG;QAC7B;QAEAF,UAAU;YACRJ;YACAK,QAAQ0B;YACR9B;QACF;IACF,OAAO;QACL,oEAAoE;QACpE,+EAA+E;QAC/E,IAAID,YAAYgC,IAAI,KAAK,MAAM;YAC7BhC,YAAYgC,IAAI,CAAC7B,IAAI,GAAG4B;QAC1B;QACA/B,YAAYgC,IAAI,GAAGD;IACrB;AACF;AAEO,SAASjC,yBACdqC,YAA4B;IAE5B,MAAMnC,cAAoC;QACxCc,OAAOqB;QACP5B,UAAU,CAACQ,SAAyBd,WAClCwB,eAAezB,aAAae,SAASd;QACvCI,QAAQ,OAAOS,OAAuBT;YACpC,MAAM+B,SAASC,CAAAA,GAAAA,eAAAA,OAAO,EAACvB,OAAOT;YAC9B,OAAO+B;QACT;QACAlC,SAAS;QACT8B,MAAM;IACR;IAEA,OAAOhC;AACT", "ignoreList": [0]}}, {"offset": {"line": 952, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 957, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/lib/is-error.ts"], "sourcesContent": ["import { isPlainObject } from '../shared/lib/is-plain-object'\n\n// We allow some additional attached properties for Next.js errors\nexport interface NextError extends Error {\n  type?: string\n  page?: string\n  code?: string | number\n  cancelled?: boolean\n  digest?: number\n}\n\n/**\n * Checks whether the given value is a NextError.\n * This can be used to print a more detailed error message with properties like `code` & `digest`.\n */\nexport default function isError(err: unknown): err is NextError {\n  return (\n    typeof err === 'object' && err !== null && 'name' in err && 'message' in err\n  )\n}\n\nfunction safeStringify(obj: any) {\n  const seen = new WeakSet()\n\n  return JSON.stringify(obj, (_key, value) => {\n    // If value is an object and already seen, replace with \"[Circular]\"\n    if (typeof value === 'object' && value !== null) {\n      if (seen.has(value)) {\n        return '[Circular]'\n      }\n      seen.add(value)\n    }\n    return value\n  })\n}\n\nexport function getProperError(err: unknown): Error {\n  if (isError(err)) {\n    return err\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    // provide better error for case where `throw undefined`\n    // is called in development\n    if (typeof err === 'undefined') {\n      return new Error(\n        'An undefined error was thrown, ' +\n          'see here for more info: https://nextjs.org/docs/messages/threw-undefined'\n      )\n    }\n\n    if (err === null) {\n      return new Error(\n        'A null error was thrown, ' +\n          'see here for more info: https://nextjs.org/docs/messages/threw-undefined'\n      )\n    }\n  }\n\n  return new Error(isPlainObject(err) ? safeStringify(err) : err + '')\n}\n"], "names": ["isError", "getProperError", "err", "safeStringify", "obj", "seen", "WeakSet", "JSON", "stringify", "_key", "value", "has", "add", "process", "env", "NODE_ENV", "Error", "isPlainObject"], "mappings": "AAyCMa,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;IA9B/B;;;CAGC,GACD,OAIC,EAAA;eAJuBf;;IAqBRC,cAAc,EAAA;eAAdA;;;+BApCc;AAef,SAASD,QAAQE,GAAY;IAC1C,OACE,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,UAAUA,OAAO,aAAaA;AAE7E;AAEA,SAASC,cAAcC,GAAQ;IAC7B,MAAMC,OAAO,IAAIC;IAEjB,OAAOC,KAAKC,SAAS,CAACJ,KAAK,CAACK,MAAMC;QAChC,oEAAoE;QACpE,IAAI,OAAOA,UAAU,YAAYA,UAAU,MAAM;YAC/C,IAAIL,KAAKM,GAAG,CAACD,QAAQ;gBACnB,OAAO;YACT;YACAL,KAAKO,GAAG,CAACF;QACX;QACA,OAAOA;IACT;AACF;AAEO,SAAST,eAAeC,GAAY;IACzC,IAAIF,QAAQE,MAAM;QAChB,OAAOA;IACT;IAEA,wCAA4C;QAC1C,wDAAwD;QACxD,2BAA2B;QAC3B,IAAI,OAAOA,QAAQ,aAAa;YAC9B,OAAO,IAAIc,MACT,oCACE;QAEN;QAEA,IAAId,QAAQ,MAAM;YAChB,OAAO,IAAIc,MACT,8BACE;QAEN;IACF;IAEA,OAAO,IAAIA,MAAMC,CAAAA,GAAAA,eAAAA,aAAa,EAACf,OAAOC,cAAcD,OAAOA,MAAM;AACnE", "ignoreList": [0]}}, {"offset": {"line": 1016, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1021, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/lib/metadata/metadata-constants.tsx"], "sourcesContent": ["export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\n"], "names": ["METADATA_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME"], "mappings": ";;;;;;;;;;;;;;;;IAAaA,sBAAsB,EAAA;eAAtBA;;IAEAC,oBAAoB,EAAA;eAApBA;;IADAC,sBAAsB,EAAA;eAAtBA;;;AADN,MAAMF,yBAAyB;AAC/B,MAAME,yBAAyB;AAC/B,MAAMD,uBAAuB", "ignoreList": [0]}}, {"offset": {"line": 1050, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1055, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/server/app-render/async-local-storage.ts"], "sourcesContent": ["import type { AsyncLocalStorage } from 'async_hooks'\n\nconst sharedAsyncLocalStorageNotAvailableError = new Error(\n  'Invariant: AsyncLocalStorage accessed in runtime where it is not available'\n)\n\nclass FakeAsyncLocalStorage<Store extends {}>\n  implements AsyncLocalStorage<Store>\n{\n  disable(): void {\n    throw sharedAsyncLocalStorageNotAvailableError\n  }\n\n  getStore(): Store | undefined {\n    // This fake implementation of AsyncLocalStorage always returns `undefined`.\n    return undefined\n  }\n\n  run<R>(): R {\n    throw sharedAsyncLocalStorageNotAvailableError\n  }\n\n  exit<R>(): R {\n    throw sharedAsyncLocalStorageNotAvailableError\n  }\n\n  enterWith(): void {\n    throw sharedAsyncLocalStorageNotAvailableError\n  }\n\n  static bind<T>(fn: T): T {\n    return fn\n  }\n}\n\nconst maybeGlobalAsyncLocalStorage =\n  typeof globalThis !== 'undefined' && (globalThis as any).AsyncLocalStorage\n\nexport function createAsyncLocalStorage<\n  Store extends {},\n>(): AsyncLocalStorage<Store> {\n  if (maybeGlobalAsyncLocalStorage) {\n    return new maybeGlobalAsyncLocalStorage()\n  }\n  return new FakeAsyncLocalStorage()\n}\n\nexport function bindSnapshot<T>(fn: T): T {\n  if (maybeGlobalAsyncLocalStorage) {\n    return maybeGlobalAsyncLocalStorage.bind(fn)\n  }\n  return FakeAsyncLocalStorage.bind(fn)\n}\n\nexport function createSnapshot(): <R, TArgs extends any[]>(\n  fn: (...args: TArgs) => R,\n  ...args: TArgs\n) => R {\n  if (maybeGlobalAsyncLocalStorage) {\n    return maybeGlobalAsyncLocalStorage.snapshot()\n  }\n  return function (fn: any, ...args: any[]) {\n    return fn(...args)\n  }\n}\n"], "names": ["bindSnapshot", "createAsyncLocalStorage", "createSnapshot", "sharedAsyncLocalStorageNotAvailableError", "Error", "FakeAsyncLocalStorage", "disable", "getStore", "undefined", "run", "exit", "enterWith", "bind", "fn", "maybeGlobalAsyncLocalStorage", "globalThis", "AsyncLocalStorage", "snapshot", "args"], "mappings": ";;;;;;;;;;;;;;;;IA+CgBA,YAAY,EAAA;eAAZA;;IATAC,uBAAuB,EAAA;eAAvBA;;IAgBAC,cAAc,EAAA;eAAdA;;;AApDhB,MAAMC,2CAA2C,IAAIC,MACnD;AAGF,MAAMC;IAGJC,UAAgB;QACd,MAAMH;IACR;IAEAI,WAA8B;QAC5B,4EAA4E;QAC5E,OAAOC;IACT;IAEAC,MAAY;QACV,MAAMN;IACR;IAEAO,OAAa;QACX,MAAMP;IACR;IAEAQ,YAAkB;QAChB,MAAMR;IACR;IAEA,OAAOS,KAAQC,EAAK,EAAK;QACvB,OAAOA;IACT;AACF;AAEA,MAAMC,+BACJ,OAAOC,eAAe,eAAgBA,WAAmBC,iBAAiB;AAErE,SAASf;IAGd,IAAIa,8BAA8B;QAChC,OAAO,IAAIA;IACb;IACA,OAAO,IAAIT;AACb;AAEO,SAASL,aAAgBa,EAAK;IACnC,IAAIC,8BAA8B;QAChC,OAAOA,6BAA6BF,IAAI,CAACC;IAC3C;IACA,OAAOR,sBAAsBO,IAAI,CAACC;AACpC;AAEO,SAASX;IAId,IAAIY,8BAA8B;QAChC,OAAOA,6BAA6BG,QAAQ;IAC9C;IACA,OAAO,SAAUJ,EAAO,EAAE,GAAGK,IAAW;QACtC,OAAOL,MAAMK;IACf;AACF", "ignoreList": [0]}}, {"offset": {"line": 1124, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1129, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/server/app-render/action-async-storage-instance.ts"], "sourcesContent": ["import type { ActionAsyncStorage } from './action-async-storage.external'\nimport { createAsyncLocalStorage } from './async-local-storage'\n\nexport const actionAsyncStorage: ActionAsyncStorage = createAsyncLocalStorage()\n"], "names": ["actionAsyncStorage", "createAsyncLocalStorage"], "mappings": ";;;;+BAGaA,sBAAAA;;;eAAAA;;;mCAF2B;AAEjC,MAAMA,qBAAyCC,CAAAA,GAAAA,mBAAAA,uBAAuB", "ignoreList": [0]}}, {"offset": {"line": 1141, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1146, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/server/app-render/action-async-storage.external.ts"], "sourcesContent": ["import type { AsyncLocalStorage } from 'async_hooks'\n\n// Share the instance module in the next-shared layer\nimport { actionAsyncStorage } from './action-async-storage-instance' with { 'turbopack-transition': 'next-shared' }\nexport interface ActionStore {\n  readonly isAction?: boolean\n  readonly isAppRoute?: boolean\n}\n\nexport type ActionAsyncStorage = AsyncLocalStorage<ActionStore>\n\nexport { actionAsyncStorage }\n"], "names": ["actionAsyncStorage"], "mappings": ";;;;+BAWSA,sBAAAA;;;eAAAA,4BAAAA,kBAAkB;;;4CARQ", "ignoreList": [0]}}, {"offset": {"line": 1157, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1162, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/server/lib/interception-routes.ts"], "sourcesContent": ["import { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\n\n// order matters here, the first match will be used\nexport const INTERCEPTION_ROUTE_MARKERS = [\n  '(..)(..)',\n  '(.)',\n  '(..)',\n  '(...)',\n] as const\n\nexport function isInterceptionRouteAppPath(path: string): boolean {\n  // TODO-APP: add more serious validation\n  return (\n    path\n      .split('/')\n      .find((segment) =>\n        INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n      ) !== undefined\n  )\n}\n\nexport function extractInterceptionRouteInformation(path: string) {\n  let interceptingRoute: string | undefined,\n    marker: (typeof INTERCEPTION_ROUTE_MARKERS)[number] | undefined,\n    interceptedRoute: string | undefined\n\n  for (const segment of path.split('/')) {\n    marker = INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n    if (marker) {\n      ;[interceptingRoute, interceptedRoute] = path.split(marker, 2)\n      break\n    }\n  }\n\n  if (!interceptingRoute || !marker || !interceptedRoute) {\n    throw new Error(\n      `Invalid interception route: ${path}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`\n    )\n  }\n\n  interceptingRoute = normalizeAppPath(interceptingRoute) // normalize the path, e.g. /(blog)/feed -> /feed\n\n  switch (marker) {\n    case '(.)':\n      // (.) indicates that we should match with sibling routes, so we just need to append the intercepted route to the intercepting route\n      if (interceptingRoute === '/') {\n        interceptedRoute = `/${interceptedRoute}`\n      } else {\n        interceptedRoute = interceptingRoute + '/' + interceptedRoute\n      }\n      break\n    case '(..)':\n      // (..) indicates that we should match at one level up, so we need to remove the last segment of the intercepting route\n      if (interceptingRoute === '/') {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..) marker at the root level, use (.) instead.`\n        )\n      }\n      interceptedRoute = interceptingRoute\n        .split('/')\n        .slice(0, -1)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    case '(...)':\n      // (...) will match the route segment in the root directory, so we need to use the root directory to prepend the intercepted route\n      interceptedRoute = '/' + interceptedRoute\n      break\n    case '(..)(..)':\n      // (..)(..) indicates that we should match at two levels up, so we need to remove the last two segments of the intercepting route\n\n      const splitInterceptingRoute = interceptingRoute.split('/')\n      if (splitInterceptingRoute.length <= 2) {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..)(..) marker at the root level or one level up.`\n        )\n      }\n\n      interceptedRoute = splitInterceptingRoute\n        .slice(0, -2)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    default:\n      throw new Error('Invariant: unexpected marker')\n  }\n\n  return { interceptingRoute, interceptedRoute }\n}\n"], "names": ["INTERCEPTION_ROUTE_MARKERS", "extractInterceptionRouteInformation", "isInterceptionRouteAppPath", "path", "split", "find", "segment", "m", "startsWith", "undefined", "interceptingRoute", "marker", "interceptedRoute", "Error", "normalizeAppPath", "slice", "concat", "join", "splitInterceptingRoute", "length"], "mappings": ";;;;;;;;;;;;;;;;IAGaA,0BAA0B,EAAA;eAA1BA;;IAkBGC,mCAAmC,EAAA;eAAnCA;;IAXAC,0BAA0B,EAAA;eAA1BA;;;0BAViB;AAG1B,MAAMF,6BAA6B;IACxC;IACA;IACA;IACA;CACD;AAEM,SAASE,2BAA2BC,IAAY;IACrD,wCAAwC;IACxC,OACEA,KACGC,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UACLN,2BAA2BK,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD,SACtDE;AAEZ;AAEO,SAASR,oCAAoCE,IAAY;IAC9D,IAAIO,mBACFC,QACAC;IAEF,KAAK,MAAMN,WAAWH,KAAKC,KAAK,CAAC,KAAM;QACrCO,SAASX,2BAA2BK,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD;QACnE,IAAII,QAAQ;YACT,CAACD,mBAAmBE,iBAAiB,GAAGT,KAAKC,KAAK,CAACO,QAAQ;YAC5D;QACF;IACF;IAEA,IAAI,CAACD,qBAAqB,CAACC,UAAU,CAACC,kBAAkB;QACtD,MAAM,IAAIC,MACR,CAAC,4BAA4B,EAAEV,KAAK,iFAAiF,CAAC;IAE1H;IAEAO,oBAAoBI,CAAAA,GAAAA,UAAAA,gBAAgB,EAACJ,mBAAmB,iDAAiD;;IAEzG,OAAQC;QACN,KAAK;YACH,oIAAoI;YACpI,IAAID,sBAAsB,KAAK;gBAC7BE,mBAAmB,CAAC,CAAC,EAAEA,iBAAiB,CAAC;YAC3C,OAAO;gBACLA,mBAAmBF,oBAAoB,MAAME;YAC/C;YACA;QACF,KAAK;YACH,uHAAuH;YACvH,IAAIF,sBAAsB,KAAK;gBAC7B,MAAM,IAAIG,MACR,CAAC,4BAA4B,EAAEV,KAAK,4DAA4D,CAAC;YAErG;YACAS,mBAAmBF,kBAChBN,KAAK,CAAC,KACNW,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACJ,kBACPK,IAAI,CAAC;YACR;QACF,KAAK;YACH,kIAAkI;YAClIL,mBAAmB,MAAMA;YACzB;QACF,KAAK;YACH,iIAAiI;YAEjI,MAAMM,yBAAyBR,kBAAkBN,KAAK,CAAC;YACvD,IAAIc,uBAAuBC,MAAM,IAAI,GAAG;gBACtC,MAAM,IAAIN,MACR,CAAC,4BAA4B,EAAEV,KAAK,+DAA+D,CAAC;YAExG;YAEAS,mBAAmBM,uBAChBH,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACJ,kBACPK,IAAI,CAAC;YACR;QACF;YACE,MAAM,IAAIJ,MAAM;IACpB;IAEA,OAAO;QAAEH;QAAmBE;IAAiB;AAC/C", "ignoreList": [0]}}, {"offset": {"line": 1249, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1254, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/server/app-render/get-segment-param.tsx"], "sourcesContent": ["import { INTERCEPTION_ROUTE_MARKERS } from '../lib/interception-routes'\nimport type { DynamicParamTypes } from './types'\n\n/**\n * Parse dynamic route segment to type of parameter\n */\nexport function getSegmentParam(segment: string): {\n  param: string\n  type: DynamicParamTypes\n} | null {\n  const interceptionMarker = INTERCEPTION_ROUTE_MARKERS.find((marker) =>\n    segment.startsWith(marker)\n  )\n\n  // if an interception marker is part of the path segment, we need to jump ahead\n  // to the relevant portion for param parsing\n  if (interceptionMarker) {\n    segment = segment.slice(interceptionMarker.length)\n  }\n\n  if (segment.startsWith('[[...') && segment.endsWith(']]')) {\n    return {\n      // TODO-APP: Optional catchall does not currently work with parallel routes,\n      // so for now aren't handling a potential interception marker.\n      type: 'optional-catchall',\n      param: segment.slice(5, -2),\n    }\n  }\n\n  if (segment.startsWith('[...') && segment.endsWith(']')) {\n    return {\n      type: interceptionMarker ? 'catchall-intercepted' : 'catchall',\n      param: segment.slice(4, -1),\n    }\n  }\n\n  if (segment.startsWith('[') && segment.endsWith(']')) {\n    return {\n      type: interceptionMarker ? 'dynamic-intercepted' : 'dynamic',\n      param: segment.slice(1, -1),\n    }\n  }\n\n  return null\n}\n"], "names": ["getSegmentParam", "segment", "<PERSON><PERSON><PERSON><PERSON>", "INTERCEPTION_ROUTE_MARKERS", "find", "marker", "startsWith", "slice", "length", "endsWith", "type", "param"], "mappings": ";;;;+BAMg<PERSON>,mBAAAA;;;eAAAA;;;oCAN2B;AAMpC,SAASA,gBAAgBC,OAAe;IAI7C,MAAMC,qBAAqBC,oBAAAA,0BAA0B,CAACC,IAAI,CAAC,CAACC,SAC1DJ,QAAQK,UAAU,CAACD;IAGrB,+EAA+E;IAC/E,4CAA4C;IAC5C,IAAIH,oBAAoB;QACtBD,UAAUA,QAAQM,KAAK,CAACL,mBAAmBM,MAAM;IACnD;IAEA,IAAIP,QAAQK,UAAU,CAAC,YAAYL,QAAQQ,QAAQ,CAAC,OAAO;QACzD,OAAO;YACL,4EAA4E;YAC5E,8DAA8D;YAC9DC,MAAM;YACNC,OAAOV,QAAQM,KAAK,CAAC,GAAG,CAAC;QAC3B;IACF;IAEA,IAAIN,QAAQK,UAAU,CAAC,WAAWL,QAAQQ,QAAQ,CAAC,MAAM;QACvD,OAAO;YACLC,MAAMR,qBAAqB,yBAAyB;YACpDS,OAAOV,QAAQM,KAAK,CAAC,GAAG,CAAC;QAC3B;IACF;IAEA,IAAIN,QAAQK,UAAU,CAAC,QAAQL,QAAQQ,QAAQ,CAAC,MAAM;QACpD,OAAO;YACLC,MAAMR,qBAAqB,wBAAwB;YACnDS,OAAOV,QAAQM,KAAK,CAAC,GAAG,CAAC;QAC3B;IACF;IAEA,OAAO;AACT", "ignoreList": [0]}}, {"offset": {"line": 1294, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1299, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/server/app-render/work-async-storage-instance.ts"], "sourcesContent": ["import type { WorkAsyncStorage } from './work-async-storage.external'\nimport { createAsyncLocalStorage } from './async-local-storage'\n\nexport const workAsyncStorage: WorkAsyncStorage = createAsyncLocalStorage()\n"], "names": ["workAsyncStorage", "createAsyncLocalStorage"], "mappings": ";;;;+BAGaA,oBAAAA;;;eAAAA;;;mCAF2B;AAEjC,MAAMA,mBAAqCC,CAAAA,GAAAA,mBAAAA,uBAAuB", "ignoreList": [0]}}, {"offset": {"line": 1311, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1316, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/server/app-render/work-async-storage.external.ts"], "sourcesContent": ["import type { AsyncLocalStorage } from 'async_hooks'\nimport type { IncrementalCache } from '../lib/incremental-cache'\nimport type { DynamicServerError } from '../../client/components/hooks-server-context'\nimport type { FetchMetrics } from '../base-http'\nimport type { FallbackRouteParams } from '../request/fallback-params'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport type { AppSegmentConfig } from '../../build/segment-config/app/app-segment-config'\nimport type { AfterContext } from '../after/after-context'\nimport type { CacheLife } from '../use-cache/cache-life'\n\n// Share the instance module in the next-shared layer\nimport { workAsyncStorage } from './work-async-storage-instance' with { 'turbopack-transition': 'next-shared' }\n\nexport interface WorkStore {\n  readonly isStaticGeneration: boolean\n\n  /**\n   * The page that is being rendered. This relates to the path to the page file.\n   */\n  readonly page: string\n\n  /**\n   * The route that is being rendered. This is the page property without the\n   * trailing `/page` or `/route` suffix.\n   */\n  readonly route: string\n\n  /**\n   * The set of unknown route parameters. Accessing these will be tracked as\n   * a dynamic access.\n   */\n  readonly fallbackRouteParams: FallbackRouteParams | null\n\n  readonly incrementalCache?: IncrementalCache\n  readonly cacheLifeProfiles?: { [profile: string]: CacheLife }\n\n  readonly isOnDemandRevalidate?: boolean\n  readonly isPrerendering?: boolean\n  readonly isRevalidate?: boolean\n\n  forceDynamic?: boolean\n  fetchCache?: AppSegmentConfig['fetchCache']\n\n  forceStatic?: boolean\n  dynamicShouldError?: boolean\n  pendingRevalidates?: Record<string, Promise<any>>\n  pendingRevalidateWrites?: Array<Promise<void>> // This is like pendingRevalidates but isn't used for deduping.\n  readonly afterContext: AfterContext | undefined\n\n  dynamicUsageDescription?: string\n  dynamicUsageStack?: string\n  dynamicUsageErr?: DynamicServerError\n\n  nextFetchId?: number\n  pathWasRevalidated?: boolean\n\n  revalidatedTags?: string[]\n  fetchMetrics?: FetchMetrics\n\n  isDraftMode?: boolean\n  isUnstableNoStore?: boolean\n  isPrefetchRequest?: boolean\n\n  requestEndedState?: { ended?: boolean }\n\n  buildId: string\n\n  readonly reactLoadableManifest?: DeepReadonly<\n    Record<string, { files: string[] }>\n  >\n  readonly assetPrefix?: string\n}\n\nexport type WorkAsyncStorage = AsyncLocalStorage<WorkStore>\n\nexport { workAsyncStorage }\n"], "names": ["workAsyncStorage"], "mappings": ";;;;+BA2ESA,oBAAAA;;;eAAAA,0BAAAA,gBAAgB;;;0CAhEQ", "ignoreList": [0]}}, {"offset": {"line": 1327, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1332, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/server/app-render/work-unit-async-storage-instance.ts"], "sourcesContent": ["import { createAsyncLocalStorage } from './async-local-storage'\nimport type { WorkUnitAsyncStorage } from './work-unit-async-storage.external'\n\nexport const workUnitAsyncStorage: WorkUnitAsyncStorage =\n  createAsyncLocalStorage()\n"], "names": ["workUnitAsyncStorage", "createAsyncLocalStorage"], "mappings": ";;;;+BAGaA,wBAAAA;;;eAAAA;;;mCAH2B;AAGjC,MAAMA,uBACXC,CAAAA,GAAAA,mBAAAA,uBAAuB", "ignoreList": [0]}}, {"offset": {"line": 1344, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1349, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/server/app-render/work-unit-async-storage.external.ts"], "sourcesContent": ["import type { AsyncLocalStorage } from 'async_hooks'\nimport type { DraftModeProvider } from '../async-storage/draft-mode-provider'\nimport type { ResponseCookies } from '../web/spec-extension/cookies'\nimport type { ReadonlyHeaders } from '../web/spec-extension/adapters/headers'\nimport type { ReadonlyRequestCookies } from '../web/spec-extension/adapters/request-cookies'\nimport type { CacheSignal } from './cache-signal'\nimport type { DynamicTrackingState } from './dynamic-rendering'\n\n// Share the instance module in the next-shared layer\nimport { workUnitAsyncStorage } from './work-unit-async-storage-instance' with { 'turbopack-transition': 'next-shared' }\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport type {\n  RenderResumeDataCache,\n  PrerenderResumeDataCache,\n} from '../resume-data-cache/resume-data-cache'\n\ntype WorkUnitPhase = 'action' | 'render' | 'after'\n\ntype PhasePartial = {\n  /** NOTE: Will be mutated as phases change */\n  phase: WorkUnitPhase\n}\n\nexport type RequestStore = {\n  type: 'request'\n\n  /**\n   * The URL of the request. This only specifies the pathname and the search\n   * part of the URL.\n   */\n  readonly url: {\n    /**\n     * The pathname of the requested URL.\n     */\n    readonly pathname: string\n\n    /**\n     * The search part of the requested URL. If the request did not provide a\n     * search part, this will be an empty string.\n     */\n    readonly search: string\n  }\n\n  readonly headers: ReadonlyHeaders\n  // This is mutable because we need to reassign it when transitioning from the action phase to the render phase.\n  // The cookie object itself is deliberately read only and thus can't be updated.\n  cookies: ReadonlyRequestCookies\n  readonly mutableCookies: ResponseCookies\n  readonly userspaceMutableCookies: ResponseCookies\n  readonly draftMode: DraftModeProvider\n  readonly isHmrRefresh?: boolean\n  readonly serverComponentsHmrCache?: ServerComponentsHmrCache\n\n  readonly implicitTags: string[]\n\n  /**\n   * The resume data cache for this request. This will be a immutable cache.\n   */\n  renderResumeDataCache: RenderResumeDataCache | null\n\n  // DEV-only\n  usedDynamic?: boolean\n  prerenderPhase?: boolean\n\n  /**\n   * The resume data cache for this request. This will be a mutable cache.\n   */\n  devWarmupPrerenderResumeDataCache: PrerenderResumeDataCache | null\n} & PhasePartial\n\n/**\n * The Prerender store is for tracking information related to prerenders.\n *\n * It can be used for both RSC and SSR prerendering and should be scoped as close\n * to the individual `renderTo...` API call as possible. To keep the type simple\n * we don't distinguish between RSC and SSR prerendering explicitly but instead\n * use conditional object properties to infer which mode we are in. For instance cache tracking\n * only needs to happen during the RSC prerender when we are prospectively prerendering\n * to fill all caches.\n */\nexport type PrerenderStoreModern = {\n  type: 'prerender'\n  readonly implicitTags: string[]\n\n  /**\n   * This signal is aborted when the React render is complete. (i.e. it is the same signal passed to react)\n   */\n  readonly renderSignal: AbortSignal\n  /**\n   * This is the AbortController which represents the boundary between Prerender and dynamic. In some renders it is\n   * the same as the controller for the renderSignal but in others it is a separate controller. It should be aborted\n   * whenever the we are no longer in the prerender phase of rendering. Typically this is after one task or when you call\n   * a sync API which requires the prerender to end immediately\n   */\n  readonly controller: AbortController\n\n  /**\n   * when not null this signal is used to track cache reads during prerendering and\n   * to await all cache reads completing before aborting the prerender.\n   */\n  readonly cacheSignal: null | CacheSignal\n\n  /**\n   * During some prerenders we want to track dynamic access.\n   */\n  readonly dynamicTracking: null | DynamicTrackingState\n\n  // Collected revalidate times and tags for this document during the prerender.\n  revalidate: number // in seconds. 0 means dynamic. INFINITE_CACHE and higher means never revalidate.\n  expire: number // server expiration time\n  stale: number // client expiration time\n  tags: null | string[]\n\n  /**\n   * The resume data cache for this prerender.\n   */\n  prerenderResumeDataCache: PrerenderResumeDataCache | null\n\n  // DEV ONLY\n  // When used this flag informs certain APIs to skip logging because we're\n  // not part of the primary render path and are just prerendering to produce\n  // validation results\n  validating?: boolean\n} & PhasePartial\n\nexport type PrerenderStorePPR = {\n  type: 'prerender-ppr'\n  readonly implicitTags: string[]\n  readonly dynamicTracking: null | DynamicTrackingState\n  // Collected revalidate times and tags for this document during the prerender.\n  revalidate: number // in seconds. 0 means dynamic. INFINITE_CACHE and higher means never revalidate.\n  expire: number // server expiration time\n  stale: number // client expiration time\n  tags: null | string[]\n\n  /**\n   * The resume data cache for this prerender.\n   */\n  prerenderResumeDataCache: PrerenderResumeDataCache\n} & PhasePartial\n\nexport type PrerenderStoreLegacy = {\n  type: 'prerender-legacy'\n  readonly implicitTags: string[]\n  // Collected revalidate times and tags for this document during the prerender.\n  revalidate: number // in seconds. 0 means dynamic. INFINITE_CACHE and higher means never revalidate.\n  expire: number // server expiration time\n  stale: number // client expiration time\n  tags: null | string[]\n} & PhasePartial\n\nexport type PrerenderStore =\n  | PrerenderStoreLegacy\n  | PrerenderStorePPR\n  | PrerenderStoreModern\n\nexport type UseCacheStore = {\n  type: 'cache'\n  readonly implicitTags: string[]\n  // Collected revalidate times and tags for this cache entry during the cache render.\n  revalidate: number // implicit revalidate time from inner caches / fetches\n  expire: number // server expiration time\n  stale: number // client expiration time\n  explicitRevalidate: undefined | number // explicit revalidate time from cacheLife() calls\n  explicitExpire: undefined | number // server expiration time\n  explicitStale: undefined | number // client expiration time\n  tags: null | string[]\n} & PhasePartial\n\nexport type UnstableCacheStore = {\n  type: 'unstable-cache'\n} & PhasePartial\n\n/**\n * The Cache store is for tracking information inside a \"use cache\" or unstable_cache context.\n * Inside this context we should never expose any request or page specific information.\n */\nexport type CacheStore = UseCacheStore | UnstableCacheStore\n\nexport type WorkUnitStore = RequestStore | CacheStore | PrerenderStore\n\nexport type WorkUnitAsyncStorage = AsyncLocalStorage<WorkUnitStore>\n\nexport { workUnitAsyncStorage }\n\nexport function getExpectedRequestStore(\n  callingExpression: string\n): RequestStore {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    if (workUnitStore.type === 'request') {\n      return workUnitStore\n    }\n    if (\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-ppr' ||\n      workUnitStore.type === 'prerender-legacy'\n    ) {\n      // This should not happen because we should have checked it already.\n      throw new Error(\n        `\\`${callingExpression}\\` cannot be called inside a prerender. This is a bug in Next.js.`\n      )\n    }\n    if (workUnitStore.type === 'cache') {\n      throw new Error(\n        `\\`${callingExpression}\\` cannot be called inside \"use cache\". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`\n      )\n    } else if (workUnitStore.type === 'unstable-cache') {\n      throw new Error(\n        `\\`${callingExpression}\\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n      )\n    }\n  }\n  throw new Error(\n    `\\`${callingExpression}\\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`\n  )\n}\n\nexport function getPrerenderResumeDataCache(\n  workUnitStore: WorkUnitStore\n): PrerenderResumeDataCache | null {\n  if (\n    workUnitStore.type !== 'prerender-legacy' &&\n    workUnitStore.type !== 'cache' &&\n    workUnitStore.type !== 'unstable-cache'\n  ) {\n    if (workUnitStore.type === 'request') {\n      return workUnitStore.devWarmupPrerenderResumeDataCache\n    }\n\n    return workUnitStore.prerenderResumeDataCache\n  }\n\n  return null\n}\n\nexport function getRenderResumeDataCache(\n  workUnitStore: WorkUnitStore\n): RenderResumeDataCache | null {\n  if (\n    workUnitStore.type !== 'prerender-legacy' &&\n    workUnitStore.type !== 'cache' &&\n    workUnitStore.type !== 'unstable-cache'\n  ) {\n    if (workUnitStore.type === 'request') {\n      return workUnitStore.renderResumeDataCache\n    }\n\n    // We return the mutable resume data cache here as an immutable version of\n    // the cache as it can also be used for reading.\n    return workUnitStore.prerenderResumeDataCache\n  }\n\n  return null\n}\n"], "names": ["getExpectedRequestStore", "getPrerenderResumeDataCache", "getRenderResumeDataCache", "workUnitAsyncStorage", "callingExpression", "workUnitStore", "getStore", "type", "Error", "devWarmupPrerenderResumeDataCache", "prerenderResumeDataCache", "renderResumeDataCache"], "mappings": ";;;;;;;;;;;;;;;;;IAyLgBA,uBAAuB,EAAA;eAAvBA;;IAiCAC,2BAA2B,EAAA;eAA3BA;;IAkBAC,wBAAwB,EAAA;eAAxBA;;IArDPC,oBAAoB,EAAA;eAApBA,8BAAAA,oBAAoB;;;8CA9KQ;AAgL9B,SAASH,wBACdI,iBAAyB;IAEzB,MAAMC,gBAAgBF,8BAAAA,oBAAoB,CAACG,QAAQ;IACnD,IAAID,eAAe;QACjB,IAAIA,cAAcE,IAAI,KAAK,WAAW;YACpC,OAAOF;QACT;QACA,IACEA,cAAcE,IAAI,KAAK,eACvBF,cAAcE,IAAI,KAAK,mBACvBF,cAAcE,IAAI,KAAK,oBACvB;YACA,oEAAoE;YACpE,MAAM,IAAIC,MACR,CAAC,EAAE,EAAEJ,kBAAkB,iEAAiE,CAAC;QAE7F;QACA,IAAIC,cAAcE,IAAI,KAAK,SAAS;YAClC,MAAM,IAAIC,MACR,CAAC,EAAE,EAAEJ,kBAAkB,2JAA2J,CAAC;QAEvL,OAAO,IAAIC,cAAcE,IAAI,KAAK,kBAAkB;YAClD,MAAM,IAAIC,MACR,CAAC,EAAE,EAAEJ,kBAAkB,sKAAsK,CAAC;QAElM;IACF;IACA,MAAM,IAAII,MACR,CAAC,EAAE,EAAEJ,kBAAkB,iHAAiH,CAAC;AAE7I;AAEO,SAASH,4BACdI,aAA4B;IAE5B,IACEA,cAAcE,IAAI,KAAK,sBACvBF,cAAcE,IAAI,KAAK,WACvBF,cAAcE,IAAI,KAAK,kBACvB;QACA,IAAIF,cAAcE,IAAI,KAAK,WAAW;YACpC,OAAOF,cAAcI,iCAAiC;QACxD;QAEA,OAAOJ,cAAcK,wBAAwB;IAC/C;IAEA,OAAO;AACT;AAEO,SAASR,yBACdG,aAA4B;IAE5B,IACEA,cAAcE,IAAI,KAAK,sBACvBF,cAAcE,IAAI,KAAK,WACvBF,cAAcE,IAAI,KAAK,kBACvB;QACA,IAAIF,cAAcE,IAAI,KAAK,WAAW;YACpC,OAAOF,cAAcM,qBAAqB;QAC5C;QAEA,0EAA0E;QAC1E,gDAAgD;QAChD,OAAON,cAAcK,wBAAwB;IAC/C;IAEA,OAAO;AACT", "ignoreList": [0]}}, {"offset": {"line": 1418, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1423, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/server/dynamic-rendering-utils.ts"], "sourcesContent": ["/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for dynamicIO where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  expression: string\n): Promise<T> {\n  const hangingPromise = new Promise<T>((_, reject) => {\n    signal.addEventListener(\n      'abort',\n      () => {\n        reject(\n          new Error(\n            `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`unstable_after\\`, or similar functions you may observe this error and you should handle it in that context.`\n          )\n        )\n      },\n      { once: true }\n    )\n  })\n  // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n  // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n  // your own promise out of it you'll need to ensure you handle the error when it rejects.\n  hangingPromise.catch(ignoreReject)\n  return hangingPromise\n}\n\nfunction ignoreReject() {}\n"], "names": ["makeHangingPromise", "signal", "expression", "hanging<PERSON>romise", "Promise", "_", "reject", "addEventListener", "Error", "once", "catch", "ignoreReject"], "mappings": "AAAA;;;;;;CAMC,GAAA;;;;+BACe<PERSON>,sBAAAA;;;eAAAA;;;AAAT,SAASA,mBACdC,MAAmB,EACnBC,UAAkB;IAElB,MAAMC,iBAAiB,IAAIC,QAAW,CAACC,GAAGC;QACxCL,OAAOM,gBAAgB,CACrB,SACA;YACED,OACE,IAAIE,MACF,CAAC,qBAAqB,EAAEN,WAAW,qGAAqG,EAAEA,WAAW,8JAA8J,CAAC;QAG1T,GACA;YAAEO,MAAM;QAAK;IAEjB;IACA,2GAA2G;IAC3G,6GAA6G;IAC7G,yFAAyF;IACzFN,eAAeO,KAAK,CAACC;IACrB,OAAOR;AACT;AAEA,SAASQ,gBAAgB", "ignoreList": [0]}}, {"offset": {"line": 1454, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1459, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/server/app-render/dynamic-rendering.ts"], "sourcesContent": ["/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../lib/metadata/metadata-constants'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicExpression: undefined | string\n  syncDynamicErrorWithStack: null | Error\n  // Dev only\n  syncDynamicLogged?: boolean\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspendedDynamic: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasSyncDynamicErrors: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicExpression: undefined,\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspendedDynamic: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasSyncDynamicErrors: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender-ppr') {\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      workUnitStore.revalidate = 0\n\n      // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n/**\n * This function communicates that some dynamic path parameter was read. This\n * differs from the more general `trackDynamicDataAccessed` in that it is will\n * not error when `dynamic = \"error\"` is set.\n *\n * @param store The static generation store\n * @param expression The expression that was accessed dynamically\n */\nexport function trackFallbackParamAccessed(\n  store: WorkStore,\n  expression: string\n): void {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return\n\n  postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking)\n}\n\n/**\n * This function is meant to be used when prerendering without dynamicIO or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(\n  _store: WorkStore,\n  workUnitStore: void | WorkUnitStore\n) {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n    if (\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-legacy'\n    ) {\n      workUnitStore.revalidate = 0\n    }\n    if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n// Despite it's name we don't actually abort unless we have a controller to call abort on\n// There are times when we let a prerender run long to discover caches where we want the semantics\n// of tracking dynamic access without terminating the prerender early\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicExpression = expression\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n  return abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with dynamicIO. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in dynamicIO mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicExpression = expression\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n      if (prerenderStore.validating === true) {\n        // We always log Request Access in dev at the point of calling the function\n        // So we mark the dynamic validation as not requiring it to be printed\n        dynamicTracking.syncDynamicLogged = true\n      }\n    }\n  }\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createPostponedAbortSignal(reason: string): AbortSignal {\n  assertPostpone()\n  const controller = new AbortController()\n  // We get our hands on a postpone instance by calling postpone and catching the throw\n  try {\n    React.unstable_postpone(reason)\n  } catch (x: unknown) {\n    controller.abort(x)\n  }\n  return controller.signal\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  if (typeof window === 'undefined') {\n    const workStore = workAsyncStorage.getStore()\n\n    if (\n      workStore &&\n      workStore.isStaticGeneration &&\n      workStore.fallbackRouteParams &&\n      workStore.fallbackRouteParams.size > 0\n    ) {\n      // There are fallback route params, we should track these as dynamic\n      // accesses.\n      const workUnitStore = workUnitAsyncStorage.getStore()\n      if (workUnitStore) {\n        // We're prerendering with dynamicIO or PPR or both\n        if (workUnitStore.type === 'prerender') {\n          // We are in a prerender with dynamicIO semantics\n          // We are going to hang here and never resolve. This will cause the currently\n          // rendering component to effectively be a dynamic hole\n          React.use(makeHangingPromise(workUnitStore.renderSignal, expression))\n        } else if (workUnitStore.type === 'prerender-ppr') {\n          // We're prerendering with PPR\n          postponeWithTracking(\n            workStore.route,\n            expression,\n            workUnitStore.dynamicTracking\n          )\n        } else if (workUnitStore.type === 'prerender-legacy') {\n          throwToInterruptStaticGeneration(expression, workStore, workUnitStore)\n        }\n      }\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  route: string,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    dynamicValidation.hasSuspendedDynamic = true\n    return\n  } else if (\n    serverDynamic.syncDynamicErrorWithStack ||\n    clientDynamic.syncDynamicErrorWithStack\n  ) {\n    dynamicValidation.hasSyncDynamicErrors = true\n    return\n  } else {\n    const message = `Route \"${route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\nfunction createErrorWithComponentStack(\n  message: string,\n  componentStack: string\n) {\n  const error = new Error(message)\n  error.stack = 'Error: ' + message + componentStack\n  return error\n}\n\nexport function throwIfDisallowedDynamic(\n  route: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): void {\n  let syncError: null | Error\n  let syncExpression: undefined | string\n  let syncLogged: boolean\n  if (serverDynamic.syncDynamicErrorWithStack) {\n    syncError = serverDynamic.syncDynamicErrorWithStack\n    syncExpression = serverDynamic.syncDynamicExpression!\n    syncLogged = serverDynamic.syncDynamicLogged === true\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    syncError = clientDynamic.syncDynamicErrorWithStack\n    syncExpression = clientDynamic.syncDynamicExpression!\n    syncLogged = clientDynamic.syncDynamicLogged === true\n  } else {\n    syncError = null\n    syncExpression = undefined\n    syncLogged = false\n  }\n\n  if (dynamicValidation.hasSyncDynamicErrors && syncError) {\n    if (!syncLogged) {\n      // In dev we already log errors about sync dynamic access. But during builds we need to ensure\n      // the offending sync error is logged before we exit the build\n      console.error(syncError)\n    }\n    // The actual error should have been logged when the sync access ocurred\n    throw new StaticGenBailoutError()\n  }\n\n  const dynamicErrors = dynamicValidation.dynamicErrors\n  if (dynamicErrors.length) {\n    for (let i = 0; i < dynamicErrors.length; i++) {\n      console.error(dynamicErrors[i])\n    }\n\n    throw new StaticGenBailoutError()\n  }\n\n  if (!dynamicValidation.hasSuspendedDynamic) {\n    if (dynamicValidation.hasDynamicMetadata) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateMetadata\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateMetadata\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    } else if (dynamicValidation.hasDynamicViewport) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateViewport\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateViewport\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    }\n  }\n}\n"], "names": ["Postpone", "abortAndThrowOnSynchronousRequestDataAccess", "abortOnSynchronousPlatformIOAccess", "accessedDynamicData", "annotateDynamicAccess", "consumeDynamicAccess", "createDynamicTrackingState", "createDynamicValidationState", "createPostponedAbortSignal", "formatDynamicAPIAccesses", "getFirstDynamicReason", "isDynamicPostpone", "isPrerenderInterruptedError", "markCurrentScopeAsDynamic", "postponeWithTracking", "throwIfDisallowedDynamic", "throwToInterruptStaticGeneration", "trackAllowedDynamicAccess", "trackDynamicDataInDynamicRender", "trackFallbackParamAccessed", "trackSynchronousPlatformIOAccessInDev", "trackSynchronousRequestDataAccessInDev", "useDynamicRouteParams", "hasPostpone", "React", "unstable_postpone", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicExpression", "undefined", "syncDynamicErrorWithStack", "hasSuspendedDynamic", "hasDynamicMetadata", "hasDynamicViewport", "hasSyncDynamicErrors", "dynamicErrors", "trackingState", "expression", "store", "workUnitStore", "type", "forceDynamic", "forceStatic", "dynamicShouldError", "StaticGenBailoutError", "route", "dynamicTracking", "revalidate", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "process", "env", "NODE_ENV", "usedDynamic", "prerenderStore", "workUnitAsyncStorage", "getStore", "_store", "abortOnSynchronousDynamicDataAccess", "reason", "error", "createPrerenderInterruptedError", "controller", "abort", "push", "Error", "errorWithStack", "requestStore", "prerenderPhase", "validating", "syncDynamicLogged", "assertPostpone", "createPostponeReason", "message", "isDynamicPostponeReason", "includes", "NEXT_PRERENDER_INTERRUPTED", "digest", "length", "serverDynamic", "clientDynamic", "filter", "access", "map", "split", "slice", "line", "join", "AbortController", "x", "signal", "window", "workStore", "workAsyncStorage", "isStaticGeneration", "fallbackRouteParams", "size", "use", "makeHangingPromise", "renderSignal", "hasSuspenseRegex", "hasMetadataRegex", "RegExp", "METADATA_BOUNDARY_NAME", "hasViewportRegex", "VIEWPORT_BOUNDARY_NAME", "hasOutletRegex", "OUTLET_BOUNDARY_NAME", "componentStack", "dynamicValidation", "test", "createErrorWithComponentStack", "syncError", "syncExpression", "syncLogged", "console", "i"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC,GAoJKqD,QAAQC,GAAG,CAACC,QAAQ;AApJzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2UevD,QAAQ,EAAA;eAARA;;IAnCAC,2CAA2C,EAAA;eAA3CA;;IAlCAC,kCAAkC,EAAA;eAAlCA;;IA+JAC,mBAAmB,EAAA;eAAnBA;;IA+EAC,qBAAqB,EAAA;eAArBA;;IAzEAC,oBAAoB,EAAA;eAApBA;;IAxWAC,0BAA0B,EAAA;eAA1BA;;IAWAC,4BAA4B,EAAA;eAA5BA;;IA0ZAC,0BAA0B,EAAA;eAA1BA;;IAlDAC,wBAAwB,EAAA;eAAxBA;;IA9VAC,qBAAqB,EAAA;eAArBA;;IAwRAC,iBAAiB,EAAA;eAAjBA;;IAwCAC,2BAA2B,EAAA;eAA3BA;;IAnTAC,yBAAyB,EAAA;eAAzBA;;IA+OAC,oBAAoB,EAAA;eAApBA;;IAqQAC,wBAAwB,EAAA;eAAxBA;;IApaAC,gCAAgC,EAAA;eAAhCA;;IA0XAC,yBAAyB,EAAA;eAAzBA;;IAjWAC,+BAA+B,EAAA;eAA/BA;;IAzCAC,0BAA0B,EAAA;eAA1BA;;IAiHAC,qCAAqC,EAAA;eAArCA;;IA2CHC,sCAAsC,EAAA;eAAtCA;;IAkMGC,qBAAqB,EAAA;eAArBA;;;8DAxfE;oCAEiB;yCACG;8CACD;0CACJ;uCACE;mCAK5B;;;;;;AAEP,MAAMC,cAAc,OAAOC,OAAAA,OAAK,CAACC,iBAAiB,KAAK;AA2ChD,SAASnB,2BACdoB,sBAA2C;IAE3C,OAAO;QACLA;QACAC,iBAAiB,EAAE;QACnBC,uBAAuBC;QACvBC,2BAA2B;IAC7B;AACF;AAEO,SAASvB;IACd,OAAO;QACLwB,qBAAqB;QACrBC,oBAAoB;QACpBC,oBAAoB;QACpBC,sBAAsB;QACtBC,eAAe,EAAE;IACnB;AACF;AAEO,SAASzB,sBACd0B,aAAmC;QAE5BA;IAAP,OAAA,CAAOA,kCAAAA,cAAcT,eAAe,CAAC,EAAE,KAAA,OAAA,KAAA,IAAhCS,gCAAkCC,UAAU;AACrD;AASO,SAASxB,0BACdyB,KAAgB,EAChBC,aAAuE,EACvEF,UAAkB;IAElB,IAAIE,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;IACF;IAEA,2EAA2E;IAC3E,4EAA4E;IAC5E,2DAA2D;IAC3D,IAAIF,MAAMG,YAAY,IAAIH,MAAMI,WAAW,EAAE;IAE7C,IAAIJ,MAAMK,kBAAkB,EAAE;QAC5B,MAAM,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEN,MAAMO,KAAK,CAAC,8EAA8E,EAAER,WAAW,4HAA4H,CAAC;IAEjP;IAEA,IAAIE,eAAe;QACjB,IAAIA,cAAcC,IAAI,KAAK,iBAAiB;YAC1C1B,qBACEwB,MAAMO,KAAK,EACXR,YACAE,cAAcO,eAAe;QAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;YACpDD,cAAcQ,UAAU,GAAG;YAE3B,uGAAuG;YACvG,MAAMC,MAAM,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,iDAAiD,EAAER,WAAW,2EAA2E,CAAC;YAEjKC,MAAMY,uBAAuB,GAAGb;YAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;YAEnC,MAAMJ;QACR,OAAO,wDACoB,iBACzBT,iBACAA,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAUO,SAASrC,2BACdmB,KAAgB,EAChBD,UAAkB;IAElB,MAAMoB,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,IAAI,CAACF,kBAAkBA,eAAejB,IAAI,KAAK,iBAAiB;IAEhE1B,qBAAqBwB,MAAMO,KAAK,EAAER,YAAYoB,eAAeX,eAAe;AAC9E;AAQO,SAAS9B,iCACdqB,UAAkB,EAClBC,KAAgB,EAChBmB,cAAoC;IAEpC,uGAAuG;IACvG,MAAMT,MAAM,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,mDAAmD,EAAER,WAAW,6EAA6E,CAAC;IAGrKoB,eAAeV,UAAU,GAAG;IAE5BT,MAAMY,uBAAuB,GAAGb;IAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;IAEnC,MAAMJ;AACR;AASO,SAAS9B,gCACd0C,MAAiB,EACjBrB,aAAmC;IAEnC,IAAIA,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;QACA,IACED,cAAcC,IAAI,KAAK,eACvBD,cAAcC,IAAI,KAAK,oBACvB;YACAD,cAAcQ,UAAU,GAAG;QAC7B;QACA,IACEM,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBhB,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAEA,yFAAyF;AACzF,kGAAkG;AAClG,qEAAqE;AACrE,SAASK,oCACPhB,KAAa,EACbR,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMK,SAAS,CAAC,MAAM,EAAEjB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;IAE9G,MAAM0B,QAAQC,gCAAgCF;IAE9CL,eAAeQ,UAAU,CAACC,KAAK,CAACH;IAEhC,MAAMjB,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;AACF;AAEO,SAASnC,mCACd2C,KAAa,EACbR,UAAkB,EAClBgC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;YACtDgB,gBAAgBlB,qBAAqB,GAAGS;YACxCS,gBAAgBhB,yBAAyB,GAAGuC;QAC9C;IACF;IACA,OAAOR,oCAAoChB,OAAOR,YAAYoB;AAChE;AAEO,SAASrC,sCACdkD,YAA0B;IAE1B,oFAAoF;IACpF,oDAAoD;IACpDA,aAAaC,cAAc,GAAG;AAChC;AAYO,SAAStE,4CACd4C,KAAa,EACbR,UAAkB,EAClBgC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;YACtDgB,gBAAgBlB,qBAAqB,GAAGS;YACxCS,gBAAgBhB,yBAAyB,GAAGuC;YAC5C,IAAIZ,eAAee,UAAU,KAAK,MAAM;gBACtC,2EAA2E;gBAC3E,sEAAsE;gBACtE1B,gBAAgB2B,iBAAiB,GAAG;YACtC;QACF;IACF;IACAZ,oCAAoChB,OAAOR,YAAYoB;IACvD,MAAMO,gCACJ,CAAC,MAAM,EAAEnB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;AAEnG;AAGO,MAAMhB,yCACXD;AASK,SAASpB,SAAS,EAAE8D,MAAM,EAAEjB,KAAK,EAAiB;IACvD,MAAMY,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,MAAMb,kBACJW,kBAAkBA,eAAejB,IAAI,KAAK,kBACtCiB,eAAeX,eAAe,GAC9B;IACNhC,qBAAqB+B,OAAOiB,QAAQhB;AACtC;AAEO,SAAShC,qBACd+B,KAAa,EACbR,UAAkB,EAClBS,eAA4C;IAE5C4B;IACA,IAAI5B,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;IAEAb,OAAAA,OAAK,CAACC,iBAAiB,CAACkD,qBAAqB9B,OAAOR;AACtD;AAEA,SAASsC,qBAAqB9B,KAAa,EAAER,UAAkB;IAC7D,OACE,CAAC,MAAM,EAAEQ,MAAM,iEAAiE,EAAER,WAAW,EAAE,CAAC,GAChG,CAAC,+EAA+E,CAAC,GACjF,CAAC,iFAAiF,CAAC;AAEvF;AAEO,SAAS1B,kBAAkBqC,GAAY;IAC5C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,OAAQA,IAAY4B,OAAO,KAAK,UAChC;QACA,OAAOC,wBAAyB7B,IAAY4B,OAAO;IACrD;IACA,OAAO;AACT;AAEA,SAASC,wBAAwBf,MAAc;IAC7C,OACEA,OAAOgB,QAAQ,CACb,sEAEFhB,OAAOgB,QAAQ,CACb;AAGN;AAEA,IAAID,wBAAwBF,qBAAqB,OAAO,YAAY,OAAO;IACzE,MAAM,IAAIP,MACR;AAEJ;AAEA,MAAMW,6BAA6B;AAEnC,SAASf,gCAAgCY,OAAe;IACtD,MAAMb,QAAQ,IAAIK,MAAMQ;IACtBb,MAAciB,MAAM,GAAGD;IACzB,OAAOhB;AACT;AAMO,SAASnD,4BACdmD,KAAc;IAEd,OACE,OAAOA,UAAU,YACjBA,UAAU,QACTA,MAAciB,MAAM,KAAKD,8BAC1B,UAAUhB,SACV,aAAaA,SACbA,iBAAiBK;AAErB;AAEO,SAASjE,oBACdwB,eAAqC;IAErC,OAAOA,gBAAgBsD,MAAM,GAAG;AAClC;AAEO,SAAS5E,qBACd6E,aAAmC,EACnCC,aAAmC;IAEnC,oEAAoE;IACpE,0EAA0E;IAC1E,SAAS;IACTD,cAAcvD,eAAe,CAACwC,IAAI,IAAIgB,cAAcxD,eAAe;IACnE,OAAOuD,cAAcvD,eAAe;AACtC;AAEO,SAASlB,yBACdkB,eAAqC;IAErC,OAAOA,gBACJyD,MAAM,CACL,CAACC,SACC,OAAOA,OAAOjC,KAAK,KAAK,YAAYiC,OAAOjC,KAAK,CAAC6B,MAAM,GAAG,GAE7DK,GAAG,CAAC,CAAC,EAAEjD,UAAU,EAAEe,KAAK,EAAE;QACzBA,QAAQA,MACLmC,KAAK,CAAC,MACP,wEAAwE;QACxE,qEAAqE;QACrE,uDAAuD;SACtDC,KAAK,CAAC,GACNJ,MAAM,CAAC,CAACK;YACP,kDAAkD;YAClD,IAAIA,KAAKX,QAAQ,CAAC,uBAAuB;gBACvC,OAAO;YACT;YAEA,oDAAoD;YACpD,IAAIW,KAAKX,QAAQ,CAAC,mBAAmB;gBACnC,OAAO;YACT;YAEA,kDAAkD;YAClD,IAAIW,KAAKX,QAAQ,CAAC,YAAY;gBAC5B,OAAO;YACT;YAEA,OAAO;QACT,GACCY,IAAI,CAAC;QACR,OAAO,CAAC,0BAA0B,EAAErD,WAAW,GAAG,EAAEe,MAAM,CAAC;IAC7D;AACJ;AAEA,SAASsB;IACP,IAAI,CAACnD,aAAa;QAChB,MAAM,IAAI6C,MACR,CAAC,gIAAgI,CAAC;IAEtI;AACF;AAMO,SAAS5D,2BAA2BsD,MAAc;IACvDY;IACA,MAAMT,aAAa,IAAI0B;IACvB,qFAAqF;IACrF,IAAI;QACFnE,OAAAA,OAAK,CAACC,iBAAiB,CAACqC;IAC1B,EAAE,OAAO8B,GAAY;QACnB3B,WAAWC,KAAK,CAAC0B;IACnB;IACA,OAAO3B,WAAW4B,MAAM;AAC1B;AAEO,SAASzF,sBACdiC,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnCf,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;AACF;AAEO,SAASf,sBAAsBe,UAAkB;IACtD,IAAI,OAAOyD,WAAW,aAAa;QACjC,MAAMC,YAAYC,0BAAAA,gBAAgB,CAACrC,QAAQ;QAE3C,IACEoC,aACAA,UAAUE,kBAAkB,IAC5BF,UAAUG,mBAAmB,IAC7BH,UAAUG,mBAAmB,CAACC,IAAI,GAAG,GACrC;YACA,oEAAoE;YACpE,YAAY;YACZ,MAAM5D,gBAAgBmB,8BAAAA,oBAAoB,CAACC,QAAQ;YACnD,IAAIpB,eAAe;gBACjB,mDAAmD;gBACnD,IAAIA,cAAcC,IAAI,KAAK,aAAa;oBACtC,iDAAiD;oBACjD,6EAA6E;oBAC7E,uDAAuD;oBACvDhB,OAAAA,OAAK,CAAC4E,GAAG,CAACC,CAAAA,GAAAA,uBAAAA,kBAAkB,EAAC9D,cAAc+D,YAAY,EAAEjE;gBAC3D,OAAO,IAAIE,cAAcC,IAAI,KAAK,iBAAiB;oBACjD,8BAA8B;oBAC9B1B,qBACEiF,UAAUlD,KAAK,EACfR,YACAE,cAAcO,eAAe;gBAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;oBACpDxB,iCAAiCqB,YAAY0D,WAAWxD;gBAC1D;YACF;QACF;IACF;AACF;AAEA,MAAMgE,mBAAmB;AACzB,MAAMC,mBAAmB,IAAIC,OAC3B,CAAC,UAAU,EAAEC,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,mBAAmB,IAAIF,OAC3B,CAAC,UAAU,EAAEG,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,iBAAiB,IAAIJ,OAAO,CAAC,UAAU,EAAEK,mBAAAA,oBAAoB,CAAC,QAAQ,CAAC;AAEtE,SAAS7F,0BACd4B,KAAa,EACbkE,cAAsB,EACtBC,iBAAyC,EACzC9B,aAAmC,EACnCC,aAAmC;IAEnC,IAAI0B,eAAeI,IAAI,CAACF,iBAAiB;QACvC,kGAAkG;QAClG;IACF,OAAO,IAAIP,iBAAiBS,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBhF,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAI2E,iBAAiBM,IAAI,CAACF,iBAAiB;QAChDC,kBAAkB/E,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAIsE,iBAAiBU,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBjF,mBAAmB,GAAG;QACxC;IACF,OAAO,IACLmD,cAAcpD,yBAAyB,IACvCqD,cAAcrD,yBAAyB,EACvC;QACAkF,kBAAkB9E,oBAAoB,GAAG;QACzC;IACF,OAAO;QACL,MAAM0C,UAAU,CAAC,OAAO,EAAE/B,MAAM,+UAA+U,CAAC;QAChX,MAAMkB,QAAQmD,8BAA8BtC,SAASmC;QACrDC,kBAAkB7E,aAAa,CAACgC,IAAI,CAACJ;QACrC;IACF;AACF;AAEA,SAASmD,8BACPtC,OAAe,EACfmC,cAAsB;IAEtB,MAAMhD,QAAQ,IAAIK,MAAMQ;IACxBb,MAAMX,KAAK,GAAG,YAAYwB,UAAUmC;IACpC,OAAOhD;AACT;AAEO,SAAShD,yBACd8B,KAAa,EACbmE,iBAAyC,EACzC9B,aAAmC,EACnCC,aAAmC;IAEnC,IAAIgC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAInC,cAAcpD,yBAAyB,EAAE;QAC3CqF,YAAYjC,cAAcpD,yBAAyB;QACnDsF,iBAAiBlC,cAActD,qBAAqB;QACpDyF,aAAanC,cAAcT,iBAAiB,KAAK;IACnD,OAAO,IAAIU,cAAcrD,yBAAyB,EAAE;QAClDqF,YAAYhC,cAAcrD,yBAAyB;QACnDsF,iBAAiBjC,cAAcvD,qBAAqB;QACpDyF,aAAalC,cAAcV,iBAAiB,KAAK;IACnD,OAAO;QACL0C,YAAY;QACZC,iBAAiBvF;QACjBwF,aAAa;IACf;IAEA,IAAIL,kBAAkB9E,oBAAoB,IAAIiF,WAAW;QACvD,IAAI,CAACE,YAAY;YACf,8FAA8F;YAC9F,8DAA8D;YAC9DC,QAAQvD,KAAK,CAACoD;QAChB;QACA,wEAAwE;QACxE,MAAM,IAAIvE,yBAAAA,qBAAqB;IACjC;IAEA,MAAMT,gBAAgB6E,kBAAkB7E,aAAa;IACrD,IAAIA,cAAc8C,MAAM,EAAE;QACxB,IAAK,IAAIsC,IAAI,GAAGA,IAAIpF,cAAc8C,MAAM,EAAEsC,IAAK;YAC7CD,QAAQvD,KAAK,CAAC5B,aAAa,CAACoF,EAAE;QAChC;QAEA,MAAM,IAAI3E,yBAAAA,qBAAqB;IACjC;IAEA,IAAI,CAACoE,kBAAkBjF,mBAAmB,EAAE;QAC1C,IAAIiF,kBAAkBhF,kBAAkB,EAAE;YACxC,IAAImF,WAAW;gBACbG,QAAQvD,KAAK,CAACoD;gBACd,MAAM,IAAIvE,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,oEAAoE,EAAEuE,eAAe,+EAA+E,CAAC;YAEzL;YACA,MAAM,IAAIxE,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,8cAA8c,CAAC;QAEne,OAAO,IAAImE,kBAAkB/E,kBAAkB,EAAE;YAC/C,IAAIkF,WAAW;gBACbG,QAAQvD,KAAK,CAACoD;gBACd,MAAM,IAAIvE,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,oEAAoE,EAAEuE,eAAe,+EAA+E,CAAC;YAEzL;YACA,MAAM,IAAIxE,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,8cAA8c,CAAC;QAEne;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 1933, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1938, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/server/lib/router-utils/is-postpone.ts"], "sourcesContent": ["const REACT_POSTPONE_TYPE: symbol = Symbol.for('react.postpone')\n\nexport function isPostpone(error: any): boolean {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    error.$$typeof === REACT_POSTPONE_TYPE\n  )\n}\n"], "names": ["isPostpone", "REACT_POSTPONE_TYPE", "Symbol", "for", "error", "$$typeof"], "mappings": ";;;;+BAEgBA,cAAAA;;;eAAAA;;;AAFhB,MAAMC,sBAA8BC,OAAOC,GAAG,CAAC;AAExC,SAASH,WAAWI,KAAU;IACnC,OACE,OAAOA,UAAU,YACjBA,UAAU,QACVA,MAAMC,QAAQ,KAAKJ;AAEvB", "ignoreList": [0]}}, {"offset": {"line": 1952, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1957, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/server/dev/hot-reloader-types.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type { UrlObject } from 'url'\nimport type { Duplex } from 'stream'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type getBaseWebpackConfig from '../../build/webpack-config'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\nimport type { Project, Update as TurbopackUpdate } from '../../build/swc/types'\nimport type { VersionInfo } from './parse-version-info'\nimport type { DebugInfo } from '../../client/components/react-dev-overlay/types'\n\nexport const enum HMR_ACTIONS_SENT_TO_BROWSER {\n  ADDED_PAGE = 'addedPage',\n  REMOVED_PAGE = 'removedPage',\n  RELOAD_PAGE = 'reloadPage',\n  SERVER_COMPONENT_CHANGES = 'serverComponentChanges',\n  MIDDLEWARE_CHANGES = 'middlewareChanges',\n  CLIENT_CHANGES = 'clientChanges',\n  SERVER_ONLY_CHANGES = 'serverOnlyChanges',\n  SYNC = 'sync',\n  BUILT = 'built',\n  BUILDING = 'building',\n  DEV_PAGES_MANIFEST_UPDATE = 'devPagesManifestUpdate',\n  TURBOPACK_MESSAGE = 'turbopack-message',\n  SERVER_ERROR = 'serverError',\n  TURBOPACK_CONNECTED = 'turbopack-connected',\n  APP_ISR_MANIFEST = 'appIsrManifest',\n}\n\ninterface ServerErrorAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR\n  errorJSON: string\n}\n\nexport interface TurbopackMessageAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE\n  data: TurbopackUpdate | TurbopackUpdate[]\n}\n\ninterface BuildingAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.BUILDING\n}\n\nexport interface CompilationError {\n  moduleName?: string\n  message: string\n  details?: string\n  moduleTrace?: Array<{ moduleName?: string }>\n  stack?: string\n}\nexport interface SyncAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.SYNC\n  hash: string\n  errors: ReadonlyArray<CompilationError>\n  warnings: ReadonlyArray<CompilationError>\n  versionInfo: VersionInfo\n  updatedModules?: ReadonlyArray<string>\n  debug?: DebugInfo\n}\ninterface BuiltAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.BUILT\n  hash: string\n  errors: ReadonlyArray<CompilationError>\n  warnings: ReadonlyArray<CompilationError>\n  updatedModules?: ReadonlyArray<string>\n}\n\ninterface AddedPageAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE\n  data: [page: string | null]\n}\n\ninterface RemovedPageAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE\n  data: [page: string | null]\n}\n\nexport interface ReloadPageAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE\n  data: string\n}\n\ninterface ServerComponentChangesAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES\n}\n\ninterface MiddlewareChangesAction {\n  event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES\n}\n\ninterface ClientChangesAction {\n  event: HMR_ACTIONS_SENT_TO_BROWSER.CLIENT_CHANGES\n}\n\ninterface ServerOnlyChangesAction {\n  event: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ONLY_CHANGES\n  pages: ReadonlyArray<string>\n}\n\ninterface DevPagesManifestUpdateAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE\n  data: [\n    {\n      devPagesManifest: true\n    },\n  ]\n}\n\nexport interface TurbopackConnectedAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED\n  data: { sessionId: number }\n}\n\nexport interface AppIsrManifestAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.APP_ISR_MANIFEST\n  data: Record<string, boolean>\n}\n\nexport type HMR_ACTION_TYPES =\n  | TurbopackMessageAction\n  | TurbopackConnectedAction\n  | BuildingAction\n  | SyncAction\n  | BuiltAction\n  | AddedPageAction\n  | RemovedPageAction\n  | ReloadPageAction\n  | ServerComponentChangesAction\n  | ClientChangesAction\n  | MiddlewareChangesAction\n  | ServerOnlyChangesAction\n  | DevPagesManifestUpdateAction\n  | ServerErrorAction\n  | AppIsrManifestAction\n\nexport type TurbopackMsgToBrowser =\n  | { type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE; data: any }\n  | {\n      type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED\n      data: { sessionId: number }\n    }\n\nexport interface NextJsHotReloaderInterface {\n  turbopackProject?: Project\n  activeWebpackConfigs?: Array<Awaited<ReturnType<typeof getBaseWebpackConfig>>>\n  serverStats: webpack.Stats | null\n  edgeServerStats: webpack.Stats | null\n  run(\n    req: IncomingMessage,\n    res: ServerResponse,\n    parsedUrl: UrlObject\n  ): Promise<{ finished?: true }>\n\n  setHmrServerError(error: Error | null): void\n  clearHmrServerError(): void\n  start(): Promise<void>\n  stop(): Promise<void>\n  send(action: HMR_ACTION_TYPES): void\n  getCompilationErrors(page: string): Promise<any[]>\n  onHMR(\n    req: IncomingMessage,\n    _socket: Duplex,\n    head: Buffer,\n    onUpgrade: (client: { send(data: string): void }) => void\n  ): void\n  invalidate({\n    reloadAfterInvalidation,\n  }: {\n    reloadAfterInvalidation: boolean\n  }): Promise<void> | void\n  buildFallbackError(): Promise<void>\n  ensurePage({\n    page,\n    clientOnly,\n    appPaths,\n    definition,\n    isApp,\n    url,\n  }: {\n    page: string\n    clientOnly: boolean\n    appPaths?: ReadonlyArray<string> | null\n    isApp?: boolean\n    definition: RouteDefinition | undefined\n    url?: string\n  }): Promise<void>\n}\n"], "names": ["HMR_ACTIONS_SENT_TO_BROWSER"], "mappings": ";;;;;;;;;;;UAUkBA,2BAAAA;;;;;;;;;;;;;;;;GAAAA,+BAAAA,CAAAA,8BAAAA,CAAAA,CAAAA", "ignoreList": [0]}}, {"offset": {"line": 1985, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1990, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/server/dev/extract-modules-from-turbopack-message.ts"], "sourcesContent": ["import type { Update as TurbopackUpdate } from '../../build/swc/types'\n\nexport function extractModulesFromTurbopackMessage(\n  data: TurbopackUpdate | TurbopackUpdate[]\n) {\n  const updatedModules: Set<string> = new Set()\n\n  const updates = Array.isArray(data) ? data : [data]\n  for (const update of updates) {\n    // TODO this won't capture changes to CSS since they don't result in a \"merged\" update\n    if (\n      update.type !== 'partial' ||\n      update.instruction.type !== 'ChunkListUpdate' ||\n      update.instruction.merged === undefined\n    ) {\n      continue\n    }\n\n    for (const mergedUpdate of update.instruction.merged) {\n      for (const name of Object.keys(mergedUpdate.entries)) {\n        const res = /(.*)\\s+\\[.*/.exec(name)\n        if (res === null) {\n          console.error(\n            '[Turbopack HMR] Expected module to match pattern: ' + name\n          )\n          continue\n        }\n\n        updatedModules.add(res[1])\n      }\n    }\n  }\n\n  return [...updatedModules]\n}\n"], "names": ["extractModulesFromTurbopackMessage", "data", "updatedModules", "Set", "updates", "Array", "isArray", "update", "type", "instruction", "merged", "undefined", "mergedUpdate", "name", "Object", "keys", "entries", "res", "exec", "console", "error", "add"], "mappings": ";;;;+BAEg<PERSON>,sCAAAA;;;eAAAA;;;AAAT,SAASA,mCACdC,IAAyC;IAEzC,MAAMC,iBAA8B,IAAIC;IAExC,MAAMC,UAAUC,MAAMC,OAAO,CAACL,QAAQA,OAAO;QAACA;KAAK;IACnD,KAAK,MAAMM,UAAUH,QAAS;QAC5B,sFAAsF;QACtF,IACEG,OAAOC,IAAI,KAAK,aAChBD,OAAOE,WAAW,CAACD,IAAI,KAAK,qBAC5BD,OAAOE,WAAW,CAACC,MAAM,KAAKC,WAC9B;YACA;QACF;QAEA,KAAK,MAAMC,gBAAgBL,OAAOE,WAAW,CAACC,MAAM,CAAE;YACpD,KAAK,MAAMG,QAAQC,OAAOC,IAAI,CAACH,aAAaI,OAAO,EAAG;gBACpD,MAAMC,MAAM,cAAcC,IAAI,CAACL;gBAC/B,IAAII,QAAQ,MAAM;oBAChBE,QAAQC,KAAK,CACX,uDAAuDP;oBAEzD;gBACF;gBAEAX,eAAemB,GAAG,CAACJ,GAAG,CAAC,EAAE;YAC3B;QACF;IACF;IAEA,OAAO;WAAIf;KAAe;AAC5B", "ignoreList": [0]}}, {"offset": {"line": 2025, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2030, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/export/helpers/is-dynamic-usage-error.ts"], "sourcesContent": ["import { isDynamicServerError } from '../../client/components/hooks-server-context'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from '../../client/components/is-next-router-error'\nimport { isDynamicPostpone } from '../../server/app-render/dynamic-rendering'\n\nexport const isDynamicUsageError = (err: unknown) =>\n  isDynamicServerError(err) ||\n  isBailoutToCSRError(err) ||\n  isNextRouterError(err) ||\n  isDynamicPostpone(err)\n"], "names": ["isDynamicUsageError", "err", "isDynamicServerError", "isBailoutToCSRError", "isNextRouterError", "isDynamicPostpone"], "mappings": ";;;;+BAKaA,uBAAAA;;;eAAAA;;;oCALwB;8BACD;mCACF;kCACA;AAE3B,MAAMA,sBAAsB,CAACC,MAClCC,CAAAA,GAAAA,oBAAAA,oBAAoB,EAACD,QACrBE,CAAAA,GAAAA,cAAAA,mBAAmB,EAACF,QACpBG,CAAAA,GAAAA,mBAAAA,iBAAiB,EAACH,QAClBI,CAAAA,GAAAA,kBAAAA,iBAAiB,EAACJ", "ignoreList": [0]}}, {"offset": {"line": 2045, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}