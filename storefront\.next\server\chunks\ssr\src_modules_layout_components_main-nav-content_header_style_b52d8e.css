/* [project]/src/modules/layout/components/main-nav-content/header/style.css [app-client] (css) */
.react-datepicker__input-container {
  border-radius: 8px;
  background-color: #f5f5f5bb !important;
  border: 1px solid #e3e3e3 !important;
}

.react-datepicker__day--selected, .react-datepicker__day--in-selecting-range, .react-datepicker__day--in-range, .react-datepicker__month-text--selected, .react-datepicker__month-text--in-selecting-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--selected, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--selected, .react-datepicker__year-text--in-selecting-range, .react-datepicker__year-text--in-range {
  background-color: #59b71f !important;
  color: #fff !important;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected {
  background-color: #59b71f !important;
}

.react-datepicker__month-container {
  padding: 10px !important;
}

.react-datepicker__header {
  background: none !important;
}

.react-datepicker__day--keyboard-selected, .react-datepicker__month-text--keyboard-selected, .react-datepicker__quarter-text--keyboard-selected, .react-datepicker__year-text--keyboard-selected {
  border-radius: .3rem;
  background-color: #59b71f !important;
  color: #fff !important;
}

button.react-datepicker__navigation.react-datepicker__navigation--next, button.react-datepicker__navigation.react-datepicker__navigation--previous {
  border: 1px solid #e9e9e9;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 10px !important;
}

button.react-datepicker__navigation.react-datepicker__navigation--next {
  right: 112px !important;
}

button.react-datepicker__navigation.react-datepicker__navigation--previous {
  left: 8px !important;
}

.react-datepicker__navigation-icon {
  top: 0 !important;
  right: 0 !important;
  left: 0 !important;
}

.react-datepicker {
  padding: 4px;
  border: none !important;
  display: flex !important;
  box-shadow: 0 8px 24px #959da533 !important;
  border-radius: 16px !important;
}

.react-datepicker-popper .react-datepicker__triangle {
  stroke: none !important;
}

.react-datepicker__day-names {
  margin-top: 12px !important;
}

.react-datepicker__day-names > .react-datepicker__day-name {
  font-weight: 600;
  color: #3e3e3e;
}

.react-datepicker__time-container {
  width: 100px !important;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box {
  width: 100% !important;
}

@media (width <= 640px) {
  .react-datepicker {
    font-size: 12px !important;
    max-width: 100% !important;
  }

  .react-datepicker__time-container {
    width: 80px !important;
  }

  .react-datepicker__month-container {
    padding: 8px !important;
  }

  .react-datepicker__day-names {
    margin-top: 8px !important;
  }

  .react-datepicker__navigation {
    top: 8px !important;
  }

  button.react-datepicker__navigation.react-datepicker__navigation--next {
    right: 90px !important;
  }
}

@media (width >= 641px) and (width <= 768px) {
  .react-datepicker {
    font-size: 13px !important;
  }

  .react-datepicker__time-container {
    width: 90px !important;
  }
}

/*# sourceMappingURL=src_modules_layout_components_main-nav-content_header_style_b52d8e.css.map*/