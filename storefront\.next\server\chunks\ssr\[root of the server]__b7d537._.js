module.exports = {

"[externals]/ [external] (util, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: require } = __turbopack_context__;
{
const mod = __turbopack_external_require__("util");

module.exports = mod;
}}),
"[project]/src/lib/config.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
__turbopack_esm__({
    "NOMINATIM_API_URL": (()=>NOMINATIM_API_URL),
    "sdk": (()=>sdk)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$medusajs$2f$js$2d$sdk$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/node_modules/@medusajs/js-sdk/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$medusajs$2f$js$2d$sdk$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/node_modules/@medusajs/js-sdk/dist/esm/index.js [app-rsc] (ecmascript) <locals>");
;
// Defaults to standard port for Medusa server
let MEDUSA_BACKEND_URL = "http://localhost:9000";
if ("TURBOPACK compile-time truthy", 1) {
    MEDUSA_BACKEND_URL = ("TURBOPACK compile-time value", "https://efruit-admin.techtown.app");
}
const sdk = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$medusajs$2f$js$2d$sdk$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"]({
    baseUrl: MEDUSA_BACKEND_URL,
    debug: ("TURBOPACK compile-time value", "development") === "development",
    publishableKey: ("TURBOPACK compile-time value", "pk_e49d15aed11aa5cc51358a24933b5c3ad4cad882864a0962fa6e1aee20d2e2fa")
});
const NOMINATIM_API_URL = ("TURBOPACK compile-time value", "https://nominatim.openstreetmap.org") || "https://nominatim.openstreetmap.org";
}}),
"[project]/src/lib/util/medusa-error.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>medusaError)
});
function medusaError(error) {
    if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        const u = new URL(error.config.url, error.config.baseURL);
        console.error("Resource:", u.toString());
        console.error("Response data:", error.response.data);
        console.error("Status code:", error.response.status);
        console.error("Headers:", error.response.headers);
        // Extracting the error message from the response data
        const message = error.response.data.message || error.response.data;
        throw new Error(message.charAt(0).toUpperCase() + message.slice(1) + ".");
    } else if (error.request) {
        // The request was made but no response was received
        throw new Error("No response received: " + error.request);
    } else {
        // Something happened in setting up the request that triggered an Error
        throw new Error("Error setting up the request: " + error.message);
    }
}
}}),
"[project]/src/lib/data/cookies.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ {"7f1c3bfe267d9b3dd53c23206cdae5258a786ebbac":"setLocale","7f374201c7571ced7da4ab5ba6ab8137e3811159bf":"getCacheOptions","7f3748ea72472448226aec7aa6faf8d5efd5db68ec":"setCartId","7f420c193c4ac9c50a8d6e0f75baf2cf5130abba67":"removeAuthToken","7f5460f23be5a16a9ed77016580d638d16ef89f92d":"getLocale","7f719735819616d397fc3e2a03a112f7496ee7f242":"getAuthHeaders","7f8012f3e256fb910110ff6b93098a50b9eb858a26":"removeCartId","7f9ed15196bf9d14d20e78560c17d260c58cf9de3b":"getCacheTag","7fc8d5c8ee663383605bd9ae61c90483fd7e5ae9bc":"getCartId","7feb2eb5935c5e47d861cce2e499a0032c890d0db2":"setAuthToken"} */ __turbopack_esm__({
    "getAuthHeaders": (()=>getAuthHeaders),
    "getCacheOptions": (()=>getCacheOptions),
    "getCacheTag": (()=>getCacheTag),
    "getCartId": (()=>getCartId),
    "getLocale": (()=>getLocale),
    "removeAuthToken": (()=>removeAuthToken),
    "removeCartId": (()=>removeCartId),
    "setAuthToken": (()=>setAuthToken),
    "setCartId": (()=>setCartId),
    "setLocale": (()=>setLocale)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/headers.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
const getAuthHeaders = async ()=>{
    const nextCookies = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
    const token = nextCookies.get("_medusa_jwt")?.value;
    if (token) {
        return {
            authorization: `Bearer ${token}`
        };
    }
    return {};
};
const getCacheTag = async (tag)=>{
    try {
        const nextCookies = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
        const cacheId = nextCookies.get("_medusa_cache_id")?.value;
        if (!cacheId) {
            return "";
        }
        return `${tag}-${cacheId}`;
    } catch (error) {
        return "";
    }
};
const getCacheOptions = async (tag)=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    const cacheTag = await getCacheTag(tag);
    if (!cacheTag) {
        return {};
    }
    return {
        tags: [
            `${cacheTag}`
        ]
    };
};
const setAuthToken = async (token)=>{
    const nextCookies = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
    nextCookies.set("_medusa_jwt", token, {
        maxAge: 60 * 60 * 24 * 7,
        httpOnly: true,
        sameSite: "strict",
        secure: ("TURBOPACK compile-time value", "development") === "production"
    });
};
const removeAuthToken = async ()=>{
    const nextCookies = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
    nextCookies.set("_medusa_jwt", "", {
        maxAge: -1
    });
};
const getCartId = async ()=>{
    const nextCookies = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
    return nextCookies.get("_medusa_cart_id")?.value;
};
const setCartId = async (cartId)=>{
    const nextCookies = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
    nextCookies.set("_medusa_cart_id", cartId, {
        maxAge: 60 * 60 * 24 * 7,
        httpOnly: true,
        sameSite: "strict",
        secure: ("TURBOPACK compile-time value", "development") === "production"
    });
};
const removeCartId = async ()=>{
    const nextCookies = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
    nextCookies.set("_medusa_cart_id", "", {
        maxAge: -1
    });
};
const getLocale = async (key)=>{
    return (await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])()).get(key)?.value;
};
const setLocale = async ({ key, value, maxAge })=>{
    return (await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])()).set(key, value, {
        maxAge: maxAge || 60 * 60 * 24 * 7
    });
};
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getAuthHeaders,
    getCacheTag,
    getCacheOptions,
    setAuthToken,
    removeAuthToken,
    getCartId,
    setCartId,
    removeCartId,
    getLocale,
    setLocale
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getAuthHeaders, "7f719735819616d397fc3e2a03a112f7496ee7f242", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getCacheTag, "7f9ed15196bf9d14d20e78560c17d260c58cf9de3b", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getCacheOptions, "7f374201c7571ced7da4ab5ba6ab8137e3811159bf", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(setAuthToken, "7feb2eb5935c5e47d861cce2e499a0032c890d0db2", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(removeAuthToken, "7f420c193c4ac9c50a8d6e0f75baf2cf5130abba67", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getCartId, "7fc8d5c8ee663383605bd9ae61c90483fd7e5ae9bc", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(setCartId, "7f3748ea72472448226aec7aa6faf8d5efd5db68ec", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(removeCartId, "7f8012f3e256fb910110ff6b93098a50b9eb858a26", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getLocale, "7f5460f23be5a16a9ed77016580d638d16ef89f92d", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(setLocale, "7f1c3bfe267d9b3dd53c23206cdae5258a786ebbac", null);
}}),
"[project]/src/lib/util/sort-products.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
__turbopack_esm__({
    "sortProductVariant": (()=>sortProductVariant),
    "sortProductVariantImage": (()=>sortProductVariantImage),
    "sortProducts": (()=>sortProducts)
});
function sortProducts(products, sortBy) {
    let sortedProducts = products;
    if ([
        "price_asc",
        "price_desc"
    ].includes(sortBy)) {
        // Precompute the minimum price for each product
        sortedProducts.forEach((product)=>{
            if (product.variants && product.variants.length > 0) {
                product._minPrice = Math.min(...product.variants.map((variant)=>variant?.calculated_price?.calculated_amount || 0));
            } else {
                product._minPrice = Infinity;
            }
        });
        // Sort products based on the precomputed minimum prices
        sortedProducts.sort((a, b)=>{
            const diff = a._minPrice - b._minPrice;
            return sortBy === "price_asc" ? diff : -diff;
        });
    }
    if (sortBy === "created_at") {
        sortedProducts.sort((a, b)=>{
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });
    }
    if (sortBy === "featured") {
        sortedProducts.sort((a, b)=>{
            const aHasFeatured = a.variants?.some((variant)=>variant.metadata?.is_featured) || false;
            const bHasFeatured = b.variants?.some((variant)=>variant.metadata?.is_featured) || false;
            if (aHasFeatured && !bHasFeatured) return -1;
            if (!aHasFeatured && bHasFeatured) return 1;
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });
    }
    return sortedProducts;
}
const sortProductVariant = (product)=>{
    const sortedVariants = product.map((product)=>{
        const sortedVariants = getSortedFeaturedVariants(product.variants);
        return {
            ...product,
            variants: sortedVariants
        };
    });
    return sortedVariants;
};
const getSortedFeaturedVariants = (variants)=>{
    const notFeaturedVariants = [];
    const featuredVariants = variants?.filter((variant)=>{
        if (variant.metadata?.is_featured) {
            return true;
        }
        notFeaturedVariants.push(variant);
        return false;
    }) || [];
    const sortedFeaturedVariants = [
        ...featuredVariants,
        ...notFeaturedVariants
    ];
    return sortedFeaturedVariants.sort((a, b)=>Number(a.metadata?.rank ?? 0) - Number(b.metadata?.rank ?? 0)).map((variant)=>({
            ...variant,
            product_variant_images: variant.product_variant_images?.sort((a, b)=>Number(a.image_rank ?? 0) - Number(b.image_rank ?? 0)) || []
        }));
};
const sortProductVariantImage = ({ arraySort })=>{
    if (!arraySort) return [];
    return arraySort.sort((a, b)=>Number(a?.image_rank ?? 0) - Number(b?.image_rank ?? 0));
};
}}),
"[project]/src/lib/data/regions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ {"7f47d20f64b79082f86c05969ea3f2c65ea297a1b9":"listRegions","7f4bb40c409e8b383fba73aa2d40b8f4fcc6d8941b":"getRegion","7fee8f36f215429faddffa0f8bc9ec323f7af36cbc":"retrieveRegion"} */ __turbopack_esm__({
    "getRegion": (()=>getRegion),
    "listRegions": (()=>listRegions),
    "retrieveRegion": (()=>retrieveRegion)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/config.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$util$2f$medusa$2d$error$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/util/medusa-error.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/data/cookies.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
const listRegions = async ()=>{
    const next = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheOptions"])("regions")
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].client.fetch(`/store/regions`, {
        method: "GET",
        next
    }).then(({ regions })=>regions).catch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$util$2f$medusa$2d$error$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"]);
};
const retrieveRegion = async (id)=>{
    const next = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheOptions"])([
            "regions",
            id
        ].join("-"))
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].client.fetch(`/store/regions/${id}`, {
        method: "GET",
        next
    }).then(({ region })=>region).catch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$util$2f$medusa$2d$error$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"]);
};
const regionMap = new Map();
const getRegion = async (countryCode)=>{
    try {
        if (regionMap.has(countryCode.toLowerCase())) {
            return regionMap.get(countryCode.toLowerCase());
        }
        const regions = await listRegions();
        if (!regions || regions.length === 0) {
            console.log("No regions found");
            return null;
        }
        regions.forEach((region)=>{
            if (region.countries && region.countries.length > 0) {
                region.countries.forEach((c)=>{
                    regionMap.set(c?.iso_2?.toLowerCase() ?? "", region);
                });
            } else {
                regionMap.set(countryCode.toLowerCase(), region);
            }
        });
        const region = countryCode ? regionMap.get(countryCode.toLowerCase()) : regionMap.get("us");
        return region;
    } catch (e) {
        console.error("Error in getRegion:", e.message);
        return null;
    }
};
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    listRegions,
    retrieveRegion,
    getRegion
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(listRegions, "7f47d20f64b79082f86c05969ea3f2c65ea297a1b9", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(retrieveRegion, "7fee8f36f215429faddffa0f8bc9ec323f7af36cbc", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getRegion, "7f4bb40c409e8b383fba73aa2d40b8f4fcc6d8941b", null);
}}),
"[project]/src/lib/data/products.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ {"40eb8cd793372ac0550094e6d2310a38509eedcb9e":"getProductsById","7f1838bce94e5f39a2ad16af2d13fb08bf0e2954c3":"getMaterialProductByHandle","7f8b870986fc2ba3425e23a18495a75b3c3b784c34":"listProductsWithSort","7fb1f359112a8beef68c2239b7c7a17e897a11b6cf":"listProducts","7fedb0e5bf288b593dbdfbbe38ca1004bfe0ba4715":"getProductByHandle","7ff4a7b3934d17eff652259affe92e8e010f643969":"getProductList"} */ __turbopack_esm__({
    "getMaterialProductByHandle": (()=>getMaterialProductByHandle),
    "getProductByHandle": (()=>getProductByHandle),
    "getProductList": (()=>getProductList),
    "getProductsById": (()=>getProductsById),
    "listProducts": (()=>listProducts),
    "listProductsWithSort": (()=>listProductsWithSort)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/config.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$util$2f$sort$2d$products$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/util/sort-products.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/data/cookies.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$regions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/data/regions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
const queryParamsDefault = [
    "*variants.calculated_price",
    "*variants.product_variant_images"
];
async function getProductsById({ ids, regionId, fields }) {
    // Validate inputs
    if (!ids?.length || !regionId) {
        return [];
    }
    const BATCH_SIZE = 50;
    const MAX_RETRIES = 2;
    const results = [];
    // Prepare headers and cache options
    const headers = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAuthHeaders"])();
    const cacheOptions = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheOptions"])("products");
    const fieldsString = fields ? [
        ...queryParamsDefault,
        ...fields
    ].join(", ") : queryParamsDefault.join(", ");
    // Split IDs into batches
    const batches = [];
    for(let i = 0; i < ids.length; i += BATCH_SIZE){
        batches.push(ids.slice(i, i + BATCH_SIZE));
    }
    async function fetchBatch(batchIds, attempt = 1) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].client.fetch(`/store/products`, {
                method: "GET",
                query: {
                    id: batchIds,
                    region_id: regionId,
                    fields: fieldsString
                },
                headers,
                next: {
                    ...cacheOptions,
                    tags: [
                        "products"
                    ]
                }
            });
            return response.products || [];
        } catch (error) {
            if (attempt < MAX_RETRIES) {
                await new Promise((resolve)=>setTimeout(resolve, 100 * attempt ** 2));
                return fetchBatch(batchIds, attempt + 1);
            }
            return [];
        }
    }
    try {
        const batchResults = await Promise.all(batches.map((batchIds)=>fetchBatch(batchIds)));
        results.push(...batchResults.flat());
        return results;
    } catch (error) {
        return [];
    }
}
const getProductByHandle = async ({ handle, regionId, fields })=>{
    const headers = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAuthHeaders"])()
    };
    const next = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheOptions"])("products")
    };
    const fieldsString = fields ? [
        ...queryParamsDefault,
        ...fields
    ].join(", ") : queryParamsDefault.join(", ");
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].client.fetch(`/store/products`, {
        method: "GET",
        query: {
            handle,
            region_id: regionId,
            fields: fieldsString
        },
        headers,
        next
    }).then(({ products })=>{
        // Sort product variants
        const sortedProducts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$util$2f$sort$2d$products$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sortProductVariant"])(products);
        return sortedProducts[0];
    });
};
const listProducts = async ({ pageParam = 1, queryParams, countryCode, queryString })=>{
    const limit = queryParams?.limit || 12;
    const _pageParam = Math.max(pageParam, 1);
    const offset = (_pageParam - 1) * limit;
    const region = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$regions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getRegion"])(countryCode);
    if (!region) {
        return {
            response: {
                products: [],
                count: 0
            },
            nextPage: null
        };
    }
    const headers = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAuthHeaders"])()
    };
    const next = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheOptions"])("products")
    };
    const arrayQueryString = queryString?.length ? [
        ...queryParamsDefault,
        ...queryString
    ] : queryParamsDefault;
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].client.fetch(`/store/products`, {
        method: "GET",
        query: {
            limit,
            offset,
            region_id: region.id,
            fields: arrayQueryString.join(","),
            ...queryParams
        },
        headers,
        next
    }).then(({ products, count })=>{
        const nextPage = count > offset + limit ? pageParam + 1 : null;
        // Sort product variants
        const sortedProducts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$util$2f$sort$2d$products$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sortProductVariant"])(products);
        return {
            response: {
                products: sortedProducts,
                count
            },
            nextPage: nextPage,
            queryParams
        };
    });
};
const listProductsWithSort = async ({ page = 0, queryParams, sortBy = "created_at", countryCode, queryString })=>{
    const limit = queryParams?.limit || 9;
    const { response: { products, count } } = await listProducts({
        pageParam: page,
        queryParams: {
            ...queryParams,
            limit,
            offset: (page - 1) * limit
        },
        countryCode,
        queryString
    });
    const sortVariant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$util$2f$sort$2d$products$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sortProductVariant"])(products);
    const sortedProducts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$util$2f$sort$2d$products$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sortProducts"])(sortVariant, sortBy);
    const pageParam = (page - 1) * limit;
    const nextPage = count > pageParam + limit ? pageParam + limit : null;
    return {
        response: {
            products: sortedProducts,
            count
        },
        nextPage,
        queryParams
    };
};
const getMaterialProductByHandle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async function(handle) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].client.fetch(`/store/custom/material/${handle}`, {
        method: "GET",
        headers: {
            next: {
                tags: [
                    "material-by-handle"
                ]
            }
        }
    });
});
const getProductList = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async function({ pageParam = 1, queryParams, countryCode }) {
    const page = Math.max(1, pageParam || 1);
    const limit = queryParams?.limit || 12;
    const offset = (page - 1) * limit;
    const region = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$regions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getRegion"])(countryCode);
    if (!region) {
        return {
            response: {
                products: [],
                count: 0
            },
            nextPage: null
        };
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].store.product.list({
        limit,
        offset,
        region_id: region.id,
        fields: "*variants.calculated_price",
        ...queryParams
    }, {
        next: {
            tags: [
                "products"
            ]
        }
    }).then(({ products, count })=>{
        const nextPage = count > offset + limit ? page + 1 : null;
        return {
            response: {
                products,
                count
            },
            nextPage: nextPage,
            queryParams
        };
    });
});
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getProductsById,
    getProductByHandle,
    listProducts,
    listProductsWithSort,
    getMaterialProductByHandle,
    getProductList
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getProductsById, "40eb8cd793372ac0550094e6d2310a38509eedcb9e", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getProductByHandle, "7fedb0e5bf288b593dbdfbbe38ca1004bfe0ba4715", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(listProducts, "7fb1f359112a8beef68c2239b7c7a17e897a11b6cf", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(listProductsWithSort, "7f8b870986fc2ba3425e23a18495a75b3c3b784c34", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getMaterialProductByHandle, "7f1838bce94e5f39a2ad16af2d13fb08bf0e2954c3", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getProductList, "7ff4a7b3934d17eff652259affe92e8e010f643969", null);
}}),
"[project]/src/lib/data/cart.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ {"003abac2f9949eafbadcd3de2083f4c8b45b7d64ad":"placeOrder","00f29a66e7fecaa55f04418daf00e74e1a26807e99":"retrieveCart","402103384a200fec4ca2ef2f3421b390996baa1e7f":"deleteLineItem","4047d7f250e35763b4095c5266c279b6ded5a79b0c":"setShippingMethod","404f984293a8f60e4e588e26a29a413c275c495a17":"updateLineItem","405bc0d3eb68aed6c4ed2118cd86dfb4d3160443e9":"addToCart","406502743290dc6ce56278e714fca36bb72f1186a7":"removeDiscount","409dd3001228ea962c81f1b694b6c073200c94d647":"updateCart","40c01b9e426a71f9774d1a00cf70acd03f3c197173":"getOrSetCart","40d6ec036f86a3ed15851f3bc9c171aca0c62ffbff":"applyPromotions","40f7fab38fca5bb649d23ef192c8c37cbd646657af":"applyGiftCard","60299119670ad665638d5affabbaba036ce144879e":"setAddresses","603156a0d7acb0f91d49ab41605149cdfba1961c09":"updateRegion","60643cb7d7146f660764117092ce54fd45c46e1701":"initiatePaymentSession","608aee495cc85a58ceaafbdc298ba7aeed029799ac":"enrichLineItems","60976a8fd5fa235c6840213fb05627259c82480d66":"submitPromotionForm","609f822710f0ddfe57ab41b973b4308a2e2800f5f8":"removeGiftCard"} */ __turbopack_esm__({
    "addToCart": (()=>addToCart),
    "applyGiftCard": (()=>applyGiftCard),
    "applyPromotions": (()=>applyPromotions),
    "deleteLineItem": (()=>deleteLineItem),
    "enrichLineItems": (()=>enrichLineItems),
    "getOrSetCart": (()=>getOrSetCart),
    "initiatePaymentSession": (()=>initiatePaymentSession),
    "placeOrder": (()=>placeOrder),
    "removeDiscount": (()=>removeDiscount),
    "removeGiftCard": (()=>removeGiftCard),
    "retrieveCart": (()=>retrieveCart),
    "setAddresses": (()=>setAddresses),
    "setShippingMethod": (()=>setShippingMethod),
    "submitPromotionForm": (()=>submitPromotionForm),
    "updateCart": (()=>updateCart),
    "updateLineItem": (()=>updateLineItem),
    "updateRegion": (()=>updateRegion)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/config.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$util$2f$medusa$2d$error$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/util/medusa-error.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2f$omit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/lodash/omit.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/cache.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/data/cookies.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$products$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/data/products.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$regions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/data/regions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
const query = [
    "items.product.variants.product_variant_images.*",
    "items.product.variants.*",
    "items.product.variants.inventory.location_levels.*",
    "*shipping_address",
    "+shipping_address.metadata"
].join(",");
// Cache for cart to avoid redundant API calls
let cachedCart = null;
let cartCacheTime = 0;
const CART_CACHE_TTL = 30000 // 30 seconds
;
async function retrieveCart() {
    const cartId = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCartId"])();
    if (!cartId) {
        return null;
    }
    const headers = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAuthHeaders"])()
    };
    const next = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheOptions"])("carts")
    };
    return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].client.fetch(`/store/carts/${cartId}`, {
        method: "GET",
        query: {
            fields: query
        },
        headers,
        next
    }).then(({ cart })=>{
        return cart;
    }).catch(()=>{
        return null;
    });
}
async function getOrSetCart(countryCode) {
    // Return cached cart if still valid
    if (cachedCart && Date.now() - cartCacheTime < CART_CACHE_TTL) {
        return cachedCart;
    }
    let cart = await retrieveCart();
    const region = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$regions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getRegion"])(countryCode);
    if (!region) {
        throw new Error(`Region not found for country code: ${countryCode}`);
    }
    const headers = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAuthHeaders"])()
    };
    if (!cart) {
        const cartResp = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].store.cart.create({
            region_id: region.id
        }, {}, headers);
        cart = cartResp.cart;
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["setCartId"])(cart.id);
        const cartCacheTag = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheTag"])("carts");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(cartCacheTag);
    }
    if (cart && cart?.region_id !== region.id) {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].store.cart.update(cart.id, {
            region_id: region.id
        }, {}, headers);
        const cartCacheTag = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheTag"])("carts");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(cartCacheTag);
    }
    // Cache the cart
    cachedCart = cart;
    cartCacheTime = Date.now();
    return cart;
}
async function updateCart(data) {
    const cartId = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCartId"])();
    if (!cartId) {
        throw new Error("No existing cart found, please create one before updating");
    }
    const headers = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAuthHeaders"])()
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].store.cart.update(cartId, data, {}, headers).then(async ({ cart })=>{
        const cartCacheTag = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheTag"])("carts");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(cartCacheTag);
        return cart;
    }).catch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$util$2f$medusa$2d$error$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"]);
}
async function addToCart({ variantId, quantity, countryCode, metadata }) {
    if (!variantId) {
        throw new Error("Missing variant ID when adding to cart");
    }
    const cart = await getOrSetCart(countryCode);
    if (!cart) {
        throw new Error("Error retrieving or creating cart");
    }
    const headers = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAuthHeaders"])()
    };
    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].store.cart.createLineItem(cart.id, {
        variant_id: variantId,
        quantity,
        metadata
    }, {}, headers).then(async ()=>{
        // Invalidate cart cache
        cachedCart = null;
        cartCacheTime = 0;
        const cartCacheTag = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheTag"])("carts");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(cartCacheTag);
    }).catch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$util$2f$medusa$2d$error$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"]);
}
async function updateLineItem({ lineId, quantity }) {
    if (!lineId) {
        throw new Error("Missing lineItem ID when updating line item");
    }
    const cartId = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCartId"])();
    if (!cartId) {
        throw new Error("Missing cart ID when updating line item");
    }
    const headers = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAuthHeaders"])()
    };
    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].store.cart.updateLineItem(cartId, lineId, {
        quantity
    }, {}, headers).then(async ()=>{
        const cartCacheTag = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheTag"])("carts");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(cartCacheTag);
    }).catch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$util$2f$medusa$2d$error$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"]);
}
async function deleteLineItem(lineId) {
    if (!lineId) {
        throw new Error("Missing lineItem ID when deleting line item");
    }
    const cartId = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCartId"])();
    if (!cartId) {
        throw new Error("Missing cart ID when deleting line item");
    }
    const headers = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAuthHeaders"])()
    };
    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].store.cart.deleteLineItem(cartId, lineId, headers).then(async ()=>{
        const cartCacheTag = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheTag"])("carts");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(cartCacheTag);
    }).catch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$util$2f$medusa$2d$error$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"]);
}
async function enrichLineItems(lineItems, regionId) {
    if (!lineItems) return [];
    // Prepare query parameters
    const queryParams = {
        ids: lineItems.map((lineItem)=>lineItem.product_id),
        regionId: regionId
    };
    // Fetch products by their IDs
    const products = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$products$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getProductsById"])(queryParams);
    // If there are no line items or products, return an empty array
    if (!lineItems?.length || !products) {
        return [];
    }
    // Enrich line items with product and variant information
    const enrichedItems = lineItems.map((item)=>{
        const product = products.find((p)=>p.id === item.product_id);
        const variant = product?.variants?.find((v)=>v.id === item.variant_id);
        // If product or variant is not found, return the original item
        if (!product || !variant) {
            return item;
        }
        // If product and variant are found, enrich the item
        return {
            ...item,
            variant: {
                ...variant,
                product: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2f$omit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"])(product, "variants")
            }
        };
    });
    return enrichedItems;
}
async function setShippingMethod({ cartId, shippingMethodId }) {
    const headers = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAuthHeaders"])()
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].store.cart.addShippingMethod(cartId, {
        option_id: shippingMethodId
    }, {}, headers).then(async (result)=>{
        // Invalidate cart cache immediately
        cachedCart = null;
        cartCacheTime = 0;
        const cartCacheTag = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheTag"])("carts");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(cartCacheTag);
        return result;
    }).catch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$util$2f$medusa$2d$error$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"]);
}
async function initiatePaymentSession(cart, data) {
    const headers = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAuthHeaders"])()
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].store.payment.initiatePaymentSession(cart, data, {}, headers).then(async (resp)=>{
        const cartCacheTag = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheTag"])("carts");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(cartCacheTag);
        return resp;
    }).catch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$util$2f$medusa$2d$error$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"]);
}
async function applyPromotions(codes) {
    const cartId = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCartId"])();
    if (!cartId) {
        throw new Error("No existing cart found");
    }
    const headers = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAuthHeaders"])()
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].store.cart.update(cartId, {
        promo_codes: codes
    }, {}, headers).then(async (result)=>{
        const cartCacheTag = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheTag"])("carts");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(cartCacheTag);
        return result;
    }).catch((error)=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$util$2f$medusa$2d$error$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"])(error);
    });
}
async function applyGiftCard(code) {
//   const cartId = getCartId()
//   if (!cartId) return "No cartId cookie found"
//   try {
//     await updateCart(cartId, { gift_cards: [{ code }] }).then(() => {
//       revalidateTag("cart")
//     })
//   } catch (error: any) {
//     throw error
//   }
}
async function removeDiscount(code) {
// const cartId = getCartId()
// if (!cartId) return "No cartId cookie found"
// try {
//   await deleteDiscount(cartId, code)
//   revalidateTag("cart")
// } catch (error: any) {
//   throw error
// }
}
async function removeGiftCard(codeToRemove, giftCards) {
//   const cartId = getCartId()
//   if (!cartId) return "No cartId cookie found"
//   try {
//     await updateCart(cartId, {
//       gift_cards: [...giftCards]
//         .filter((gc) => gc.code !== codeToRemove)
//         .map((gc) => ({ code: gc.code })),
//     }).then(() => {
//       revalidateTag("cart")
//     })
//   } catch (error: any) {
//     throw error
//   }
}
async function submitPromotionForm(currentState, formData) {
    const code = formData.get("code");
    try {
        await applyPromotions([
            code
        ]);
    } catch (e) {
        return e.message;
    }
}
async function setAddresses(currentState, formData) {
    try {
        if (!formData) {
            throw new Error("No form data found when setting addresses");
        }
        const cartId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCartId"])();
        if (!cartId) {
            throw new Error("No existing cart found when setting addresses");
        }
        const data = {
            shipping_address: {
                first_name: formData.get("shipping_address.first_name"),
                last_name: formData.get("shipping_address.last_name"),
                address_1: formData.get("shipping_address.address_1"),
                address_2: "",
                company: formData.get("shipping_address.company") || "",
                province: formData.get("shipping_address.province"),
                country_code: formData.get("shipping_address.country_code"),
                phone: formData.get("shipping_address.phone"),
                metadata: {
                    is_generate_invoice: formData.get("shipping_address.is_generate_invoice"),
                    district: formData.get("shipping_address.metadata.district"),
                    ward: formData.get("shipping_address.metadata.ward"),
                    company_name: formData.get("shipping_address.company_name"),
                    delivery_note: formData.get("shipping_address.metadata.delivery_note"),
                    company_tax_code: formData.get("shipping_address.company_tax_code"),
                    company_address: formData.get("shipping_address.company_address")
                }
            },
            email: formData.get("email")
        };
        const sameAsBilling = formData.get("same_as_billing");
        if (sameAsBilling === "on") data.billing_address = data.shipping_address;
        if (sameAsBilling !== "on") {
            data.billing_address = {
                first_name: formData.get("billing_address.first_name"),
                last_name: formData.get("billing_address.last_name"),
                address_1: formData.get("billing_address.address_1"),
                address_2: "",
                company: formData.get("billing_address.company") || "",
                postal_code: formData.get("billing_address.postal_code") || "",
                city: formData.get("billing_address.city"),
                province: formData.get("billing_address.province"),
                country_code: formData.get("billing_address.country_code"),
                phone: formData.get("billing_address.phone"),
                metadata: {
                    is_generate_invoice: formData.get("shipping_address.is_generate_invoice"),
                    district: formData.get("shipping_address.metadata.district"),
                    ward: formData.get("shipping_address.metadata.ward"),
                    company_name: formData.get("shipping_address.company_name"),
                    company_tax_code: formData.get("shipping_address.company_tax_code"),
                    company_address: formData.get("shipping_address.company_address")
                }
            };
        }
        await updateCart(data);
        return true;
    } catch (e) {
        return e.message;
    }
}
async function placeOrder() {
    const cartId = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCartId"])();
    if (!cartId) {
        throw new Error("No existing cart found when placing an order");
    }
    const headers = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAuthHeaders"])()
    };
    try {
        const cartRes = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].store.cart.complete(cartId, {}, headers);
        const cartCacheTag = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheTag"])("carts");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(cartCacheTag);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["removeCartId"])();
        return {
            isSuccess: true,
            data: cartRes
        };
    } catch (error) {
        return {
            isSuccess: false,
            error: error
        };
    }
}
async function updateRegion(countryCode, currentPath) {
    const cartId = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCartId"])();
    const region = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$regions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getRegion"])(countryCode);
    if (!region) {
        throw new Error(`Region not found for country code: ${countryCode}`);
    }
    if (cartId) {
        await updateCart({
            region_id: region.id
        });
        const cartCacheTag = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheTag"])("carts");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(cartCacheTag);
    }
    const regionCacheTag = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheTag"])("regions");
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(regionCacheTag);
    const productsCacheTag = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheTag"])("products");
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(productsCacheTag);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])(`/${countryCode}${currentPath}`);
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    retrieveCart,
    getOrSetCart,
    updateCart,
    addToCart,
    updateLineItem,
    deleteLineItem,
    enrichLineItems,
    setShippingMethod,
    initiatePaymentSession,
    applyPromotions,
    applyGiftCard,
    removeDiscount,
    removeGiftCard,
    submitPromotionForm,
    setAddresses,
    placeOrder,
    updateRegion
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(retrieveCart, "00f29a66e7fecaa55f04418daf00e74e1a26807e99", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getOrSetCart, "40c01b9e426a71f9774d1a00cf70acd03f3c197173", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updateCart, "409dd3001228ea962c81f1b694b6c073200c94d647", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(addToCart, "405bc0d3eb68aed6c4ed2118cd86dfb4d3160443e9", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updateLineItem, "404f984293a8f60e4e588e26a29a413c275c495a17", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(deleteLineItem, "402103384a200fec4ca2ef2f3421b390996baa1e7f", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(enrichLineItems, "608aee495cc85a58ceaafbdc298ba7aeed029799ac", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(setShippingMethod, "4047d7f250e35763b4095c5266c279b6ded5a79b0c", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(initiatePaymentSession, "60643cb7d7146f660764117092ce54fd45c46e1701", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(applyPromotions, "40d6ec036f86a3ed15851f3bc9c171aca0c62ffbff", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(applyGiftCard, "40f7fab38fca5bb649d23ef192c8c37cbd646657af", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(removeDiscount, "406502743290dc6ce56278e714fca36bb72f1186a7", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(removeGiftCard, "609f822710f0ddfe57ab41b973b4308a2e2800f5f8", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(submitPromotionForm, "60976a8fd5fa235c6840213fb05627259c82480d66", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(setAddresses, "60299119670ad665638d5affabbaba036ce144879e", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(placeOrder, "003abac2f9949eafbadcd3de2083f4c8b45b7d64ad", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updateRegion, "603156a0d7acb0f91d49ab41605149cdfba1961c09", null);
}}),
"[project]/src/lib/data/customer.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ {"00d2a9beeaf3ea0c8e03bbefb5c91ebc4db229771b":"transferCart","6046214a31a89da81720c63fa8761ffdfff35b21d3":"signup","607e6ea6ae6ba4c9d99bb0657abde969d5eea30d9e":"login","60a18acd1cbb3e63083276b97262e9abe4a8ace3ec":"signout","7f14a7ccdef050f54c633babcb886df8eac5e7c7c9":"updateCustomerAddress","7f3734a14846199f910a175e23c964c7b2fda404d1":"updateCustomer","7f42af98b42bc623e20bfafe682bf4a2c6603cdf6d":"addCustomerAddress","7f4f47e91c492bd8d10c91446f7fa7386b86b4f27e":"deleteCustomerAddress","7fc387bab5b1da571c498156cecd93e246120c42fe":"retrieveCustomer","7fec1d3651369c5b1207c8062d4e1628670c2233f1":"requestPasswordReset"} */ __turbopack_esm__({
    "addCustomerAddress": (()=>addCustomerAddress),
    "deleteCustomerAddress": (()=>deleteCustomerAddress),
    "login": (()=>login),
    "requestPasswordReset": (()=>requestPasswordReset),
    "retrieveCustomer": (()=>retrieveCustomer),
    "signout": (()=>signout),
    "signup": (()=>signup),
    "transferCart": (()=>transferCart),
    "updateCustomer": (()=>updateCustomer),
    "updateCustomerAddress": (()=>updateCustomerAddress)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/cache.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/config.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$util$2f$medusa$2d$error$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/util/medusa-error.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/data/cookies.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
const retrieveCustomer = async ()=>{
    const headers = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAuthHeaders"])()
    };
    const next = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheOptions"])("customers")
    };
    return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].client.fetch(`/store/customers/me`, {
        method: "GET",
        query: {
            fields: "*orders"
        },
        headers,
        next
    }).then(({ customer })=>customer).catch(()=>null);
};
const updateCustomer = async (body)=>{
    const headers = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAuthHeaders"])()
    };
    const updateRes = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].store.customer.update(body, {}, headers).then(({ customer })=>customer).catch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$util$2f$medusa$2d$error$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"]);
    const cacheTag = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheTag"])("customers");
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(cacheTag);
    return updateRes;
};
async function signup(_currentState, formData) {
    const password = formData.get("password");
    const customerForm = {
        email: formData.get("email"),
        first_name: formData.get("first_name"),
        last_name: formData.get("last_name"),
        phone: formData.get("phone")
    };
    try {
        const token = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].auth.register("customer", "emailpass", {
            email: customerForm.email,
            password: password
        });
        const headers = {
            ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAuthHeaders"])(),
            Authorization: `Bearer ${token}`
        };
        const { customer: createdCustomer } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].store.customer.create(customerForm, {}, headers);
        const loginToken = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].auth.login("customer", "emailpass", {
            email: customerForm.email,
            password
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["setAuthToken"])(loginToken);
        const customerCacheTag = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheTag"])("customers");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(customerCacheTag);
        await transferCart();
        return {
            isSuccess: true,
            data: createdCustomer
        };
    } catch (error) {
        if (error?.status === 401) {
            return {
                isSuccess: false,
                error: {
                    message: "Error: Identity with email already exists",
                    errorStatus: error?.status
                }
            };
        } else {
            return {
                isSuccess: false,
                error: {
                    message: "Error: Something went wrong",
                    errorStatus: error?.status
                }
            };
        }
    }
}
async function login(_currentState, formData) {
    const email = formData.get("email");
    const password = formData.get("password");
    try {
        const token = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].auth.login("customer", "emailpass", {
            email,
            password
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["setAuthToken"])(token);
        const customerCacheTag = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheTag"])("customers");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(customerCacheTag);
        await transferCart();
        return {
            isSuccess: true,
            error: null,
            data: {
                token: token
            }
        };
    } catch (error) {
        if (error?.status === 401) {
            return {
                isSuccess: false,
                error: {
                    message: "Error: Invalid email or password",
                    errorStatus: error?.status
                }
            };
        } else {
            return {
                isSuccess: false,
                error: {
                    message: "Error: Something went wrong",
                    errorStatus: error?.status
                }
            };
        }
    }
}
async function signout(countryCode, localeLanguage) {
    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].auth.logout();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["removeAuthToken"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])("auth");
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])("customer");
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])(`/${countryCode}/${localeLanguage}/account`);
}
async function transferCart() {
    const cartId = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCartId"])();
    if (!cartId) {
        return;
    }
    const headers = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAuthHeaders"])();
    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].store.cart.transferCart(cartId, {}, headers);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])("cart");
}
const addCustomerAddress = async (_currentState, formData)=>{
    const address = {
        first_name: formData.first_name,
        last_name: formData.last_name,
        company: formData.company,
        address_1: formData.address_1,
        address_2: formData.address_2,
        city: formData.city,
        postal_code: formData.postal_code,
        province: formData.province,
        country_code: formData.country_code,
        phone: formData.phone
    };
    const headers = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAuthHeaders"])()
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].store.customer.createAddress(address, {}, headers).then(async ({ customer })=>{
        const customerCacheTag = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheTag"])("customers");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(customerCacheTag);
        return {
            success: true,
            error: null,
            data: customer
        };
    }).catch((err)=>{
        return {
            success: false,
            error: err.toString()
        };
    });
};
const requestPasswordReset = async (email)=>{
    if (!email) {
        return;
    }
    // return await sdk.client
    //   .fetch(`/auth/customer/emailpass/reset-password`, {
    //     method: "POST",
    //     body: JSON.stringify({ identifier: email }),
    //   })
    // .then(() => {
    //   console.log("🚀 ~ requestPasswordReset ~ email:", email)
    //   return { success: true, error: null }
    // })
    // .catch((err) => {
    //   console.log("🚀 ~ requestPasswordReset ~ err.toString():", err.toString())
    //   return { success: false, error: err.toString() }
    // })
    return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].auth.resetPassword("customer", "emailpass", {
        identifier: email
    }).then(()=>{
        return {
            success: true,
            error: null
        };
    }).catch((err)=>{
        return {
            success: false,
            error: err.toString()
        };
    });
};
const deleteCustomerAddress = async (addressId)=>{
    const headers = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAuthHeaders"])()
    };
    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].store.customer.deleteAddress(addressId, headers).then(async ()=>{
        const customerCacheTag = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheTag"])("customers");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(customerCacheTag);
        return {
            success: true,
            error: null
        };
    }).catch((err)=>{
        return {
            success: false,
            error: err.toString()
        };
    });
};
const updateCustomerAddress = async (currentState, formData)=>{
    const addressId = currentState.addressId;
    const address = {
        first_name: formData.first_name,
        last_name: formData.last_name,
        company: formData.company,
        address_1: formData.address_1,
        address_2: formData.address_2,
        city: formData.city,
        postal_code: formData.postal_code,
        province: formData.province,
        country_code: formData.country_code,
        phone: formData.phone
    };
    const headers = {
        ...await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAuthHeaders"])()
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].store.customer.updateAddress(addressId, address, {}, headers).then(async ()=>{
        const customerCacheTag = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cookies$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCacheTag"])("customers");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(customerCacheTag);
        return {
            success: true,
            error: null
        };
    }).catch((err)=>{
        return {
            success: false,
            error: err.toString()
        };
    });
};
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    retrieveCustomer,
    updateCustomer,
    signup,
    login,
    signout,
    transferCart,
    addCustomerAddress,
    requestPasswordReset,
    deleteCustomerAddress,
    updateCustomerAddress
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(retrieveCustomer, "7fc387bab5b1da571c498156cecd93e246120c42fe", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updateCustomer, "7f3734a14846199f910a175e23c964c7b2fda404d1", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(signup, "6046214a31a89da81720c63fa8761ffdfff35b21d3", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(login, "607e6ea6ae6ba4c9d99bb0657abde969d5eea30d9e", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(signout, "60a18acd1cbb3e63083276b97262e9abe4a8ace3ec", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(transferCart, "00d2a9beeaf3ea0c8e03bbefb5c91ebc4db229771b", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(addCustomerAddress, "7f42af98b42bc623e20bfafe682bf4a2c6603cdf6d", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(requestPasswordReset, "7fec1d3651369c5b1207c8062d4e1628670c2233f1", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(deleteCustomerAddress, "7f4f47e91c492bd8d10c91446f7fa7386b86b4f27e", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updateCustomerAddress, "7f14a7ccdef050f54c633babcb886df8eac5e7c7c9", null);
}}),
"[project]/src/modules/layout/components/cart-mismatch-banner/index.tsx (client proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/modules/layout/components/cart-mismatch-banner/index.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/modules/layout/components/cart-mismatch-banner/index.tsx <module evaluation>", "default");
}}),
"[project]/src/modules/layout/components/cart-mismatch-banner/index.tsx (client proxy)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/modules/layout/components/cart-mismatch-banner/index.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/modules/layout/components/cart-mismatch-banner/index.tsx", "default");
}}),
"[project]/src/modules/layout/components/cart-mismatch-banner/index.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$modules$2f$layout$2f$components$2f$cart$2d$mismatch$2d$banner$2f$index$2e$tsx__$28$client__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/modules/layout/components/cart-mismatch-banner/index.tsx (client proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$modules$2f$layout$2f$components$2f$cart$2d$mismatch$2d$banner$2f$index$2e$tsx__$28$client__proxy$29$__ = __turbopack_import__("[project]/src/modules/layout/components/cart-mismatch-banner/index.tsx (client proxy)");
;
__turbopack_export_namespace__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$modules$2f$layout$2f$components$2f$cart$2d$mismatch$2d$banner$2f$index$2e$tsx__$28$client__proxy$29$__);
}}),
"[project]/src/lib/data/menu.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
__turbopack_esm__({
    "getMenuByHandle": (()=>getMenuByHandle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/config.ts [app-rsc] (ecmascript)");
;
;
const getMenuByHandle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async function(handle) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].client.fetch(`/store/menus/${handle}`, {
        query: {},
        headers: {
            next: {
                tags: [
                    "menu-by-handle"
                ]
            }
        }
    });
});
}}),
"[project]/src/lib/data/setting.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
__turbopack_esm__({
    "getSettingsPreferences": (()=>getSettingsPreferences)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/config.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js [app-rsc] (ecmascript)");
;
;
const getSettingsPreferences = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async function() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sdk"].client.fetch("/store/custom/preferences", {
        query: {},
        headers: {
            next: {
                tags: [
                    "settings-preferences"
                ]
            }
        }
    }).then((res)=>res.preferences);
});
}}),
"[project]/src/utils/constant.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
__turbopack_esm__({
    "CHECKOUT_STEP": (()=>CHECKOUT_STEP),
    "DEFAULT_LANGUAGE": (()=>DEFAULT_LANGUAGE),
    "DEFAULT_REGION": (()=>DEFAULT_REGION),
    "GOOGLE_MAP_IFRAME": (()=>GOOGLE_MAP_IFRAME),
    "HANDLE_MENU": (()=>HANDLE_MENU),
    "REGEX_PHONE": (()=>REGEX_PHONE),
    "STORE_INFORMATION": (()=>STORE_INFORMATION)
});
const STORE_INFORMATION = {
    // name: "Efruit",
    company_name: "footer.company_name",
    company_location: "footer.company_location",
    business_registration_number: "footer.business_license_value",
    phone_hotline_value: "footer.phone_hotline_value",
    email_address_value: "footer.email_address_value",
    phone_sales: "footer.phone_sales",
    food_safety_certificate_value: "footer.food_safety_certificate_value"
};
const GOOGLE_MAP_IFRAME = "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3766.088966315491!2d106.66466057804575!3d10.797367561899268!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3175292eb990d699%3A0x85834f5e740a9367!2zMzA1LzkgTmd1eeG7hW4gVHLhu41uZyBUdXnhu4NuLCBQaMaw4budbmcgMTAsIFBow7ogTmh14bqtbiwgSOG7kyBDaMOtIE1pbmgsIFZp4buHdCBOYW0!5e1!3m2!1svi!2s!4v1751202074734!5m2!1svi!2s";
const HANDLE_MENU = {
    MAIN_MENU: "main-menu",
    FOOTER_MENU: "footer-menu",
    LEFT_MENU: "left-menu",
    RIGHT_MENU: "right-menu"
};
const CHECKOUT_STEP = {
    ADDRESS: "address",
    SHIPPING: "shipping",
    PAYMENT: "payment",
    REVIEW: "review"
};
const DEFAULT_REGION = ("TURBOPACK compile-time value", "vn") || "vn";
const DEFAULT_LANGUAGE = ("TURBOPACK compile-time value", "vi") || "vi";
const REGEX_PHONE = /^(\+\d{1,2}\s?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/;
}}),
"[project]/src/modules/layout/components/footer-content/index.tsx (client proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/modules/layout/components/footer-content/index.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/modules/layout/components/footer-content/index.tsx <module evaluation>", "default");
}}),
"[project]/src/modules/layout/components/footer-content/index.tsx (client proxy)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/modules/layout/components/footer-content/index.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/modules/layout/components/footer-content/index.tsx", "default");
}}),
"[project]/src/modules/layout/components/footer-content/index.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$modules$2f$layout$2f$components$2f$footer$2d$content$2f$index$2e$tsx__$28$client__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/modules/layout/components/footer-content/index.tsx (client proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$modules$2f$layout$2f$components$2f$footer$2d$content$2f$index$2e$tsx__$28$client__proxy$29$__ = __turbopack_import__("[project]/src/modules/layout/components/footer-content/index.tsx (client proxy)");
;
__turbopack_export_namespace__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$modules$2f$layout$2f$components$2f$footer$2d$content$2f$index$2e$tsx__$28$client__proxy$29$__);
}}),
"[project]/src/modules/layout/templates/footer/index.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$menu$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/data/menu.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$setting$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/data/setting.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$constant$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/utils/constant.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$modules$2f$layout$2f$components$2f$footer$2d$content$2f$index$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/modules/layout/components/footer-content/index.tsx [app-rsc] (ecmascript)");
;
;
;
;
;
const Footer = async ()=>{
    const settings = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$setting$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSettingsPreferences"])();
    const dataFooterMenu = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$menu$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getMenuByHandle"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$constant$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["HANDLE_MENU"].FOOTER_MENU);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$modules$2f$layout$2f$components$2f$footer$2d$content$2f$index$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
        settings: settings,
        dataFooterMenu: dataFooterMenu
    }, void 0, false, {
        fileName: "[project]/src/modules/layout/templates/footer/index.tsx",
        lineNumber: 13,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = Footer;
}}),
"[project]/src/app/[countryCode]/[localeLanguage]/(main)/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: require } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>ButtonNavigateLayout),
    "metadata": (()=>metadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cart$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/data/cart.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$customer$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/data/customer.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$util$2f$env$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/util/env.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$modules$2f$layout$2f$components$2f$cart$2d$mismatch$2d$banner$2f$index$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/modules/layout/components/cart-mismatch-banner/index.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$modules$2f$layout$2f$templates$2f$footer$2f$index$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/modules/layout/templates/footer/index.tsx [app-rsc] (ecmascript)");
;
;
;
;
;
;
const metadata = {
    metadataBase: new URL((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$util$2f$env$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBaseURL"])())
};
async function ButtonNavigateLayout(props) {
    const customer = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$customer$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["retrieveCustomer"])();
    const cart = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2f$cart$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["retrieveCart"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Fragment"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex min-h-screen flex-col",
            children: [
                customer && cart && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$modules$2f$layout$2f$components$2f$cart$2d$mismatch$2d$banner$2f$index$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                    customer: customer,
                    cart: cart
                }, void 0, false, {
                    fileName: "[project]/src/app/[countryCode]/[localeLanguage]/(main)/layout.tsx",
                    lineNumber: 24,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                    className: "flex-1",
                    children: props.children
                }, void 0, false, {
                    fileName: "[project]/src/app/[countryCode]/[localeLanguage]/(main)/layout.tsx",
                    lineNumber: 26,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$modules$2f$layout$2f$templates$2f$footer$2f$index$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/[countryCode]/[localeLanguage]/(main)/layout.tsx",
                    lineNumber: 27,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/[countryCode]/[localeLanguage]/(main)/layout.tsx",
            lineNumber: 22,
            columnNumber: 7
        }, this)
    }, void 0, false);
}
}}),
"[project]/src/app/[countryCode]/[localeLanguage]/(main)/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: require } = __turbopack_context__;
{
__turbopack_export_namespace__(__turbopack_import__("[project]/src/app/[countryCode]/[localeLanguage]/(main)/layout.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__b7d537._.js.map