{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/lib/util/env.ts"], "sourcesContent": ["export const getBaseURL = () => {\r\n  return process.env.NEXT_PUBLIC_BASE_URL || \"https://localhost:8000\"\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,aAAa;IACxB,OAAO,6DAAoC;AAC7C"}}, {"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/ui/floating-support.tsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/floating-support.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/floating-support.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0S,GACvU,wEACA"}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/ui/floating-support.tsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/floating-support.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/floating-support.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsR,GACnT,oDACA"}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/ui/scroll-top.tsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/scroll-top.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/scroll-top.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA"}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/components/ui/scroll-top.tsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/scroll-top.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/scroll-top.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA"}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/src/app/layout.tsx"], "sourcesContent": ["import \"yet-another-react-lightbox/plugins/thumbnails.css\"\r\nimport \"yet-another-react-lightbox/styles.css\"\r\nimport \"../styles/globals.css\"\r\n\r\nimport \"swiper/css\"\r\nimport \"swiper/css/navigation\"\r\nimport \"swiper/css/pagination\"\r\nimport \"swiper/css/scrollbar\"\r\nimport \"swiper/swiper-bundle.css\"\r\n\r\nimport { getBaseURL } from \"@lib/util/env\"\r\nimport FloatingSupport from \"components/ui/floating-support\"\r\nimport ScrollToTopButton from \"components/ui/scroll-top\"\r\nimport { Metadata } from \"next\"\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"eFruit - Fresh Fruits & Premium Quality\",\r\n  description:\r\n    \"Fresh fruits and premium quality products delivered to your door. Experience the best selection of seasonal fruits with guaranteed freshness.\",\r\n  icons: [],\r\n  metadataBase: new URL(getBaseURL()),\r\n}\r\n\r\ntype Props = {\r\n  children: React.ReactNode\r\n}\r\nexport default async function RootLayout(props: Props) {\r\n  return (\r\n    <html data-mode=\"light\" className=\"antialiased\">\r\n      <head>\r\n        <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\r\n        <link\r\n          rel=\"preconnect\"\r\n          href=\"https://fonts.gstatic.com\"\r\n          crossOrigin=\"anonymous\"\r\n        />\r\n        <link\r\n          href=\"https://fonts.googleapis.com/css2?family=Mulish:ital,wght@0,200..1000;1,200..1000&display=swap\"\r\n          rel=\"stylesheet\"\r\n        />\r\n      </head>\r\n      <body>\r\n        <main className=\"relative\">{props.children}</main>\r\n        <ScrollToTopButton />\r\n        <FloatingSupport />\r\n        {/* <FollowCursor /> */}\r\n      </body>\r\n    </html>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAUA;AACA;AACA;;;;;;;;;;;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aACE;IACF,OAAO,EAAE;IACT,cAAc,IAAI,IAAI,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD;AACjC;AAKe,eAAe,WAAW,KAAY;IACnD,qBACE,8OAAC;QAAK,aAAU;QAAQ,WAAU;;0BAChC,8OAAC;;kCACC,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBACC,KAAI;wBACJ,MAAK;wBACL,aAAY;;;;;;kCAEd,8OAAC;wBACC,MAAK;wBACL,KAAI;;;;;;;;;;;;0BAGR,8OAAC;;kCACC,8OAAC;wBAAK,WAAU;kCAAY,MAAM,QAAQ;;;;;;kCAC1C,8OAAC,yIAAA,CAAA,UAAiB;;;;;kCAClB,8OAAC,+IAAA,CAAA,UAAe;;;;;;;;;;;;;;;;;AAKxB"}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/wao-tech/benntod/storefront/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}