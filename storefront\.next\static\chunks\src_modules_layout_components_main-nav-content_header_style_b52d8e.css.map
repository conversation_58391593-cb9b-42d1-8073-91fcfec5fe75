{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/src/modules/layout/components/main-nav-content/header/style.css"], "sourcesContent": [".react-datepicker__input-container {\r\n  background-color: #f5f5f5bb !important;\r\n  border: solid 1px #e3e3e3 !important;\r\n  border-radius: 8px;\r\n}\r\n\r\n.react-datepicker__day--selected,\r\n.react-datepicker__day--in-selecting-range,\r\n.react-datepicker__day--in-range,\r\n.react-datepicker__month-text--selected,\r\n.react-datepicker__month-text--in-selecting-range,\r\n.react-datepicker__month-text--in-range,\r\n.react-datepicker__quarter-text--selected,\r\n.react-datepicker__quarter-text--in-selecting-range,\r\n.react-datepicker__quarter-text--in-range,\r\n.react-datepicker__year-text--selected,\r\n.react-datepicker__year-text--in-selecting-range,\r\n.react-datepicker__year-text--in-range {\r\n  background-color: #59b71f !important;\r\n  color: white !important;\r\n}\r\n\r\n.react-datepicker__time-container\r\n  .react-datepicker__time\r\n  .react-datepicker__time-box\r\n  ul.react-datepicker__time-list\r\n  li.react-datepicker__time-list-item--selected {\r\n  background-color: #59b71f !important;\r\n}\r\n\r\n.react-datepicker__month-container {\r\n  padding: 10px !important;\r\n}\r\n\r\n.react-datepicker__header {\r\n  background: none !important;\r\n}\r\n\r\n.react-datepicker__day--keyboard-selected,\r\n.react-datepicker__month-text--keyboard-selected,\r\n.react-datepicker__quarter-text--keyboard-selected,\r\n.react-datepicker__year-text--keyboard-selected {\r\n  border-radius: 0.3rem;\r\n  background-color: #59b71f !important;\r\n  color: white !important;\r\n}\r\n\r\nbutton.react-datepicker__navigation.react-datepicker__navigation--next,\r\nbutton.react-datepicker__navigation.react-datepicker__navigation--previous {\r\n  top: 10px !important;\r\n  border: solid 1px #e9e9e9;\r\n  border-radius: 9999px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\nbutton.react-datepicker__navigation.react-datepicker__navigation--next {\r\n  right: 112px !important;\r\n}\r\nbutton.react-datepicker__navigation.react-datepicker__navigation--previous {\r\n  left: 8px !important;\r\n}\r\n.react-datepicker__navigation-icon {\r\n  top: 0 !important;\r\n  right: 0 !important;\r\n  left: 0 !important;\r\n}\r\n\r\n.react-datepicker {\r\n  border: none !important;\r\n  padding: 4px;\r\n  display: flex !important;\r\n  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px !important;\r\n  border-radius: 16px !important;\r\n}\r\n\r\n.react-datepicker-popper .react-datepicker__triangle {\r\n  stroke: none !important;\r\n}\r\n\r\n.react-datepicker__day-names {\r\n  margin-top: 12px !important;\r\n}\r\n\r\n.react-datepicker__day-names > .react-datepicker__day-name {\r\n  font-weight: 600;\r\n  color: #3e3e3e;\r\n}\r\n\r\n.react-datepicker__time-container {\r\n  width: 100px !important;\r\n}\r\n\r\n.react-datepicker__time-container\r\n  .react-datepicker__time\r\n  .react-datepicker__time-box {\r\n  width: 100% !important;\r\n}\r\n\r\n/* Responsive adjustments for mobile */\r\n@media (max-width: 640px) {\r\n  .react-datepicker {\r\n    font-size: 12px !important;\r\n    max-width: 100% !important;\r\n  }\r\n\r\n  .react-datepicker__time-container {\r\n    width: 80px !important;\r\n  }\r\n\r\n  .react-datepicker__month-container {\r\n    padding: 8px !important;\r\n  }\r\n\r\n  .react-datepicker__day-names {\r\n    margin-top: 8px !important;\r\n  }\r\n\r\n  .react-datepicker__navigation {\r\n    top: 8px !important;\r\n  }\r\n\r\n  button.react-datepicker__navigation.react-datepicker__navigation--next {\r\n    right: 90px !important;\r\n  }\r\n}\r\n\r\n/* Tablet adjustments */\r\n@media (min-width: 641px) and (max-width: 768px) {\r\n  .react-datepicker {\r\n    font-size: 13px !important;\r\n  }\r\n\r\n  .react-datepicker__time-container {\r\n    width: 90px !important;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;AAMA;;;;;AAgBA;;;;AAQA;;;;AAIA;;;;AAIA;;;;;;AASA;;;;;;;;;AAUA;;;;AAGA;;;;AAGA;;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAOA;EACE;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAMF;EACE;;;;EAIA"}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}